{"version": 3, "names": ["TaggedTemplateExpression", "node", "print", "tag", "typeParameters", "quasi", "TemplateElement", "Error", "TemplateLiteral", "quasis", "partRaw", "i", "length", "value", "raw", "token", "expressions", "tokenMap", "findMatching", "_catchUpTo", "loc", "start"], "sources": ["../../src/generators/template-literals.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport type * as t from \"@babel/types\";\n\nexport function TaggedTemplateExpression(\n  this: Printer,\n  node: t.TaggedTemplateExpression,\n) {\n  this.print(node.tag);\n  this.print(node.typeParameters); // TS\n  this.print(node.quasi);\n}\n\nexport function TemplateElement(this: Printer) {\n  throw new Error(\"TemplateElement printing is handled in TemplateLiteral\");\n}\n\nexport function TemplateLiteral(this: Printer, node: t.TemplateLiteral) {\n  const quasis = node.quasis;\n\n  let partRaw = \"`\";\n\n  for (let i = 0; i < quasis.length; i++) {\n    partRaw += quasis[i].value.raw;\n\n    if (i + 1 < quasis.length) {\n      this.token(partRaw + \"${\", true);\n      this.print(node.expressions[i]);\n      partRaw = \"}\";\n\n      // In Babel 7 we have indivirual tokens for ${ and }, so the automatic\n      // catchup logic does not work. Manually look for those tokens.\n      if (!process.env.BABEL_8_BREAKING && this.tokenMap) {\n        const token = this.tokenMap.findMatching(node, \"}\", i);\n        if (token) this._catchUpTo(token.loc.start);\n      }\n    }\n  }\n\n  this.token(partRaw + \"`\", true);\n}\n"], "mappings": ";;;;;;;;AAGO,SAASA,wBAAwBA,CAEtCC,IAAgC,EAChC;EACA,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAAC;EACpB,IAAI,CAACD,KAAK,CAACD,IAAI,CAACG,cAAc,CAAC;EAC/B,IAAI,CAACF,KAAK,CAACD,IAAI,CAACI,KAAK,CAAC;AACxB;AAEO,SAASC,eAAeA,CAAA,EAAgB;EAC7C,MAAM,IAAIC,KAAK,CAAC,wDAAwD,CAAC;AAC3E;AAEO,SAASC,eAAeA,CAAgBP,IAAuB,EAAE;EACtE,MAAMQ,MAAM,GAAGR,IAAI,CAACQ,MAAM;EAE1B,IAAIC,OAAO,GAAG,GAAG;EAEjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACtCD,OAAO,IAAID,MAAM,CAACE,CAAC,CAAC,CAACE,KAAK,CAACC,GAAG;IAE9B,IAAIH,CAAC,GAAG,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAE;MACzB,IAAI,CAACG,KAAK,CAACL,OAAO,GAAG,IAAI,EAAE,IAAI,CAAC;MAChC,IAAI,CAACR,KAAK,CAACD,IAAI,CAACe,WAAW,CAACL,CAAC,CAAC,CAAC;MAC/BD,OAAO,GAAG,GAAG;MAIb,IAAqC,IAAI,CAACO,QAAQ,EAAE;QAClD,MAAMF,KAAK,GAAG,IAAI,CAACE,QAAQ,CAACC,YAAY,CAACjB,IAAI,EAAE,GAAG,EAAEU,CAAC,CAAC;QACtD,IAAII,KAAK,EAAE,IAAI,CAACI,UAAU,CAACJ,KAAK,CAACK,GAAG,CAACC,KAAK,CAAC;MAC7C;IACF;EACF;EAEA,IAAI,CAACN,KAAK,CAACL,OAAO,GAAG,GAAG,EAAE,IAAI,CAAC;AACjC", "ignoreList": []}