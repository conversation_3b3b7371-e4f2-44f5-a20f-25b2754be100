const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  quickSave: (data) => ipcRenderer.invoke('quick-save', data),
  autoSave: (data) => ipcRenderer.invoke('auto-save', data),
  saveAs: (data) => ipcRenderer.invoke('save-as', data),
  f5SaveAction: (data) => ipcRenderer.invoke('f5-save-action', data),
  loadBackup: (filepath) => ipcRenderer.invoke('load-backup', filepath),
  listBackups: () => ipcRenderer.invoke('list-backups'),
  archiveHistory: (data) => ipc<PERSON>enderer.invoke('archive-history', data),
  saveHistory: (data) => ipcRenderer.invoke('save-history', data),
  loadHistory: () => ipcRenderer.invoke('load-history'),
  listHistoryArchives: () => ipcRenderer.invoke('list-history-archives'),
  loadHistoryArchive: (filename) => ipc<PERSON><PERSON>er.invoke('load-history-archive', filename),
});