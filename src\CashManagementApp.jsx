import React, {
  useState, useEffect, useMemo, useCallback, useRef, createContext, useContext
} from 'react';
import { produce } from 'immer';

import * as XLSX from 'xlsx';               //  NEW – SheetJS for .xls / .xlsx
import {
  Calendar, Search, TrendingUp, Users, AlertCircle,
  Moon, Sun, ChevronLeft, ChevronRight, Filter, Eye,
  Plus, Trash2, Edit, List, Download, ChevronDown,
  ChevronUp, FileText, Truck, Gift, DollarSign, Upload,
  Undo, Redo, Lock, Unlock, XCircle, Save, FolderOpen,
  RefreshCw,
  PanelTopClose, PanelTopOpen // NEW: Icons for the focus mode button
} from 'lucide-react';
import { parseJSONAsync, stringifyJSONAsync } from './jsonHelper';
// At the top of your CashManagementApp component file, add these imports:
import { performantStateRestore, preventThrottling, keepRendererActive } from './performanceHelper';


/* --------------------------------------------------------------------
   1.  NEW - Critical Application Infrastructure
   ------------------------------------------------------------------ */

// 10. CRITICAL: Update localStorage operations in your React components
// ============================================

// In your React component, add this storage manager:
class StorageManager {
  constructor() {
    this.queue = [];
    this.processing = false;
    this.cache = new Map();
  }

  async save(key, value) {
    try {
      // FIX: Immediately stringify the value to "snapshot" it and remove any Immer proxies.
      // This is the core of the solution.
      const serializedValue = await stringifyJSONAsync(value);

      // We can parse it back for the cache to keep it as an object in memory.
      const cleanValue = await parseJSONAsync(serializedValue);

      // Update cache with the clean, proxy-free value.
      this.cache.set(key, cleanValue);

      // Queue the save operation with the already-serialized string.
      this.queue.push({ key, serializedValue });

      if (!this.processing) {
        this.processQueue();
      }
    } catch (e) {
        // This will catch errors if the value is already a revoked proxy when save() is called.
        console.error(`[StorageManager] Failed to serialize value for key "${key}" during the initial save call.`, e);
    }
  }

  async processQueue() {
    if (this.queue.length === 0) {
      this.processing = false;
      return;
    }

    this.processing = true;
    const batch = this.queue.splice(0, 10); // Process in batches

    const processBatch = () => {
      batch.forEach(({ key, serializedValue }) => {
        try {
          // FIX: Use the pre-serialized value directly. Do NOT stringify again.
          localStorage.setItem(key, serializedValue);
        } catch (e) {
          console.error('Storage error:', e);
          // FIX: Pass the serialized value to the error handler as well.
          this.handleStorageError(key, serializedValue, e);
        }
      });
      
      // Process next batch
      if (this.queue.length > 0) {
          setTimeout(() => this.processQueue(), 0);
      } else {
          this.processing = false;
      }
    };

    if ('requestIdleCallback' in window) {
      requestIdleCallback(processBatch, { timeout: 1000 });
    } else {
      setTimeout(processBatch, 16); // Fallback
    }
  }

  handleStorageError(key, serializedValue, error) {
    if (error.name === 'QuotaExceededError') {
      const keysToCheck = ['cashManagement_changeHistory', 'cashManagement_dailyData'];

      keysToCheck.forEach(k => {
        try {
          const data = localStorage.getItem(k);
          if (data) {
            const parsed = JSON.parse(data);
            if (Array.isArray(parsed) && parsed.length > 100) {
              localStorage.setItem(k, JSON.stringify(parsed.slice(-100)));
            } else if (typeof parsed === 'object' && !Array.isArray(parsed) && parsed !== null) {
              const objKeys = Object.keys(parsed);
              if (objKeys.length > 365) {
                const sorted = objKeys.sort();
                const toKeep = sorted.slice(-365);
                const newData = {};
                toKeep.forEach(k => newData[k] = parsed[k]);
                localStorage.setItem(k, JSON.stringify(newData));
              }
            }
          }
        } catch (e) {
          console.error('Failed to clean storage:', e);
        }
      });

      // Retry save
      try {
        // FIX: Use the serialized value that was passed in.
        localStorage.setItem(key, serializedValue);
      } catch (e) {
        console.error('Failed to save after cleanup:', e);
      }
    }
  }

  load(key, defaultValue) {
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }

    try {
      const stored = localStorage.getItem(key);
      if (stored === null) {
          this.cache.set(key, defaultValue);
          return defaultValue;
      }
      const data = JSON.parse(stored);
      this.cache.set(key, data);
      return data;
    } catch (e) {
      console.error('Error loading from storage:', e);
      this.cache.set(key, defaultValue);
      return defaultValue;
    }
  }
  flushSync() {
    while (this.queue.length) {
      const { key, serializedValue } = this.queue.shift();
      try { localStorage.setItem(key, serializedValue); }
      catch (e) { console.error('[StorageManager] flush failed', e); }
    }
  }
}

// Create singleton instance
const storageManager = new StorageManager();


// ============================================
// 12. Toast notification system (add to your app)
// ============================================
const ToastContext = React.createContext();

export const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const showToast = (message, type = 'info') => {
    const id = Date.now() + Math.random();
    const toast = { id, message, type };

    setToasts(prev => [...prev, toast]);

    const update = (newMessage, newType) => {
      setToasts(prev =>
        prev.map(t => t.id === id ? { ...t, message: newMessage, type: newType } : t)
      );
    };

    const remove = () => {
      setToasts(prev => prev.filter(t => t.id !== id));
    };

    // Auto remove after 3 seconds
    const timer = setTimeout(remove, 3000);

    return { update, remove, timer };
  };

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      <div className="fixed bottom-4 right-4 z-50 space-y-2">
        {toasts.map(toast => (
          <div
            key={toast.id}
            className={`
              px-4 py-2 rounded shadow-lg text-white transition-all
              ${toast.type === 'success' ? 'bg-green-500' : ''}
              ${toast.type === 'error' ? 'bg-red-500' : ''}
              ${toast.type === 'warning' ? 'bg-yellow-500' : ''}
              ${toast.type === 'info' ? 'bg-blue-500' : ''}
            `}
          >
            {toast.message}
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within ToastProvider');
  }
  return context;
};


/* --------------------------------------------------------------------
   2.  Constants  (unchanged – shortened here ONLY for readability)
   ------------------------------------------------------------------ */

// Bulgarian Holidays 2024-2025
const BULGARIAN_HOLIDAYS = {
  '2024': [
    { date: '01-01', name: 'Нова година' },
    { date: '03-03', name: 'Освобождение на България' },
    { date: '04-28', name: 'Великден' },
    { date: '04-29', name: 'Великден' },
    { date: '05-01', name: 'Ден на труда' },
    { date: '05-06', name: 'Гергьовден' },
    { date: '05-24', name: 'Ден на славянската писменост' },
    { date: '09-06', name: 'Ден на Съединението' },
    { date: '09-22', name: 'Ден на независимостта' },
    { date: '12-24', name: 'Бъдни вечер' },
    { date: '12-25', name: 'Коледа' },
    { date: '12-26', name: 'Коледа' }
  ],
  '2025': [
    { date: '01-01', name: 'Нова година' },
    { date: '03-03', name: 'Освобождение на България' },
    { date: '04-20', name: 'Великден' },
    { date: '04-21', name: 'Великден' },
    { date: '05-01', name: 'Ден на труда' },
    { date: '05-06', name: 'Гергьовден' },
    { date: '05-24', name: 'Ден на славянската писменост' },
    { date: '09-06', name: 'Ден на Съединението' },
    { date: '09-22', name: 'Ден на независимостта' },
    { date: '12-24', name: 'Бъдни вечер' },
    { date: '12-25', name: 'Коледа' },
    { date: '12-26', name: 'Коледа' }
  ]
};

// BGN and EUR denominations
const BGN_DENOMINATIONS = [100, 50, 20, 10, 5, 2, 1, 0.50, 0.20, 0.10, 0.05, 0.02, 0.01];
const EUR_DENOMINATIONS = [500, 200, 100, 50, 20, 10, 5];

// Initial Transaction groups
// REQUEST 2: Added 'Заплати' and 'Пренос' to the default groups.
const INITIAL_TRANSACTION_GROUPS = [
  'Аванс заплата',
  'Заплати',
  'Пренос',
  'Пари за банка',
  'Обмяна валута',
  'Ремонти',
  'Доставки',
  'Услуги',
  'Продажби',
  'Други приходи',
  'Други разходи',
  'Наем',
  'Комунални',
  'Такси',
  'Застраховки',
  'Транспорт',
  'Бонус'
];

// Initial Departments with work schedules
const INITIAL_DEPARTMENTS = {
  'Администрация': {
    workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
    halfDays: []
  },
  'Цех': {
    workDays: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'],
    halfDays: ['Friday']
  },
  'Магазин': {
    workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
    halfDays: []
  }
};

// Invoice types
const INVOICE_TYPES = {
  'фактура': 'С фактура',
  'без': 'Без фактура',
  'смесено': 'Смесено'
};

/* --------------------------------------------------------------------
   3.  NEW helpers just for the Excel import
   ------------------------------------------------------------------ */

/** letter → zero‑based column index */
const col = (l) => XLSX.utils.decode_col(l);

/* Excel → denomination index maps */
const BGN_COL_MAP = {
  100: col('D'),  50: col('E'),  20: col('F'),  10: col('G'),
    5: col('H'),   2: col('I'),   1: col('J'), 0.50: col('K'),
 0.20: col('L'), 0.10: col('M'), 0.05: col('N'), 0.02: col('O'),
 0.01: col('P'),
};
const EUR_COL_MAP = {
   500: col('R'), 200: col('S'), 100: col('T'),
    50: col('U'),  20: col('V'),  10: col('W'),  5: col('X'),
};

/** Extract “MM.YY” (or “M.YY”) from filename like “Каса 04.25.xls” */
function parseMonthYear(filename) {
  const m = filename.match(/(\d{1,2})\.(\d{2})/);
  if (!m) return null;
  const month = Number(m[1]);                // 1–12
  const year  = 2000 + Number(m[2]);         // “25” → 2025
  return { month: month - 1, year };         // zero‑based month for JS Date
}

/** Blank transaction row identical to those produced in initialiseDayData */
function blankRow() {
  return {
    id: Math.random(),
    name: '', group: '',
    bgnCounts: {}, eurCounts: {},
    bgnTotal: 0,  eurTotal: 0,
    isCarryOver: false,
  };
}


/* --------------------------------------------------------------------
   4.  All UI components from your original file
       – UNTOUCHED apart from the two places marked “NEW/CHANGED”.
   ------------------------------------------------------------------ */
// Helper function for transliteration search
const normalizeTextForSearch = (text) => {
    const cyrillicToLatinMap = {
        'а':'a', 'б':'b', 'в':'v', 'г':'g', 'д':'d', 'е':'e', 'ж':'zh', 'з':'z', 'и':'i',
        'й':'y', 'к':'k', 'л':'l', 'м':'m', 'н':'n', 'о':'o', 'п':'p', 'р':'r', 'с':'s',
        'т':'t', 'у':'u', 'ф':'f', 'х':'h', 'ц':'ts', 'ч':'ch', 'ш':'sh', 'щ':'sht',
        'ъ':'a', 'ь':'y', 'ю':'yu', 'я':'ya'
    };
    return text.toLowerCase().split('').map(char => cyrillicToLatinMap[char] || char).join('');
};

const flexibleTextSearch = (text, searchText) => {
    if (!text) return false;
    if (!searchText) return true;

    const normalizedText = normalizeTextForSearch(text);
    const normalizedSearchText = normalizeTextForSearch(searchText);

    return normalizedText.includes(normalizedSearchText);
};


// Controlled input component that manages its own state with navigation support
const ControlledInput = ({ value, onChange, onBlur, className, type = "text", blurOnEnter = true, cellId, ...props }) => {
  const [localValue, setLocalValue] = useState(value || '');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef(null);

  useEffect(() => {
    if (!isFocused) {
      setLocalValue(value || '');
    }
  }, [value, isFocused]);

  const navigateToCell = (currentCellId, direction) => {
    if (!currentCellId) return;

    const parts = currentCellId.split('-');
    if (parts.length < 3) return;

    const [section, rowIndex, columnType, columnIndex] = parts;
    const rowIdx = parseInt(rowIndex);
    const colIdx = columnIndex ? parseInt(columnIndex) : null;

    let targetCellId = null;

    if (direction === 'ArrowRight') {
      if (columnType === 'name') {
        // From name column, go to first BGN denomination (100лв)
        targetCellId = `${section}-${rowIndex}-bgn-0`;
      } else if (columnType === 'bgn' && colIdx !== null) {
        // Move within BGN denominations or to EUR
        if (colIdx < 6) { // BGN has 7 denominations (0-6)
          targetCellId = `${section}-${rowIndex}-bgn-${colIdx + 1}`;
        } else {
          // Move to first EUR denomination
          targetCellId = `${section}-${rowIndex}-eur-0`;
        }
      } else if (columnType === 'eur' && colIdx !== null) {
        // Move within EUR denominations
        if (colIdx < 7) { // EUR has 8 denominations (0-7)
          targetCellId = `${section}-${rowIndex}-eur-${colIdx + 1}`;
        }
      }
    } else if (direction === 'ArrowLeft') {
      if (columnType === 'bgn' && colIdx !== null) {
        if (colIdx > 0) {
          targetCellId = `${section}-${rowIndex}-bgn-${colIdx - 1}`;
        } else {
          // Move to name column
          targetCellId = `${section}-${rowIndex}-name`;
        }
      } else if (columnType === 'eur' && colIdx !== null) {
        if (colIdx > 0) {
          targetCellId = `${section}-${rowIndex}-eur-${colIdx - 1}`;
        } else {
          // Move to last BGN denomination
          targetCellId = `${section}-${rowIndex}-bgn-6`;
        }
      }
      // Note: Don't move left from name column (as requested)
    } else if (direction === 'ArrowUp' || direction === 'ArrowDown') {
      const newRowIdx = direction === 'ArrowUp' ? rowIdx - 1 : rowIdx + 1;
      if (newRowIdx >= 0) {
        targetCellId = `${section}-${newRowIdx}-${columnType}${colIdx !== null ? '-' + colIdx : ''}`;
      }
    }

    if (targetCellId) {
      const targetInput = document.querySelector(`[data-cell-id="${targetCellId}"]`);
      if (targetInput) {
        targetInput.focus();
        targetInput.select();
      }
    }
  };

  const handleKeyDown = (e) => {
    // Handle Delete key to clear cell value
    if (e.key === 'Delete') {
      e.preventDefault();
      setLocalValue('');
      onChange('');
      return;
    }

    // Handle arrow key navigation
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
      e.preventDefault();
      navigateToCell(cellId, e.key);
      return;
    }

    if (e.key === 'Enter') {
      e.preventDefault();
      if (blurOnEnter) {
        e.target.blur();
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setLocalValue(value || '');
      e.target.blur();
    }
  };

  const handleBlur = (e) => {
    setIsFocused(false);
    if (localValue !== value) {
      onChange(localValue);
    }
    if (onBlur) {
      onBlur(e);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleChange = (e) => {
    setLocalValue(e.target.value);
  };

  return (
    <input
      ref={inputRef}
      type={type}
      value={localValue}
      onChange={handleChange}
      onBlur={handleBlur}
      onFocus={handleFocus}
      onKeyDown={handleKeyDown}
      className={className}
      data-cell-id={cellId}
      {...props}
    />
  );
};

// 5. FIXED AutocompleteNameInput component with navigation support
// ================================================================
const AutocompleteNameInput = ({
  value,
  onChange,
  className,
  suggestions = [],
  disabled,
  cellId,
  ...props
}) => {
  const [localValue, setLocalValue] = useState(value || '');
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const wrapperRef = useRef(null);
  const inputRef = useRef(null);
  const [isComposing, setIsComposing] = useState(false);

  // FIX: Properly filter suggestions
  const filteredSuggestions = useMemo(() => {
    if (!localValue || !showSuggestions || !Array.isArray(suggestions)) return [];

    return suggestions
      .filter(suggestion => {
        if (!suggestion || typeof suggestion !== 'object' || !suggestion.name) return false;
        return flexibleTextSearch(suggestion.name, localValue) &&
               suggestion.name.toLowerCase() !== localValue.toLowerCase();
      })
      .slice(0, 10);
  }, [localValue, suggestions, showSuggestions]);

  // FIX: Clean up event listeners properly
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setShowSuggestions(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // FIX: Sync with parent value when not focused
  useEffect(() => {
    if (!isFocused && !isComposing) {
      setLocalValue(value || '');
    }
  }, [value, isFocused, isComposing]);

  const navigateToCell = (currentCellId, direction) => {
    if (!currentCellId) return;

    const parts = currentCellId.split('-');
    if (parts.length < 3) return;

    const [section, rowIndex, columnType, columnIndex] = parts;
    const rowIdx = parseInt(rowIndex);
    const colIdx = columnIndex ? parseInt(columnIndex) : null;

    let targetCellId = null;

    if (direction === 'ArrowRight') {
      if (columnType === 'name') {
        // From name column, go to first BGN denomination (100лв)
        targetCellId = `${section}-${rowIndex}-bgn-0`;
      }
    } else if (direction === 'ArrowUp' || direction === 'ArrowDown') {
      const newRowIdx = direction === 'ArrowUp' ? rowIdx - 1 : rowIdx + 1;
      if (newRowIdx >= 0) {
        targetCellId = `${section}-${newRowIdx}-${columnType}${colIdx !== null ? '-' + colIdx : ''}`;
      }
    }

    if (targetCellId) {
      const targetInput = document.querySelector(`[data-cell-id="${targetCellId}"]`);
      if (targetInput) {
        targetInput.focus();
        targetInput.select();
      }
    }
  };

  const handleKeyDown = (e) => {
    if (isComposing) return;

    // Handle Delete key to clear cell value
    if (e.key === 'Delete') {
      e.preventDefault();
      setLocalValue('');
      onChange('');
      return;
    }

    // Handle navigation keys when suggestions are not shown or no suggestions available
    if (!showSuggestions || filteredSuggestions.length === 0) {
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
        e.preventDefault();
        commitValue(); // Save current value before navigating
        navigateToCell(cellId, e.key);
        return;
      }
    }

    switch (e.key) {
      case 'ArrowDown':
        if (showSuggestions && filteredSuggestions.length > 0) {
          e.preventDefault();
          setSelectedIndex(prev =>
            prev < filteredSuggestions.length - 1 ? prev + 1 : prev
          );
        } else {
          e.preventDefault();
          commitValue();
          navigateToCell(cellId, e.key);
        }
        break;
      case 'ArrowUp':
        if (showSuggestions && filteredSuggestions.length > 0) {
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        } else {
          e.preventDefault();
          commitValue();
          navigateToCell(cellId, e.key);
        }
        break;
      case 'ArrowLeft':
      case 'ArrowRight':
        if (!showSuggestions || filteredSuggestions.length === 0) {
          e.preventDefault();
          commitValue();
          navigateToCell(cellId, e.key);
        }
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && filteredSuggestions[selectedIndex]) {
          handleSuggestionClick(filteredSuggestions[selectedIndex].name);
        } else {
          commitValue();
        }
        break;
      case 'Escape':
        e.preventDefault();
        setLocalValue(value || '');
        setShowSuggestions(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
      case 'Tab':
        if (showSuggestions && filteredSuggestions.length > 0) {
          if (selectedIndex >= 0) {
            e.preventDefault();
            handleSuggestionClick(filteredSuggestions[selectedIndex].name);
          }
        }
        break;
    }
  };

  const commitValue = () => {
    if (localValue !== value) {
      onChange(localValue);
    }
    setShowSuggestions(false);
    setSelectedIndex(-1);
  };

  const handleBlur = () => {
    setIsFocused(false);
    // Delay to allow click on suggestion
    setTimeout(() => {
      // Re-check focus state after delay
      const activeElement = document.activeElement;
      if (!wrapperRef.current || !wrapperRef.current.contains(activeElement)) {
        commitValue();
      }
    }, 200);
  };

  const handleFocus = () => {
    setIsFocused(true);
    setShowSuggestions(true);
    setSelectedIndex(-1);
  };

  const handleChange = e => {
  const v = e.target.value;
  setLocalValue(v);
  setShowSuggestions(true);
  setSelectedIndex(-1);
  // NEW: defer parent update until blur/enter
  if (v === '') onChange('');
  };

  const handleSuggestionClick = (suggestionName) => {
    setLocalValue(suggestionName);
    onChange(suggestionName);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // FIX: Handle IME composition
  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = (e) => {
    setIsComposing(false);
    // Manually trigger change for some browsers after composition
    handleChange(e);
  };

  return (
    <div className="relative" ref={wrapperRef}>
      <input
        ref={inputRef}
        type="text"
        autoComplete="off"
        value={localValue}
        onChange={handleChange}
        onBlur={handleBlur}
        onFocus={handleFocus}
        onKeyDown={handleKeyDown}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
        className={className}
        disabled={disabled}
        data-cell-id={cellId}
        {...props}
      />
      {showSuggestions && filteredSuggestions.length > 0 && !disabled && (
        <div
          className="absolute z-50 w-full bg-white border border-gray-300 rounded-md mt-1 max-h-48 overflow-y-auto shadow-lg"
          style={{ minWidth: '200px' }}
        >
          {filteredSuggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.name}-${index}`}
              onMouseDown={(e) => {
                e.preventDefault();
                handleSuggestionClick(suggestion.name);
              }}
              onMouseEnter={() => setSelectedIndex(index)}
              className={`px-3 py-2 cursor-pointer transition-colors ${
                index === selectedIndex
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-900 hover:bg-gray-100'
              }`}
            >
              <div className="font-medium">{suggestion.name}</div>
              {suggestion.group && (
                <div className={`text-xs ${
                  index === selectedIndex ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  {suggestion.group}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};


// NEW Automatic Salary Row Component for DailySheet
const AutomaticSalaryRow = ({
  rowData,
  isDarkMode,
  viewMode,
  columnWidths
}) => {
  if (!rowData) return null;

  const { name, group, bgnTotal, eurTotal, bgnCounts, eurCounts } = rowData;
  const inputClass = `${isDarkMode ? 'bg-gray-700 text-gray-500' : 'bg-gray-200 text-gray-500'} cursor-not-allowed`;

  return (
    <tr
      className={`
        transition-colors duration-300
        ${isDarkMode ? 'bg-gray-700/50' : 'bg-gray-200'}
        border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}
      `}
    >
      <td className="px-2 py-2 text-center text-xs text-gray-500">AUTO</td>
      <td className="px-2 py-2"></td>
      <td className="px-2 py-2">
        <input
          type="text"
          value={group}
          disabled
          className={`w-full px-2 py-1 text-base border rounded ${inputClass}`}
        />
      </td>
      <td className="px-3 py-2">
        <input
          type="text"
          value={name}
          disabled
          className={`w-full px-2 py-1 text-base border rounded ${inputClass}`}
        />
      </td>
      {(viewMode === 'both' || viewMode === 'bgn') && (
        <td className="px-2 py-2 text-center font-semibold">
          {bgnTotal.toFixed(2)} лв
        </td>
      )}
      {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((denom, i) => (
        <td key={i} className="px-2 py-2">
          <input
            type="number"
            value={bgnCounts[i] || ''}
            disabled
            className={`w-full px-1 py-1 text-base text-center border rounded ${inputClass}`}
          />
        </td>
      ))}
      {(viewMode === 'both' || viewMode === 'eur') && (
        <td className="px-2 py-2 text-center font-semibold">
          €{eurTotal.toFixed(2)}
        </td>
      )}
      {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((denom, i) => (
        <td key={i} className="px-2 py-2">
          <input
            type="number"
            value={eurCounts[i] || ''}
            disabled
            className={`w-full px-1 py-1 text-base text-center border rounded ${inputClass}`}
          />
        </td>
      ))}
    </tr>
  );
};

// Horizontal Mini Calendar Component
const MiniCalendar = ({
  isDarkMode,
  currentMonth,
  setCurrentMonth,
  currentYear,
  setCurrentYear,
  selectedDay,
  setSelectedDay,
  daysInMonth,
  monthNames,
  isCurrentMonth,
  isWeekend,
  isHoliday,
  hasScrolledRef
}) => {
  return (
    <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-2 flex flex-col`}>
      <div className="flex justify-between items-center mb-2">
        <button onClick={() => {
          // FIX [1]: When changing the month, reset the scroll flag.
          hasScrolledRef.current = false;
          if (currentMonth === 0) {
            setCurrentMonth(11);
            setCurrentYear(currentYear - 1);
          } else {
            setCurrentMonth(currentMonth - 1);
          }
        }} className={`p-1 ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'} rounded`}>
          <ChevronLeft className="w-5 h-5" />
        </button>
        <h3 className="font-bold text-lg">{monthNames[currentMonth]} {currentYear}</h3>
        <button onClick={() => {
          // FIX [1]: When changing the month, reset the scroll flag.
          hasScrolledRef.current = false;
          if (currentMonth === 11) {
            setCurrentMonth(0);
            setCurrentYear(currentYear + 1);
          } else {
            setCurrentMonth(currentMonth + 1);
          }
        }} className={`p-1 ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'} rounded`}>
          <ChevronRight className="w-5 h-5" />
        </button>
      </div>
      
      {!isCurrentMonth && (
        <div className="bg-red-100 border-l-8 border-red-600 p-4 my-3 rounded-md">
          <div className="flex items-center">
            <AlertCircle className="w-8 h-8 mr-4 text-red-600" />
            <p className="text-xl font-bold text-red-700">Внимание: Не е текущият месец!</p>
          </div>
        </div>
      )}
      
      <div className="overflow-x-auto">
        <div className="flex gap-1">
          {Array.from({ length: daysInMonth }).map((_, i) => {
            const day = i + 1;
            const isSelected = day === selectedDay;
            const weekend = isWeekend(day);
            const holiday = isHoliday(day);
            
            return (
              <button
                key={day}
                onClick={() => {
                    // FIX [1]: When selecting a day, reset the scroll flag.
                    hasScrolledRef.current = false;
                    setSelectedDay(day);
                }}
                className={`flex-shrink-0 w-12 h-12 rounded text-center transition-colors flex items-center justify-center text-base ${
                  isSelected ? 'bg-blue-500 text-white' :
                  holiday ? 'bg-red-100 text-red-700 hover:bg-red-200' :
                  weekend ? `${isDarkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-200 text-gray-600'} hover:bg-gray-300` :
                  isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
                }`}
              >
                {day}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

// Daily Sheet Component - HEAVILY MODIFIED
const DailySheet = ({
  isDarkMode,
  currentYear,
  currentMonth,
  selectedDay,
  dailyData,
  initializeDayData,
  scrollToRowId,
  setScrollToRowId,
  highlightedRowId,
  setHighlightedRowId,
  hasScrolledRef,
  monthNames,
  isWeekend,
  isHoliday,
  salaryCashPayments,
  calculateTotal,
  viewMode,
  frozenHeaders,
  setFrozenHeaders,
  scrollContainerRef,
  columnWidths,
  handleMouseDown,
  nameColumnWidth,
  transactionGroups,
  clientList,
  employeeList,
  handleNameChange,
  deleteRowAt,
  addRowAt,
  updateTransaction,
  // NEW PROPS for resizable panel
  panelHeight,
  setPanelHeight,
  isFocusMode,
  setIsFocusMode,
  // Pass calendar setters for the mini calendar
  setCurrentMonth,
  setCurrentYear,
  setSelectedDay,
  daysInMonth,
  isCurrentMonth,
}) => {
  const key = `${currentYear}-${currentMonth}-${selectedDay}`;
  const date = new Date(currentYear, currentMonth, selectedDay);
  const dayOfWeek = date.getDay();
  const dayData = dailyData[key] || initializeDayData(dayOfWeek);
  const MIN_PANEL_HEIGHT = 55;
  const MAX_PANEL_HEIGHT = 450;
  const panelResizeRef = useRef(null);

  const handlePanelResizeMouseDown = (e) => {
    e.preventDefault();
    panelResizeRef.current = {
      startY: e.clientY,
      startHeight: panelHeight,
    };
    document.addEventListener('mousemove', handlePanelResizeMouseMove);
    document.addEventListener('mouseup', handlePanelResizeMouseUp);
  };

  const handlePanelResizeMouseMove = (e) => {
    if (!panelResizeRef.current) return;
    const { startY, startHeight } = panelResizeRef.current;
    const deltaY = e.clientY - startY;
    const newHeight = startHeight + deltaY;

    if (newHeight < MIN_PANEL_HEIGHT) {
      setIsFocusMode(true);
      // Clean up listeners when transitioning to focus mode
      document.removeEventListener('mousemove', handlePanelResizeMouseMove);
      document.removeEventListener('mouseup', handlePanelResizeMouseUp);
      panelResizeRef.current = null;
    } else {
      setPanelHeight(Math.min(MAX_PANEL_HEIGHT, newHeight));
    }
  };

  const handlePanelResizeMouseUp = () => {
    panelResizeRef.current = null;
    document.removeEventListener('mousemove', handlePanelResizeMouseMove);
    document.removeEventListener('mouseup', handlePanelResizeMouseUp);
  };
  
  useEffect(() => {
      if (!scrollToRowId) return;
      hasScrolledRef.current = true;
      const element = document.getElementById(scrollToRowId);
      
      if (element) {
          setTimeout(() => {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });
              setHighlightedRowId(scrollToRowId);
              
              const highlightTimer = setTimeout(() => {
                  setHighlightedRowId(null);
                  setScrollToRowId(null); 
              }, 2500);
              
              return () => clearTimeout(highlightTimer);
          }, 100);
      } else {
          setScrollToRowId(null);
      }
  }, [scrollToRowId, hasScrolledRef, setHighlightedRowId, setScrollToRowId]);

  const today = new Date();
  const isToday = selectedDay === today.getDate() && currentMonth === today.getMonth() && currentYear === today.getFullYear();
  const prevMonthForSalary = today.getMonth() === 0 ? 11 : today.getMonth() - 1;
  const prevYearForSalary = today.getMonth() === 0 ? today.getFullYear() - 1 : today.getFullYear();

  const salaryExpenseRowData = useMemo(() => {
      const salaryExpenseKey = `${prevYearForSalary}-${prevMonthForSalary}`;
      const totalBgnCounts = {};

      Object.values(salaryCashPayments).forEach(employeeData => {
          const paymentsForPeriod = employeeData[salaryExpenseKey];
          if (paymentsForPeriod) {
              Object.entries(paymentsForPeriod).forEach(([denomIndex, count]) => {
                  if (count > 0) {
                      totalBgnCounts[denomIndex] = (totalBgnCounts[denomIndex] || 0) + count;
                  }
              });
          }
      });

      if (Object.keys(totalBgnCounts).length === 0) return null;

      const bgnTotal = calculateTotal(BGN_DENOMINATIONS, BGN_DENOMINATIONS.map((_, i) => totalBgnCounts[i] || 0));

      return {
          name: `Заплати за ${monthNames[prevMonthForSalary]}`,
          group: 'Заплати',
          bgnTotal: bgnTotal,
          eurTotal: 0,
          bgnCounts: totalBgnCounts,
          eurCounts: {},
      };
  }, [salaryCashPayments, prevYearForSalary, prevMonthForSalary, monthNames, calculateTotal]);


  const totals = useMemo(() => {
    const incomeBGN = dayData.income.reduce((sum, t) => sum + (t?.bgnTotal || 0), 0);
    const incomeEUR = dayData.income.reduce((sum, t) => sum + (t?.eurTotal || 0), 0);
    let expenseBGN = dayData.expense.reduce((sum, t) => sum + (t?.bgnTotal || 0), 0);
    let expenseEUR = dayData.expense.reduce((sum, t) => sum + (t?.eurTotal || 0), 0);

    if (isToday && salaryExpenseRowData) {
      expenseBGN += salaryExpenseRowData.bgnTotal || 0;
    }
    return {
      incomeBGN, incomeEUR, expenseBGN, expenseEUR
    };
  }, [dayData, isToday, salaryExpenseRowData]);

  const balance = {
    bgn: totals.incomeBGN - totals.expenseBGN,
    eur: totals.incomeEUR - totals.expenseEUR
  };

  const denominationCounts = useMemo(() => {
      const counts = {
          income: { bgn: Array(BGN_DENOMINATIONS.length).fill(0), eur: Array(EUR_DENOMINATIONS.length).fill(0) },
          expense: { bgn: Array(BGN_DENOMINATIONS.length).fill(0), eur: Array(EUR_DENOMINATIONS.length).fill(0) },
          balance: { bgn: Array(BGN_DENOMINATIONS.length).fill(0), eur: Array(EUR_DENOMINATIONS.length).fill(0) }
      };

      dayData.income.forEach(transaction => {
          if (transaction) {
              BGN_DENOMINATIONS.forEach((_, i) => {
                  counts.income.bgn[i] += transaction.bgnCounts?.[i] || 0;
              });
              EUR_DENOMINATIONS.forEach((_, i) => {
                  counts.income.eur[i] += transaction.eurCounts?.[i] || 0;
              });
          }
      });

      dayData.expense.forEach(transaction => {
          if (transaction) {
              BGN_DENOMINATIONS.forEach((_, i) => {
                  counts.expense.bgn[i] += transaction.bgnCounts?.[i] || 0;
              });
              EUR_DENOMINATIONS.forEach((_, i) => {
                  counts.expense.eur[i] += transaction.eurCounts?.[i] || 0;
              });
          }
      });
      
      if (isToday && salaryExpenseRowData) {
          BGN_DENOMINATIONS.forEach((_, i) => {
              counts.expense.bgn[i] += salaryExpenseRowData.bgnCounts?.[i] || 0;
          });
      }

      BGN_DENOMINATIONS.forEach((_, i) => {
          counts.balance.bgn[i] = counts.income.bgn[i] - counts.expense.bgn[i];
      });
      EUR_DENOMINATIONS.forEach((_, i) => {
          counts.balance.eur[i] = counts.income.eur[i] - counts.expense.eur[i];
      });

      return counts;
  }, [dayData, isToday, salaryExpenseRowData]);


  return (
    <div className={`${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white'} rounded-lg shadow-md overflow-hidden h-full flex flex-col`}>
      {/* Resizable Top Panel */}
      <div
        style={{ height: panelHeight }}
        className={`relative flex flex-col transition-all duration-150 ease-out overflow-hidden border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}
      >
        <div className="flex-grow p-4 overflow-y-auto">
          {panelHeight > 240 && (
            <div className="mb-4">
               <MiniCalendar
                  isDarkMode={isDarkMode}
                  currentMonth={currentMonth}
                  setCurrentMonth={setCurrentMonth}
                  currentYear={currentYear}
                  setCurrentYear={setCurrentYear}
                  selectedDay={selectedDay}
                  setSelectedDay={setSelectedDay}
                  daysInMonth={daysInMonth}
                  monthNames={monthNames}
                  isCurrentMonth={isCurrentMonth}
                  isWeekend={isWeekend}
                  isHoliday={isHoliday}
                  hasScrolledRef={hasScrolledRef}
                />
            </div>
          )}

          {panelHeight > 150 && (
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-2xl font-bold">
                  {selectedDay} {monthNames[currentMonth]} {currentYear}
                  {isWeekend(selectedDay) && <span className="ml-2 text-orange-500 text-base">(Почивен ден)</span>}
                  {isHoliday(selectedDay) && <span className="ml-2 text-red-500 text-base">(Празник)</span>}
                </h2>
                <div className="flex gap-4 items-center mt-2">
                  {[
                    { label:'Баланс ЛВ', val:balance.bgn, currency:'лв', pos:balance.bgn>=0 },
                    { label:'Баланс EUR', val:balance.eur, currency:'€', pos:balance.eur>=0 },
                    { label:'Приходи',     val:totals.incomeBGN, currency:'лв',  pos:true },
                    { label:'Разходи',     val:totals.expenseBGN, currency:'лв', pos:false },
                  ].map(({label,val,pos, currency}) => (
                    <div key={label} className="text-center">
                      <div className="text-xl font-bold text-grey-600">{label}</div>
                      <div className={`text-lg font-bold ${pos?'text-green-600':'text-red-600'}`}>
                        {currency === '€' ? '€' : ''}{val.toFixed(2)}{currency === 'лв' ? ' лв' : ''}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          
           {/* Sticky Denomination Header */}
           <div className={`w-full overflow-x-auto ${isDarkMode ? 'bg-gray-900' : 'bg-gray-100'} sticky top-0 z-10`}>
            <table className="w-full text-base">
              <thead>
                <tr className="text-sm font-semibold">
                  <th className="px-2 py-2 text-center w-8"></th>
                  <th className="px-2 py-2 w-10"></th>
                  <th className="px-2 py-2 text-left" style={{ width: columnWidths['group'] || 120 }}>
                      <div className="flex items-center justify-between">
                          <span>Група</span>
                          <div className="w-1 h-5"></div>
                      </div>
                  </th>
                  <th className="px-3 py-2 text-left" style={{ width: nameColumnWidth }}>
                      <div className="flex items-center justify-between">
                          <span>Име</span>
                          <div className="w-1 h-5"></div>
                      </div>
                  </th>
                  {(viewMode === 'both' || viewMode === 'bgn') && <th className="px-2 py-2 text-center" style={{ width: columnWidths['incomeTotalBGN'] || 100 }}>
                      <div className="flex items-center justify-center">
                          <span></span>
                          <div className="w-1 h-5 ml-2"></div>
                      </div>
                  </th>}
                  {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((d, i) => (
                    <th key={`header-bgn-${d}`} className="px-2 py-2 text-center" style={{ width: columnWidths[`bgn${i}`] || 60 }}>
                      <div className="flex items-center justify-center">
                          <span className="text-base">{d}лв</span>
                          <div className="w-1 h-5 ml-2"></div>
                      </div>
                    </th>
                  ))}
                  {(viewMode === 'both' || viewMode === 'eur') && <th className="px-2 py-2 text-center" style={{ width: columnWidths['incomeTotalEUR'] || 100 }}>
                      <div className="flex items-center justify-center">
                          <span></span>
                          <div className="w-1 h-5 ml-2"></div>
                      </div>
                  </th>}
                  {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((d, i) => (
                    <th key={`header-eur-${d}`} className="px-2 py-2 text-center" style={{ width: columnWidths[`eur${i}`] || 60 }}>
                      <div className="flex items-center justify-center">
                          <span className="text-base">€{d}</span>
                          <div className="w-1 h-5 ml-2"></div>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
            </table>
          </div>
        </div>
        {/* Panel Resizer Handle */}
        <div
          onMouseDown={handlePanelResizeMouseDown}
          className={`absolute bottom-0 left-0 w-full h-2 bg-gray-300 hover:bg-blue-500 transition-colors cursor-row-resize z-20 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-300'}`}
          title="Оразмери панела"
        ></div>
      </div>


      {/* Main content area with scroll */}
      <div className="flex-1 overflow-auto" ref={scrollContainerRef}>
        {/* Income Section */}
        <div className={`${isDarkMode ? 'bg-green-900/20' : 'bg-green-50'} px-4 py-4`}>
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-xl font-bold text-green-600 px-2">Приходи</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-base">
              <thead className="sticky top-0 z-10">
                <tr className={`${isDarkMode ? 'bg-gray-800' : 'bg-green-100'}`}>
                  <th className="px-2 py-2 text-center w-8">#</th>
                  <th className="px-2 py-2 w-10"></th>
                  <th className="px-2 py-2 text-left" style={{ width: columnWidths['group'] || 120 }}>
                    <div className="flex items-center justify-between">
                      <span>Група</span>
                      <div
                        className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600"
                        onMouseDown={(e) => handleMouseDown(e, 'group')}
                      />
                    </div>
                  </th>
                  <th className="px-3 py-2 text-left" style={{ width: nameColumnWidth }}>
                    <div className="flex items-center justify-between">
                      <span>Име</span>
                      <div
                        className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600"
                        onMouseDown={(e) => handleMouseDown(e, 'name')}
                      />
                    </div>
                  </th>
                  {(viewMode === 'both' || viewMode === 'bgn') && (
                    <th className="px-2 py-2 text-center" style={{ width: columnWidths['incomeTotalBGN'] || 100 }}>
                      <div className="flex items-center justify-center">
                        <span>Приходи</span>
                        <div
                          className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2"
                          onMouseDown={(e) => handleMouseDown(e, 'incomeTotalBGN')}
                        />
                      </div>
                    </th>
                  )}
                  {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((d, i) => (
                    <th key={d} className="px-2 py-2 text-center" style={{ width: columnWidths[`bgn${i}`] || 60 }} data-column={`bgn${i}`}>
                      <div className="flex items-center justify-center">
                        <span className="text-base">{d}лв</span>
                        <div
                          className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2"
                          onMouseDown={(e) => handleMouseDown(e, `bgn${i}`)}
                        />
                      </div>
                    </th>
                  ))}
                  {(viewMode === 'both' || viewMode === 'eur') && (
                    <th className="px-2 py-2 text-center" style={{ width: columnWidths['incomeTotalEUR'] || 100 }}>
                      <div className="flex items-center justify-center">
                        <span>Приходи</span>
                        <div
                          className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2"
                          onMouseDown={(e) => handleMouseDown(e, 'incomeTotalEUR')}
                        />
                      </div>
                    </th>
                  )}
                  {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((d, i) => (
                    <th key={d} className="px-2 py-2 text-center" style={{ width: columnWidths[`eur${i}`] || 60 }}>
                      <div className="flex items-center justify-center">
                        <span className="text-base">€{d}</span>
                        <div
                          className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2"
                          onMouseDown={(e) => handleMouseDown(e, `eur${i}`)}
                        />
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {dayData.income.map((transaction, index) => {
                  if(!transaction) return null;
                  const uniqueKey = `${key}-income-${transaction.id}`;
                  const isHighlighted = highlightedRowId === uniqueKey;
                  const isCarryOver = transaction.isCarryOver;
                  const inputClass = isCarryOver ? `${isDarkMode ? 'bg-gray-700 text-gray-500' : 'bg-gray-200 text-gray-500'} cursor-not-allowed` : `${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'}`;
                  
                  return (
                  <tr 
                    key={transaction.id}
                    id={uniqueKey}
                    className={`
                      transition-colors duration-300
                      ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-green-50'}
                      border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}
                      ${isHighlighted ? (isDarkMode ? 'bg-blue-800' : 'bg-blue-200') : ''}
                    `}
                  >
                    <td className="px-2 py-2 text-center text-xs text-gray-500">{index + 1}</td>
                    <td className="px-2 py-2">
                      <div className="flex gap-1.5">
                        <button
                          onClick={() => deleteRowAt('income', index)}
                          className="text-red-500 hover:text-red-700 text-lg"
                          title="Изтрий ред"
                          disabled={isCarryOver}
                        >
                          -
                        </button>
                        <button
                          onClick={() => addRowAt('income', index)}
                          className="text-green-500 hover:text-green-700 text-lg"
                          title="Добави ред"
                        >
                          +
                        </button>
                      </div>
                    </td>
                    <td className="px-2 py-2">
                      <select
                        value={transaction.group || ''}
                        onChange={(e) => updateTransaction('income', index, 'group', e.target.value)}
                        className={`w-full px-2 py-1 text-base border rounded ${inputClass}`}
                        disabled={isCarryOver}
                      >
                        <option value="">-</option>
                        {transactionGroups.map(group => (
                          <option key={group} value={group}>{group}</option>
                        ))}
                      </select>
                    </td>
                    <td className="px-3 py-2 relative">
                      <AutocompleteNameInput
                        value={transaction.name || ''}
                        onChange={(newName) => handleNameChange('income', index, newName)}
                        className={`w-full px-2 py-1 text-base border rounded ${inputClass}`}
                        suggestions={clientList}
                        disabled={isCarryOver}
                        cellId={`income-${index}-name`}
                      />
                    </td>
                    {(viewMode === 'both' || viewMode === 'bgn') && (
                      <td className="px-2 py-2 text-center font-semibold">
                        {transaction.bgnTotal.toFixed(2)} лв
                      </td>
                    )}
                    {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((denom, i) => (
                      <td key={i} className="px-2 py-2">
                        <ControlledInput
                          type="number"
                          min="0"
                          value={transaction.bgnCounts[i] || ''}
                          onChange={(value) => updateTransaction('income', index, 'bgnCount', ['bgn', i, value])}
                          className={`w-full px-1 py-1 text-base text-center border rounded ${inputClass}`}
                          disabled={isCarryOver}
                          cellId={`income-${index}-bgn-${i}`}
                        />
                      </td>
                    ))}
                    {(viewMode === 'both' || viewMode === 'eur') && (
                      <td className="px-2 py-2 text-center font-semibold">
                        €{transaction.eurTotal.toFixed(2)}
                      </td>
                    )}
                    {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((denom, i) => (
                      <td key={i} className="px-2 py-2">
                        <ControlledInput
                          type="number"
                          min="0"
                          value={transaction.eurCounts[i] || ''}
                          onChange={(value) => updateTransaction('income', index, 'eurCount', ['eur', i, value])}
                          className={`w-full px-1 py-1 text-base text-center border rounded ${inputClass}`}
                          disabled={isCarryOver}
                          cellId={`income-${index}-eur-${i}`}
                        />
                      </td>
                    ))}
                  </tr>
                )})}
                {/* Denomination headers for the total row */}
                <tr className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'} text-sm font-semibold`}>
                  <td colSpan="4" className="px-3 py-1"></td>
                  {(viewMode === 'both' || viewMode === 'bgn') && (
                    <td className="px-2 py-1 text-center"></td>
                  )}
                  {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((d, i) => (
                      <td key={`label-bgn-${i}`} className="px-2 py-1 text-center text-gray-600">
                         {d}лв
                      </td>
                  ))}
                  {(viewMode === 'both' || viewMode === 'eur') && (
                    <td className="px-2 py-1 text-center"></td>
                  )}
                  {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((d, i) => (
                      <td key={`label-eur-${i}`} className="px-2 py-1 text-center text-gray-600">
                          €{d}
                      </td>
                  ))}
                </tr>
                <tr className={`font-bold ${isDarkMode ? 'bg-gray-800' : 'bg-green-200'} ${frozenHeaders.income ? 'sticky top-0 z-20' : ''}`}>
                  <td colSpan="1" className="px-2 py-2">
                    <button
                      onClick={() => setFrozenHeaders({...frozenHeaders, income: !frozenHeaders.income})}
                      className={`p-1 rounded ${frozenHeaders.income ? 'text-blue-600' : 'text-gray-600'} hover:bg-gray-200`}
                      title={frozenHeaders.income ? 'Отключи ред' : 'Заключи ред'}
                    >
                      {frozenHeaders.income ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />}
                    </button>
                  </td>
                  <td colSpan="3" className="px-3 py-2 text-right">Всичко Приходи</td>
                  {(viewMode === 'both' || viewMode === 'bgn') && (
                    <td className="px-2 py-2 text-center">{totals.incomeBGN.toFixed(2)} лв</td>
                  )}
                  {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((d, i) => (
                      <td key={`total-bgn-${i}`} className="px-2 py-2 text-center text-green-700">
                         {denominationCounts.income.bgn[i] > 0 ? denominationCounts.income.bgn[i] : ''}
                      </td>
                  ))}
                  {(viewMode === 'both' || viewMode === 'eur') && (
                    <td className="px-2 py-2 text-center">€{totals.incomeEUR.toFixed(2)}</td>
                  )}
                  {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((d, i) => (
                      <td key={`total-eur-${i}`} className="px-2 py-2 text-center text-green-700">
                          {denominationCounts.income.eur[i] > 0 ? denominationCounts.income.eur[i] : ''}
                      </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Expense Section */}
        <div className={`${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'} px-4 py-4 mt-4`}>
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-xl font-bold text-red-600 px-2">Разходи</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-base">
              <thead className="sticky top-0 z-10">
                <tr className={`${isDarkMode ? 'bg-gray-800' : 'bg-red-100'}`}>
                  <th className="px-2 py-2 text-center w-8">#</th>
                  <th className="px-2 py-2 w-10"></th>
                  <th className="px-2 py-2 text-left" style={{ width: columnWidths['group'] || 120 }}>
                    <div className="flex items-center justify-between">
                      <span>Група</span>
                      <div
                        className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600"
                        onMouseDown={(e) => handleMouseDown(e, 'group')}
                      />
                    </div>
                  </th>
                  <th className="px-3 py-2 text-left" style={{ width: nameColumnWidth }}>
                    <div className="flex items-center justify-between">
                      <span>Име</span>
                      <div
                        className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600"
                        onMouseDown={(e) => handleMouseDown(e, 'name')}
                      />
                    </div>
                  </th>
                  {(viewMode === 'both' || viewMode === 'bgn') && (
                    <th className="px-2 py-2 text-center" style={{ width: columnWidths['expenseTotalBGN'] || 100 }}>
                      <div className="flex items-center justify-center">
                        <span>Разходи</span>
                        <div
                          className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2"
                          onMouseDown={(e) => handleMouseDown(e, 'expenseTotalBGN')}
                        />
                      </div>
                    </th>
                  )}
                  {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((d, i) => (
                    <th key={d} className="px-2 py-2 text-center" style={{ width: columnWidths[`bgn${i}`] || 60 }}>
                      <div className="flex items-center justify-center">
                        <span className="text-base">{d}лв</span>
                        <div
                          className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2"
                          onMouseDown={(e) => handleMouseDown(e, `bgn${i}`)}
                        />
                      </div>
                    </th>
                  ))}
                  {(viewMode === 'both' || viewMode === 'eur') && (
                    <th className="px-2 py-2 text-center" style={{ width: columnWidths['expenseTotalEUR'] || 100 }}>
                      <div className="flex items-center justify-center">
                        <span>Разходи</span>
                        <div
                          className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2"
                          onMouseDown={(e) => handleMouseDown(e, 'expenseTotalEUR')}
                        />
                      </div>
                    </th>
                  )}
                  {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((d, i) => (
                    <th key={d} className="px-2 py-2 text-center" style={{ width: columnWidths[`eur${i}`] || 60 }}>
                      <div className="flex items-center justify-center">
                        <span className="text-base">€{d}</span>
                        <div
                          className="w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2"
                          onMouseDown={(e) => handleMouseDown(e, `eur${i}`)}
                        />
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {isToday && (
                  <AutomaticSalaryRow
                      rowData={salaryExpenseRowData}
                      isDarkMode={isDarkMode}
                      viewMode={viewMode}
                      columnWidths={columnWidths}
                  />
                )}
                {dayData.expense.map((transaction, index) => {
                   if(!transaction) return null;
                   const uniqueKey = `${key}-expense-${transaction.id}`;
                   const isHighlighted = highlightedRowId === uniqueKey;
                   
                   return (
                  <tr 
                    key={transaction.id}
                    id={uniqueKey}
                    className={`
                      transition-colors duration-300
                      ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-red-50'}
                      border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}
                      ${isHighlighted ? (isDarkMode ? 'bg-blue-800' : 'bg-blue-200') : ''}
                    `}
                  >
                    <td className="px-2 py-2 text-center text-xs text-gray-500">{index + 1}</td>
                    <td className="px-2 py-2">
                      <div className="flex gap-1.5">
                        <button
                          onClick={() => deleteRowAt('expense', index)}
                          className="text-red-500 hover:text-red-700 text-lg"
                          title="Изтрий ред"
                        >
                          -
                        </button>
                        <button
                          onClick={() => addRowAt('expense', index)}
                          className="text-green-500 hover:text-green-700 text-lg"
                          title="Добави ред"
                        >
                          +
                        </button>
                      </div>
                    </td>
                    <td className="px-2 py-2">
                      <select
                        value={transaction.group || ''}
                        onChange={(e) => updateTransaction('expense', index, 'group', e.target.value)}
                        className={`w-full px-2 py-1 text-base ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded`}
                      >
                        <option value="">-</option>
                        {transactionGroups.map(group => (
                          <option key={group} value={group}>{group}</option>
                        ))}
                      </select>
                    </td>
                    <td className="px-3 py-2 relative">
                      <AutocompleteNameInput
                        value={transaction.name || ''}
                        onChange={(newName) => handleNameChange('expense', index, newName)}
                        className={`w-full px-2 py-1 text-base ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded`}
                        suggestions={[...clientList, ...employeeList.map(e => ({ name: e.name, group: 'Заплати' }))]}
                        cellId={`expense-${index}-name`}
                      />
                    </td>
                    {(viewMode === 'both' || viewMode === 'bgn') && (
                      <td className="px-2 py-2 text-center font-semibold">
                        {transaction.bgnTotal.toFixed(2)} лв
                      </td>
                    )}
                    {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((denom, i) => (
                      <td key={i} className="px-2 py-2">
                        <ControlledInput
                          type="number"
                          min="0"
                          value={transaction.bgnCounts[i] || ''}
                          onChange={(value) => updateTransaction('expense', index, 'bgnCount', ['bgn', i, value])}
                          className={`w-full px-1 py-1 text-base text-center ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded`}
                          cellId={`expense-${index}-bgn-${i}`}
                        />
                      </td>
                    ))}
                    {(viewMode === 'both' || viewMode === 'eur') && (
                      <td className="px-2 py-2 text-center font-semibold">
                        €{transaction.eurTotal.toFixed(2)}
                      </td>
                    )}
                    {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((denom, i) => (
                      <td key={i} className="px-2 py-2">
                        <ControlledInput
                          type="number"
                          min="0"
                          value={transaction.eurCounts[i] || ''}
                          onChange={(value) => updateTransaction('expense', index, 'eurCount', ['eur', i, value])}
                          className={`w-full px-1 py-1 text-base text-center ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded`}
                          cellId={`expense-${index}-eur-${i}`}
                        />
                      </td>
                    ))}
                  </tr>
                )})}
                {/* Denomination headers for the total row */}
                <tr className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'} text-sm font-semibold`}>
                  <td colSpan="4" className="px-3 py-1"></td>
                  {(viewMode === 'both' || viewMode === 'bgn') && (
                    <td className="px-2 py-1 text-center"></td>
                  )}
                  {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((d, i) => (
                      <td key={`label-bgn-${i}`} className="px-2 py-1 text-center text-gray-600">
                         {d}лв
                      </td>
                  ))}
                  {(viewMode === 'both' || viewMode === 'eur') && (
                    <td className="px-2 py-1 text-center"></td>
                  )}
                  {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((d, i) => (
                      <td key={`label-eur-${i}`} className="px-2 py-1 text-center text-gray-600">
                          €{d}
                      </td>
                  ))}
                </tr>
                <tr className={`font-bold ${isDarkMode ? 'bg-gray-800' : 'bg-red-200'} ${frozenHeaders.expense ? 'sticky top-0 z-20' : ''}`}>
                  <td colSpan="1" className="px-2 py-2">
                    <button
                      onClick={() => setFrozenHeaders({...frozenHeaders, expense: !frozenHeaders.expense})}
                      className={`p-1 rounded ${frozenHeaders.expense ? 'text-blue-600' : 'text-gray-600'} hover:bg-gray-200`}
                      title={frozenHeaders.expense ? 'Отключи ред' : 'Заключи ред'}
                    >
                      {frozenHeaders.expense ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />}
                    </button>
                  </td>
                  <td colSpan="3" className="px-3 py-2 text-right">Всички Разходи</td>
                  {(viewMode === 'both' || viewMode === 'bgn') && (
                    <td className="px-2 py-2 text-center">{totals.expenseBGN.toFixed(2)} лв</td>
                  )}
                  {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((d, i) => (
                      <td key={`total-bgn-${i}`} className="px-2 py-2 text-center text-red-700">
                         {denominationCounts.expense.bgn[i] > 0 ? denominationCounts.expense.bgn[i] : ''}
                      </td>
                  ))}
                  {(viewMode === 'both' || viewMode === 'eur') && (
                    <td className="px-2 py-2 text-center">€{totals.expenseEUR.toFixed(2)}</td>
                  )}
                  {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((d, i) => (
                      <td key={`total-eur-${i}`} className="px-2 py-2 text-center text-red-700">
                          {denominationCounts.expense.eur[i] > 0 ? denominationCounts.expense.eur[i] : ''}
                      </td>
                  ))}
                </tr>
              </tbody>
              <tfoot>
                 {/* Denomination headers for the balance row */}
                  <tr className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'} text-sm font-semibold`}>
                      <td colSpan="4" className="px-3 py-1"></td>
                      {(viewMode === 'both' || viewMode === 'bgn') && (
                        <td className="px-2 py-1 text-center"></td>
                      )}
                      {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((d, i) => (
                          <td key={`label-bgn-${i}`} className="px-2 py-1 text-center text-gray-600">
                             {d}лв
                          </td>
                      ))}
                      {(viewMode === 'both' || viewMode === 'eur') && (
                        <td className="px-2 py-1 text-center"></td>
                      )}
                      {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((d, i) => (
                          <td key={`label-eur-${i}`} className="px-2 py-1 text-center text-gray-600">
                              €{d}
                          </td>
                      ))}
                  </tr>
                 <tr className={`font-bold text-lg ${isDarkMode ? 'bg-gray-900' : 'bg-blue-200'} ${frozenHeaders.balance ? 'sticky bottom-0 z-20' : ''}`}>
                    <td colSpan="1" className="px-2 py-3">
                      <button
                        onClick={() => setFrozenHeaders({...frozenHeaders, balance: !frozenHeaders.balance})}
                        className={`p-1 rounded ${frozenHeaders.balance ? 'text-blue-600' : 'text-gray-600'} hover:bg-gray-200`}
                        title={frozenHeaders.balance ? 'Отключи ред' : 'Заключи ред'}
                      >
                        {frozenHeaders.balance ? <Lock className="w-4 h-4" /> : <Unlock className="w-4 h-4" />}
                      </button>
                    </td>
                    <td colSpan="3" className="px-3 py-3 text-right">Дневен Баланс</td>
                    {(viewMode === 'both' || viewMode === 'bgn') && (
                      <td className={`px-2 py-3 text-center ${balance.bgn >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {balance.bgn.toFixed(2)} лв
                      </td>
                    )}
                    {(viewMode === 'both' || viewMode === 'bgn') && BGN_DENOMINATIONS.map((d, i) => (
                        <td key={`balance-bgn-${i}`} className={`px-2 py-3 text-center ${denominationCounts.balance.bgn[i] >= 0 ? (isDarkMode ? 'text-blue-300' : 'text-blue-800') : 'text-red-600'}`}>
                           {denominationCounts.balance.bgn[i] !== 0 ? denominationCounts.balance.bgn[i] : ''}
                        </td>
                    ))}
                    {(viewMode === 'both' || viewMode === 'eur') && (
                      <td className={`px-2 py-3 text-center ${balance.eur >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        €{balance.eur.toFixed(2)}
                      </td>
                    )}
                    {(viewMode === 'both' || viewMode === 'eur') && EUR_DENOMINATIONS.map((d, i) => (
                        <td key={`balance-eur-${i}`} className={`px-2 py-3 text-center ${denominationCounts.balance.eur[i] >= 0 ? (isDarkMode ? 'text-blue-300' : 'text-blue-800') : 'text-red-600'}`}>
                            {denominationCounts.balance.eur[i] !== 0 ? denominationCounts.balance.eur[i] : ''}
                        </td>
                    ))}
                  </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

// Analysis Component
const AnalysisTab = ({
  isDarkMode,
  analysisFilters,
  setAnalysisFilters,
  allTransactions,
  currentYear,
  currentMonth,
  dailyData,
  transactionGroups,
  setActiveTab,
  setCurrentYear,
  setCurrentMonth,
  setSelectedDay,
  setScrollToRowId,
  hasScrolledRef,
  salaryCashPayments, // Pass salary cash payments to generate automatic rows
  monthNames,
  calculateTotal
}) => {
  
  // Memoize the automatic salary rows for analysis
  const automaticSalaryRows = useMemo(() => {
    const rows = [];
    const seenMonths = new Set();

    Object.entries(salaryCashPayments).forEach(([employeeId, employeeData]) => {
      Object.keys(employeeData).forEach(monthKey => {
        if (!seenMonths.has(monthKey)) {
          seenMonths.add(monthKey);
          const [year, month] = monthKey.split('-').map(Number);
          
          let totalBgnCounts = {};
          Object.values(salaryCashPayments).forEach(empData => {
            const paymentsForPeriod = empData[monthKey];
            if (paymentsForPeriod) {
              Object.entries(paymentsForPeriod).forEach(([denomIndex, count]) => {
                if (count > 0) {
                  totalBgnCounts[denomIndex] = (totalBgnCounts[denomIndex] || 0) + count;
                }
              });
            }
          });

          if (Object.keys(totalBgnCounts).length > 0) {
              const bgnTotal = calculateTotal(BGN_DENOMINATIONS, BGN_DENOMINATIONS.map((_, i) => totalBgnCounts[i] || 0));
              // UPDATED: Calculate a representative payment date as the last day of the month AFTER the salary period.
              const paymentDate = new Date(year, month + 2, 0); 

              rows.push({
                  id: `auto-salary-${monthKey}`,
                  uniqueKey: `auto-salary-${monthKey}`,
                  type: 'expense',
                  name: `Заплати за ${monthNames[month]}`,
                  bgnTotal: bgnTotal,
                  eurTotal: 0,
                  year: paymentDate.getFullYear(),
                  month: paymentDate.getMonth(),
                  day: paymentDate.getDate(),
                  date: paymentDate,
                  isAutoGenerated: true,
              });
          }
        }
      });
    });
    return rows;
  }, [salaryCashPayments, monthNames, calculateTotal]);


  const filteredTransactions = useMemo(() => {
    const amount = parseFloat(analysisFilters.amountSearch);
    const byAmount = !Number.isNaN(amount);

    // Combine manual and automatic transactions for filtering
    const combinedTransactions = [...allTransactions, ...automaticSalaryRows];

    // REQUEST 1: If "showOnlyAutoSalaries" is checked, filter for those first.
    if (analysisFilters.showOnlyAutoSalaries) {
        return automaticSalaryRows.sort((a,b) => b.date - a.date);
    }

    let filtered = combinedTransactions.filter(t => {
      if (analysisFilters.searchName && !flexibleTextSearch(t.name, analysisFilters.searchName)) return false;
      if (analysisFilters.selectedGroups.length &&
          !analysisFilters.selectedGroups.includes(t.group)) return false;
      if (analysisFilters.excludedGroups.length &&
          analysisFilters.excludedGroups.includes(t.group))  return false;
      if (analysisFilters.excludeCarryOver && t.isCarryOver)  return false;
      
      const today = new Date();
      const dayOfWeek = today.getDay(); 
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
      startOfWeek.setHours(0, 0, 0, 0);
  
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      endOfWeek.setHours(23, 59, 59, 999);

      if (analysisFilters.dateRange === 'month') {
        if(!(t.year === currentYear && t.month === currentMonth)) return false;
      } else if (analysisFilters.dateRange === 'week') {
        if(!(t.date >= startOfWeek && t.date <= endOfWeek)) return false;
      } else if (analysisFilters.dateRange === 'lastMonth') {
        const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
        const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
        if(!(t.year === lastMonthYear && t.month === lastMonth)) return false;
      } else if (analysisFilters.dateRange === 'last3Months') {
        const threeMonthsAgo = new Date(currentYear, currentMonth - 2, 1);
        const currentMonthEnd = new Date(currentYear, currentMonth + 1, 0, 23, 59, 59, 999);
        if(!(t.date >= threeMonthsAgo && t.date <= currentMonthEnd)) return false;
      } else if (analysisFilters.dateRange === 'year') {
        if(!(t.year === currentYear)) return false;
      } else if (analysisFilters.dateRange === 'q1') {
        if(!(t.year === currentYear && t.month >= 0 && t.month <= 2)) return false;
      } else if (analysisFilters.dateRange === 'q2') {
        if(!(t.year === currentYear && t.month >= 3 && t.month <= 5)) return false;
      } else if (analysisFilters.dateRange === 'q3') {
        if(!(t.year === currentYear && t.month >= 6 && t.month <= 8)) return false;
      } else if (analysisFilters.dateRange === 'q4') {
        if(!(t.year === currentYear && t.month >= 9 && t.month <= 11)) return false;
      }
      
      if (byAmount) {
        const matches =
          t.bgnTotal?.toFixed(2).startsWith(analysisFilters.amountSearch) ||
          t.eurTotal?.toFixed(2).startsWith(analysisFilters.amountSearch);
        if (!matches) return false;
      }
      return true;
    });

    /* sort: if amount filter present – by closeness; else newest first */
    filtered = filtered.sort((a,b) => {
      if (byAmount) {
        const aVal = a.bgnTotal||a.eurTotal||0;
        const bVal = b.bgnTotal||b.eurTotal||0;
        return Math.abs(aVal-amount) - Math.abs(bVal-amount);
      }
      return b.date - a.date;
    });

    return filtered;

  }, [allTransactions, automaticSalaryRows, analysisFilters, currentYear, currentMonth]);
  
  const analysisTotals = useMemo(() => {
    // Use the already filtered transactions to calculate totals for accuracy
      const acc = { incomeBGN: 0, incomeEUR: 0, expenseBGN: 0, expenseEUR: 0 };
      
      filteredTransactions.forEach(t => {
          if (t.type === 'income') {
              acc.incomeBGN += t.bgnTotal || 0;
              acc.incomeEUR += t.eurTotal || 0;
          } else if (t.type === 'expense') {
              acc.expenseBGN += t.bgnTotal || 0;
              acc.expenseEUR += t.eurTotal || 0;
          }
      });

      return acc;
  }, [filteredTransactions]);

  const netBGN = analysisTotals.incomeBGN - analysisTotals.expenseBGN;
  const netEUR = analysisTotals.incomeEUR - analysisTotals.expenseEUR;
  
  const toggleGroup = (group) => {
    setAnalysisFilters(prev => ({
      ...prev,
      selectedGroups: prev.selectedGroups.includes(group) 
        ? prev.selectedGroups.filter(g => g !== group)
        : [...prev.selectedGroups, group],
      showOnlyAutoSalaries: false // Disable salary filter when changing groups
    }));
  };
  
  const toggleExcludeGroup = (group) => {
    setAnalysisFilters(prev => ({
      ...prev,
      excludedGroups: prev.excludedGroups.includes(group) 
        ? prev.excludedGroups.filter(g => g !== group)
        : [...prev.excludedGroups, group],
      showOnlyAutoSalaries: false // Disable salary filter
    }));
  };

  const handleGoToTransaction = (t) => {
    // UPDATED: Handle navigation for both normal and auto-generated transactions.
    if (t.isAutoGenerated) {
      // For auto-generated salaries, go to the calculated payment day in the daily sheet.
      hasScrolledRef.current = false;
      setActiveTab('daily');
      setCurrentYear(t.year);
      setCurrentMonth(t.month);
      setSelectedDay(t.day);
      setScrollToRowId(null); // No specific row to scroll to
      return;
    }
    
    // Original logic for normal transactions
    hasScrolledRef.current = true;
    setActiveTab('daily');
    setCurrentYear(t.year);
    setCurrentMonth(t.month);
    setSelectedDay(t.day);
    setScrollToRowId(t.uniqueKey);
  };
  
  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
        <h3 className="font-bold text-lg mb-3 flex items-center">
          <Filter className="w-5 h-5 mr-2" />
          Филтри
        </h3>
        
        <div className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <ControlledInput
              type="text"
              value={analysisFilters.searchName}
              onChange={(value) => setAnalysisFilters(prev => ({ ...prev, searchName: value, showOnlyAutoSalaries: false }))}
              placeholder="Търси по име..."
              className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2 w-full`}
            />
            <ControlledInput
              type="number"
              value={analysisFilters.amountSearch}
              onChange={value => setAnalysisFilters(p => ({ ...p, amountSearch:value, showOnlyAutoSalaries: false }))}
              placeholder="Сума (напр. 200)"
              className={`${isDarkMode?'bg-gray-700 border-gray-600':'bg-white'} border rounded px-3 py-2 w-full`}
            />
            <select
              value={analysisFilters.dateRange}
              onChange={(e) => setAnalysisFilters(prev => ({ ...prev, dateRange: e.target.value, showOnlyAutoSalaries: false }))}
              className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}
            >
              <option value="month">Текущ месец</option>
              <option value="week">Тази седмица</option>
              <option value="lastMonth">Миналия месец</option>
              <option value="last3Months">Последни 3 месеца</option>
              <option value="year">Текуща година</option>
              <option value="q1">Q1 (Ян-Мар)</option>
              <option value="q2">Q2 (Апр-Юни)</option>
              <option value="q3">Q3 (Юли-Сеп)</option>
              <option value="q4">Q4 (Окт-Дек)</option>
              <option value="all">Всички</option>
            </select>
          </div>
          
          <div>
            <label className="text-sm font-semibold mb-2 block">Филтрирай по групи:</label>
            <div className="flex flex-wrap gap-2">
              {transactionGroups.map(group => (
                <button
                  key={group}
                  onClick={() => toggleGroup(group)}
                  className={`px-3 py-1 rounded text-sm ${
                    analysisFilters.selectedGroups.includes(group)
                      ? 'bg-blue-500 text-white'
                      : isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
                  }`}
                >
                  {group}
                </button>
              ))}
            </div>
          </div>
          <div>
            <label className="text-sm font-semibold mb-2 block">Изключи групи:</label>
            <div className="flex flex-wrap gap-2">
              {transactionGroups.map(group => (
                <button
                  key={group}
                  onClick={() => toggleExcludeGroup(group)}
                  className={`px-3 py-1 rounded text-sm ${
                    analysisFilters.excludedGroups.includes(group)
                      ? 'bg-red-500 text-white'
                      : isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'
                  }`}
                >
                  {group}
                </button>
              ))}
            </div>
             <div className="flex items-center gap-4 mt-3">
                 <label className="inline-flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={analysisFilters.excludeCarryOver}
                    onChange={e => setAnalysisFilters(p => ({ ...p, excludeCarryOver:e.target.checked, showOnlyAutoSalaries: false }))}
                  />
                  Изключи „Пренос“
                </label>
                {/* REQUEST 1: New button to show only auto-generated salaries */}
                <button
                    onClick={() => setAnalysisFilters(prev => ({
                        searchName: '',
                        selectedGroups: [],
                        excludedGroups: [],
                        dateRange: 'all',
                        amountSearch: '',
                        excludeCarryOver: true, // Keep default as true
                        showOnlyAutoSalaries: !prev.showOnlyAutoSalaries
                    }))}
                    className={`px-3 py-1 rounded text-sm ${
                        analysisFilters.showOnlyAutoSalaries
                        ? 'bg-purple-600 text-white'
                        : isDarkMode ? 'bg-gray-600 hover:bg-gray-500' : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                >
                    <DollarSign className="w-4 h-4 mr-1 inline-block" />
                    Само заплати
                </button>
             </div>
          </div>
        </div>
      </div>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className={`${isDarkMode ? 'bg-green-900' : 'bg-green-100'} rounded-lg p-4`}>
          <h4 className={`font-semibold ${isDarkMode ? 'text-green-200' : 'text-green-800'} mb-2`}>Общо приходи</h4>
          <div className={`text-2xl font-bold ${isDarkMode ? 'text-green-100' : 'text-green-900'}`}>
            {analysisTotals.incomeBGN.toFixed(2)} лв
          </div>
          <div className={`text-lg font-semibold ${isDarkMode ? 'text-green-200' : 'text-green-800'}`}>
            €{analysisTotals.incomeEUR.toFixed(2)}
          </div>
        </div>
        
        <div className={`${isDarkMode ? 'bg-red-900' : 'bg-red-100'} rounded-lg p-4`}>
          <h4 className={`font-semibold ${isDarkMode ? 'text-red-200' : 'text-red-800'} mb-2`}>Общо разходи</h4>
          <div className={`text-2xl font-bold ${isDarkMode ? 'text-red-100' : 'text-red-900'}`}>
            {analysisTotals.expenseBGN.toFixed(2)} лв
          </div>
          <div className={`text-lg font-semibold ${isDarkMode ? 'text-red-200' : 'text-red-800'}`}>
            €{analysisTotals.expenseEUR.toFixed(2)}
          </div>
        </div>
        
        <div className={`${netBGN >= 0 ? (isDarkMode ? 'bg-blue-900' : 'bg-blue-100') : (isDarkMode ? 'bg-orange-900' : 'bg-orange-100')} rounded-lg p-4`}>
          <h4 className={`font-semibold ${netBGN >= 0 ? (isDarkMode ? 'text-blue-200' : 'text-blue-800') : (isDarkMode ? 'text-orange-200' : 'text-orange-800')} mb-2`}>
            Нетен баланс
          </h4>
          <div className={`text-2xl font-bold ${netBGN >= 0 ? (isDarkMode ? 'text-blue-100' : 'text-blue-900') : (isDarkMode ? 'text-orange-100' : 'text-orange-900')}`}>
            {netBGN.toFixed(2)} лв
          </div>
          <div className={`text-lg font-semibold ${netEUR >= 0 ? (isDarkMode ? 'text-blue-200' : 'text-blue-800') : (isDarkMode ? 'text-orange-200' : 'text-orange-800')}`}>
            €{netEUR.toFixed(2)}
          </div>
        </div>
      </div>
      
      {/* Transaction List */}
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
        <h3 className="font-bold text-lg mb-3">Транзакции</h3>
        
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <th className="text-left py-2 px-2">Дата</th>
                <th className="text-left py-2 px-2">Тип</th>
                <th className="text-left py-2 px-2">Група</th>
                <th className="text-left py-2 px-2">Име</th>
                <th className="text-center py-2 px-2">BGN</th>
                <th className="text-center py-2 px-2">EUR</th>
                <th className="text-center py-2 px-2">Действие</th>
              </tr>
            </thead>
            <tbody>
              {filteredTransactions.map((t, index) => (
                <tr key={t.uniqueKey || index} className={`border-b ${t.isAutoGenerated ? (isDarkMode ? 'bg-purple-900/40' : 'bg-purple-100') : ''} ${isDarkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'}`}>
                  <td className="py-2 px-2">{`${t.day}.${t.month + 1}.${t.year}`}</td>
                  <td className={`py-2 px-2 ${t.type === 'income' ? 'text-green-600' : 'text-red-600'}`}>
                    {t.type === 'income' ? 'Приход' : 'Разход'}
                  </td>
                  <td className="py-2 px-2">{t.group}</td>
                  <td className="py-2 px-2">{t.name}</td>
                  <td className="py-2 text-center px-2">{(t.bgnTotal || 0).toFixed(2)}</td>
                  <td className="py-2 text-center px-2">{(t.eurTotal || 0).toFixed(2)}</td>
                  <td className="py-1 px-1 text-center">
                    {/* UPDATED: Button is now always enabled and has conditional logic/title */}
                    <button
                      onClick={() => handleGoToTransaction(t)}
                      className={`px-4 py-2 rounded-full text-blue-500 hover:text-blue-700 hover:bg-blue-100`}
                      title={t.isAutoGenerated ? "Отиди до деня на плащане" : "Отиди до транзакцията"}
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Departments Section Component
const DepartmentsSection = ({
  isDarkMode,
  departments,
  setDepartments,
  employeeList,
  setEmployeeList
}) => {
  const [showAddDepartment, setShowAddDepartment] = useState(false);
  const [newDepartmentName, setNewDepartmentName] = useState('');
  const [editingDepartment, setEditingDepartment] = useState(null);
  const [editDepartmentName, setEditDepartmentName] = useState('');
  
  const addDepartment = () => {
    if (newDepartmentName.trim() && !departments[newDepartmentName]) {
      setDepartments({
        ...departments,
        [newDepartmentName]: {
          workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
          halfDays: []
        }
      });
      setNewDepartmentName('');
      setShowAddDepartment(false);
    }
  };
  
  const updateDepartmentName = () => {
    if (editDepartmentName.trim() && editingDepartment && editDepartmentName !== editingDepartment) {
      const updatedDepartments = {};
      const updatedEmployees = employeeList.map(emp => {
        if (emp.department === editingDepartment) {
          return { ...emp, department: editDepartmentName };
        }
        return emp;
      });
      
      Object.entries(departments).forEach(([key, value]) => {
        if (key === editingDepartment) {
          updatedDepartments[editDepartmentName] = value;
        } else {
          updatedDepartments[key] = value;
        }
      });
      
      setDepartments(updatedDepartments);
      setEmployeeList(updatedEmployees);
      setEditingDepartment(null);
      setEditDepartmentName('');
    }
  };
  
  const deleteDepartment = (deptName) => {
    if (employeeList.some(emp => emp.department === deptName)) {
      alert('Не може да изтриете отдел с служители!');
      return;
    }
    
    const { [deptName]: removed, ...rest } = departments;
    setDepartments(rest);
  };
  
  return (
    <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-bold text-lg">Управление на отдели</h3>
        <button
          onClick={() => setShowAddDepartment(!showAddDepartment)}
          className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center"
        >
          <Plus className="w-4 h-4 mr-1" />
          Добави отдел
        </button>
      </div>
      
      {showAddDepartment && (
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={newDepartmentName}
            onChange={(e) => setNewDepartmentName(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addDepartment()}
            placeholder="Име на нов отдел"
            className={`flex-1 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}
          />
          <button
            onClick={addDepartment}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            Добави
          </button>
        </div>
      )}
      
      <div className="space-y-2">
        {Object.keys(departments).map(dept => (
          <div key={dept} className={`flex items-center justify-between p-2 border rounded ${isDarkMode ? 'border-gray-700' : ''}`}>
            {editingDepartment === dept ? (
              <>
                <input
                  type="text"
                  value={editDepartmentName}
                  onChange={(e) => setEditDepartmentName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && updateDepartmentName()}
                  className={`flex-1 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1`}
                />
                <div className="flex gap-2 ml-2">
                  <button
                    onClick={updateDepartmentName}
                    className="text-green-500 hover:text-green-700"
                  >
                    ✓
                  </button>
                  <button
                    onClick={() => {
                      setEditingDepartment(null);
                      setEditDepartmentName('');
                    }}
                    className="text-red-500 hover:text-red-700"
                  >
                    ✗
                  </button>
                </div>
              </>
            ) : (
              <>
                <span>{dept}</span>
                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      setEditingDepartment(dept);
                      setEditDepartmentName(dept);
                    }}
                    className="text-blue-500 hover:text-blue-700"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => deleteDepartment(dept)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// Employee List Component
const EmployeeListTab = ({
  isDarkMode,
  departments,
  setDepartments,
  employeeList,
  setEmployeeList,
  employeeTransactions,
  setEmployeeTransactions,
  setClientList,
  leaveData,
  currentYear,
  importEmployeesFromFile // NEW PROP
}) => {
  const [expandedEmployee, setExpandedEmployee] = useState(null);
  const [showAddEmployee, setShowAddEmployee] = useState(false);
  const [newEmployee, setNewEmployee] = useState({ 
      name: '', 
      totalSalary: '', 
      bankSalary: '', 
      socialSecurity: '',
      department: Object.keys(departments)[0] || 'Администрация',
      annualLeave: ''
  });

  const [editingEmployeeId, setEditingEmployeeId] = useState(null);
  const [editEmployeeData, setEditEmployeeData] = useState(null);

  const handleEditClick = (employee) => {
    setEditingEmployeeId(employee.id);
    setEditEmployeeData({ ...employee });
  };

  const handleCancelEdit = () => {
    setEditingEmployeeId(null);
    setEditEmployeeData(null);
  };

  const handleSaveEdit = (id) => {
    setEmployeeList(employeeList.map(emp => emp.id === id ? editEmployeeData : emp));
    handleCancelEdit();
  };

  const handleEditChange = (field, value) => {
    setEditEmployeeData(prev => ({ ...prev, [field]: value }));
  };
  
  const toggleExpanded = (employeeId) => {
    setExpandedEmployee(expandedEmployee === employeeId ? null : employeeId);
  };
  
  const handleAddEmployee = () => {
    if (newEmployee.name && newEmployee.totalSalary) {
      const newEmp = {
        id: Date.now(),
        name: newEmployee.name,
        totalSalary: parseFloat(newEmployee.totalSalary) || 0,
        bankSalary: parseFloat(newEmployee.bankSalary) || 0,
        socialSecurity: parseFloat(newEmployee.socialSecurity) || 0,
        department: newEmployee.department,
        annualLeave: parseInt(newEmployee.annualLeave) || 0
      };
      setEmployeeList([...employeeList, newEmp]);
      
      // Also add to client list for autocomplete in expenses
      setClientList(prev => [...new Set([...prev, { name: newEmp.name, group: 'Заплати' }])]);
      
      setNewEmployee({ name: '', totalSalary: '', bankSalary: '', socialSecurity: '', department: Object.keys(departments)[0] || 'Администрация', annualLeave: '' });
      setShowAddEmployee(false);
    }
  };
  
  const deleteEmployee = (id) => {
    if(window.confirm('Сигурни ли сте, че искате да изтриете този служител?')) {
      const employeeToDelete = employeeList.find(emp => emp.id === id);
      setEmployeeList(employeeList.filter(emp => emp.id !== id));
      setEmployeeTransactions(prev => {
        const newTrans = { ...prev };
        delete newTrans[id];
        return newTrans;
      });
       // Also remove from client list
       if (employeeToDelete) {
        setClientList(prev => prev.filter(c => c.name !== employeeToDelete.name));
      }
    }
  };
  
  return (
    <div className="space-y-4">
      <DepartmentsSection
        isDarkMode={isDarkMode}
        departments={departments}
        setDepartments={setDepartments}
        employeeList={employeeList}
        setEmployeeList={setEmployeeList}
      />
      
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-bold text-lg flex items-center">
            <Users className="w-5 h-5 mr-2" />
            Списък служители
          </h3>
           {/* UPDATED: Buttons wrapper */}
          <div className="flex items-center gap-2">
            <label className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 flex items-center cursor-pointer">
              <Upload className="w-4 h-4 mr-1" />
              Импорт
              <input
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={importEmployeesFromFile}
                className="hidden"
              />
            </label>
            <button
              onClick={() => setShowAddEmployee(!showAddEmployee)}
              className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center"
            >
              <Plus className="w-4 h-4 mr-1" />
              Добави служител
            </button>
          </div>
        </div>
        
        {showAddEmployee && (
          <div className="border-t pt-3 mt-3 space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-6 gap-3">
              <input type="text" value={newEmployee.name} onChange={(e) => setNewEmployee({...newEmployee, name: e.target.value})} placeholder="Име на служител" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}/>
              <input type="number" value={newEmployee.totalSalary} onChange={(e) => setNewEmployee({...newEmployee, totalSalary: e.target.value})} placeholder="Обща заплата (BGN)" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}/>
              <input type="number" value={newEmployee.bankSalary} onChange={(e) => setNewEmployee({...newEmployee, bankSalary: e.target.value})} placeholder="Заплата по банка (BGN)" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}/>
              <input type="number" value={newEmployee.socialSecurity} onChange={(e) => setNewEmployee({...newEmployee, socialSecurity: e.target.value})} placeholder="Осигуровки (BGN)" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}/>
              <select value={newEmployee.department} onChange={(e) => setNewEmployee({...newEmployee, department: e.target.value})} className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}>
                {Object.keys(departments).map(dept => (<option key={dept} value={dept}>{dept}</option>))}
              </select>
              <input type="number" value={newEmployee.annualLeave} onChange={(e) => setNewEmployee({...newEmployee, annualLeave: e.target.value})} placeholder="Годишен отпуск" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}/>
            </div>
            <button onClick={handleAddEmployee} className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Запази</button>
          </div>
        )}
      </div>
      
      {employeeList.map(employee => {
        const isEditing = editingEmployeeId === employee.id;
        const transactions = employeeTransactions[employee.id] || [];
        const isExpanded = expandedEmployee === employee.id;
        
        const usedLeaveThisYear = Object.values(leaveData[employee.id]?.[currentYear] || {}).reduce((sum, days) => sum + (days || 0), 0);
        const remainingLeave = (employee.annualLeave || 0) - usedLeaveThisYear;
        // REQUEST 4: The cash part of the salary.
        const cashSalary = (employee.totalSalary || 0) - (employee.socialSecurity || 0) - (employee.bankSalary || 0);

        if (isEditing) {
          return (
            <div key={employee.id} className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-3">
                    <div>
                        <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Име</label>
                        <input type="text" value={editEmployeeData.name} onChange={(e) => handleEditChange('name', e.target.value)} placeholder="Име" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : ''} border rounded px-3 py-2 w-full`} />
                    </div>
                    <div>
                        <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Обща заплата</label>
                        <input type="number" value={editEmployeeData.totalSalary} onChange={(e) => handleEditChange('totalSalary', parseFloat(e.target.value) || 0)} placeholder="Обща заплата" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : ''} border rounded px-3 py-2 w-full`} />
                    </div>
                    <div>
                        <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Заплата по банка</label>
                        <input type="number" value={editEmployeeData.bankSalary} onChange={(e) => handleEditChange('bankSalary', parseFloat(e.target.value) || 0)} placeholder="Заплата по банка" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : ''} border rounded px-3 py-2 w-full`} />
                    </div>
                    <div>
                        <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Осигуровки</label>
                        <input type="number" value={editEmployeeData.socialSecurity} onChange={(e) => handleEditChange('socialSecurity', parseFloat(e.target.value) || 0)} placeholder="Осигуровки" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : ''} border rounded px-3 py-2 w-full`} />
                    </div>
                    <div>
                        <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Отдел</label>
                        <select value={editEmployeeData.department} onChange={(e) => handleEditChange('department', e.target.value)} className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : ''} border rounded px-3 py-2 w-full`}>
                           {Object.keys(departments).map(dept => (<option key={dept} value={dept}>{dept}</option>))}
                        </select>
                    </div>
                    <div>
                        <label className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>Годишен отпуск</label>
                        <input type="number" value={editEmployeeData.annualLeave} onChange={(e) => handleEditChange('annualLeave', parseInt(e.target.value) || 0)} placeholder="Годишен отпуск" className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : ''} border rounded px-3 py-2 w-full`} />
                    </div>
                </div>
                <div className="flex gap-2">
                    <button onClick={() => handleSaveEdit(employee.id)} className="bg-green-500 text-white px-4 py-1 rounded hover:bg-green-600">Запази</button>
                    <button onClick={handleCancelEdit} className="bg-gray-500 text-white px-4 py-1 rounded hover:bg-gray-600">Отказ</button>
                </div>
            </div>
          )
        }

        return (
          <div key={employee.id} className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <button onClick={() => toggleExpanded(employee.id)} className={`p-1 ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'} rounded`}>
                  {isExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
                </button>
                <div>
                  <h4 className="font-semibold">{employee.name}</h4>
                  <div className="text-sm text-gray-500 flex flex-wrap gap-x-4">
                    <span>{employee.department}</span>
                    <span>• Общо: {(employee.totalSalary || 0).toFixed(2)} лв</span>
                    <span>• По банка: {(employee.bankSalary || 0).toFixed(2)} лв</span>
                    <span className="text-red-500">• Осигуровки: {(employee.socialSecurity || 0).toFixed(2)} лв</span>
                    <span className="text-green-600 font-semibold">• В брой: {cashSalary.toFixed(2)} лв</span>
                    <span className="text-purple-500">• Отпуск: {remainingLeave} / {employee.annualLeave || 0} (изп. {usedLeaveThisYear})</span>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <button onClick={() => handleEditClick(employee)} className="text-blue-500 hover:text-blue-700"><Edit className="w-4 h-4" /></button>
                <button onClick={() => deleteEmployee(employee.id)} className="text-red-500 hover:text-red-700"><Trash2 className="w-4 h-4" /></button>
              </div>
            </div>
            
            {isExpanded && (
              <div className="mt-4 pl-10">
                <h5 className="font-semibold mb-2">Транзакции:</h5>
                {transactions.length > 0 ? (
                  <div className="space-y-1">
                    {transactions.map((trans, i) => (
                      <div key={i} className={`flex justify-between text-sm p-2 rounded ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                        <span>{trans.date}</span>
                        <span>{trans.type}</span>
                        <span className="font-mono">{trans.amount.toFixed(2)} лв</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">Няма транзакции</p>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};


// NEW: Salary Detail Popup Component
const SalaryDetailPopup = ({ popupData, setPopupData, onClose, allTransactions, monthNames, isDarkMode }) => {
  const { employee, month, year } = popupData;

  const filtered = useMemo(() => {
    if (!employee) return [];
    return allTransactions.filter(t => 
      flexibleTextSearch(t.name, employee.name) &&
      t.month === month &&
      t.year === year
    ).sort((a,b) => a.date - b.date);
  }, [employee, month, year, allTransactions]);

  const totalAmount = useMemo(() => {
      return filtered.reduce((sum, t) => sum + (t.bgnTotal || 0), 0);
  }, [filtered]);

  if (!employee) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`${isDarkMode ? 'bg-gray-900' : 'bg-gray-100'} rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[90vh] flex flex-col`}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-bold text-xl">Транзакции за {employee.name}</h3>
          <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-300">
            <XCircle className="w-6 h-6" />
          </button>
        </div>

        <div className="flex items-center gap-4 mb-4 p-3 rounded-md border">
            <span className="font-semibold">Период:</span>
            <select 
                value={month} 
                onChange={(e) => setPopupData({...popupData, month: parseInt(e.target.value)})}
                className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}
            >
                {monthNames.map((name, index) => <option key={name} value={index}>{name}</option>)}
            </select>
            <input 
                type="number"
                value={year}
                onChange={(e) => setPopupData({...popupData, year: parseInt(e.target.value)})}
                className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2 w-24`}
            />
        </div>

        <div className="flex-grow overflow-y-auto">
          {filtered.length > 0 ? (
            <table className="w-full text-sm">
              <thead>
                <tr className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                  <th className="text-left py-2 px-2">Дата</th>
                  <th className="text-left py-2 px-2">Тип</th>
                  <th className="text-left py-2 px-2">Група</th>
                  <th className="text-left py-2 px-2">Име</th>
                  <th className="text-center py-2 px-2">Сума (BGN)</th>
                </tr>
              </thead>
              <tbody>
                {filtered.map((t) => (
                  <tr key={t.uniqueKey} className={`border-b ${isDarkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'}`}>
                    <td className="py-2 px-2">{`${t.day}.${t.month + 1}.${t.year}`}</td>
                    <td className={`py-2 px-2 ${t.type === 'income' ? 'text-green-600' : 'text-red-600'}`}>
                      {t.type === 'income' ? 'Приход' : 'Разход'}
                    </td>
                    <td className="py-2 px-2">{t.group}</td>
                    <td className="py-2 px-2">{t.name}</td>
                    <td className="py-2 text-center px-2 font-mono">{(t.bgnTotal || 0).toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className={`font-bold border-t-2 ${isDarkMode ? 'border-gray-700' : 'border-gray-300'}`}>
                  <td colSpan="4" className="text-right py-2 px-2">Общо:</td>
                  <td className="text-center py-2 px-2 font-mono">{totalAmount.toFixed(2)} лв</td>
                </tr>
              </tfoot>
            </table>
          ) : (
            <p className="text-center py-8 text-gray-500">Няма намерени транзакции за избрания период.</p>
          )}
        </div>
      </div>
    </div>
  );
}

// NEW: Global Cash Payment Table Component
const GlobalCashPaymentSection = ({
    isDarkMode,
    employeesByDepartment,
    monthNames,
    salaryCashPayments,
    handleSalaryCashChange,
    initialYear,
    initialMonth,
    calculateTotal
}) => {
    const [viewDate, setViewDate] = useState({ year: initialYear, month: initialMonth });

    const navigateMonth = (direction) => {
        const currentDate = new Date(viewDate.year, viewDate.month, 1);
        currentDate.setMonth(currentDate.getMonth() + direction);
        setViewDate({
            year: currentDate.getFullYear(),
            month: currentDate.getMonth()
        });
    };

    const handleBillChange = (employeeId, year, month, denomIndex, count) => {
        const monthKey = `${year}-${month}`;
        const currentPayments = salaryCashPayments[employeeId]?.[monthKey] || {};
        const newPayments = {
            ...currentPayments,
            [denomIndex]: parseInt(count) || 0
        };
        handleSalaryCashChange(employeeId, year, month, newPayments);
    };
    
    // Memoized calculation for department totals
    const departmentTotals = useMemo(() => {
        const totals = {};
        const monthKey = `${viewDate.year}-${viewDate.month}`;

        for (const deptName in employeesByDepartment) {
            const deptCounts = Array(BGN_DENOMINATIONS.length).fill(0);
            const employeesInDept = employeesByDepartment[deptName];
            
            employeesInDept.forEach(employee => {
                const payments = salaryCashPayments[employee.id]?.[monthKey] || {};
                BGN_DENOMINATIONS.forEach((_, index) => {
                    deptCounts[index] += payments[index] || 0;
                });
            });
            
            totals[deptName] = {
                counts: deptCounts,
                totalAmount: calculateTotal(BGN_DENOMINATIONS, deptCounts)
            };
        }
        return totals;
    }, [employeesByDepartment, salaryCashPayments, viewDate, calculateTotal]);


    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center p-3 rounded-lg border">
                <h3 className="font-bold text-lg">Разпределение на пари в брой</h3>
                <div className="flex items-center gap-2">
                    <button onClick={() => navigateMonth(-1)} className="p-1 rounded hover:bg-gray-300"><ChevronLeft className="w-5 h-5"/></button>
                    <span className="font-bold text-lg">{monthNames[viewDate.month]} {viewDate.year}</span>
                    <button onClick={() => navigateMonth(1)} className="p-1 rounded hover:bg-gray-300"><ChevronRight className="w-5 h-5"/></button>
                </div>
            </div>

            <div className="overflow-x-auto">
                <table className="w-full text-base">
                    <thead>
                        <tr className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'} font-semibold`}>
                            <th className="px-3 py-2 text-left w-64">Име</th>
                            {BGN_DENOMINATIONS.map(d => (
                                <th key={`header-bgn-${d}`} className="px-2 py-2 text-center w-20">{d}лв</th>
                            ))}
                            <th className="px-3 py-2 text-center w-28">Общо</th>
                        </tr>
                    </thead>
                    <tbody>
                        {Object.entries(employeesByDepartment).map(([deptName, employees]) => (
                            <React.Fragment key={deptName}>
                                <tr className={`font-bold ${isDarkMode ? 'bg-gray-900/50' : 'bg-gray-100'}`}>
                                    <td className="px-3 py-2">{deptName}</td>
                                    {departmentTotals[deptName].counts.map((count, i) => (
                                        <td key={i} className="px-2 py-2 text-center text-blue-600">
                                            {count > 0 ? count : ''}
                                        </td>
                                    ))}
                                    <td className="px-3 py-2 text-center text-blue-600 font-semibold">
                                        {departmentTotals[deptName].totalAmount > 0 ? departmentTotals[deptName].totalAmount.toFixed(2) + ' лв' : ''}
                                    </td>
                                </tr>
                                {employees.map(employee => {
                                    const monthKey = `${viewDate.year}-${viewDate.month}`;
                                    const employeePayments = salaryCashPayments[employee.id]?.[monthKey] || {};
                                    const employeeTotal = calculateTotal(BGN_DENOMINATIONS, BGN_DENOMINATIONS.map((_, i) => employeePayments[i] || 0));

                                    return (
                                        <tr key={employee.id} className={`border-b ${isDarkMode ? 'border-gray-800 hover:bg-gray-700/50' : 'border-gray-200 hover:bg-gray-50'}`}>
                                            <td className="px-3 py-2">{employee.name}</td>
                                            {BGN_DENOMINATIONS.map((denom, i) => (
                                                <td key={i} className="px-2 py-2">
                                                    <ControlledInput
                                                        type="number"
                                                        min="0"
                                                        value={employeePayments[i] || ''}
                                                        onChange={(val) => handleBillChange(employee.id, viewDate.year, viewDate.month, i, val)}
                                                        className={`w-full px-1 py-1 text-base text-center border rounded ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'}`}
                                                    />
                                                </td>
                                            ))}
                                            <td className="px-3 py-2 text-center font-semibold">
                                                {employeeTotal > 0 ? employeeTotal.toFixed(2) + ' лв' : ''}
                                            </td>
                                        </tr>
                                    );
                                })}
                            </React.Fragment>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};


// Salary Management Component
const SalaryTab = ({
  isDarkMode,
  currentMonth,
  currentYear,
  monthNames,
  calculateWorkingDays,
  employeeList,
  allTransactions,
  salaryCashPayments,
  handleSalaryCashChange,
  leaveData,
  handleLeaveDataChange,
  calculateTotal,
  popupData,
  setPopupData,
  departments,
  // REQUEST 5: Pass salary inputs state and setter
  salaryInputs,
  setSalaryInputs,
}) => {
  const [expandedDepartments, setExpandedDepartments] = useState({});
  const [expandedCashPayment, setExpandedCashPayment] = useState(null);
  const [cashPaymentView, setCashPaymentView] = useState({});

  const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
  const prevYear = currentMonth === 0 ? currentYear - 1 : currentYear;
  
  // REQUEST 5: Key for storing/retrieving salary inputs for the current period.
  const salaryInputKey = `${prevYear}-${prevMonth}`;
  
  const standardWorkingDays = useMemo(() => calculateWorkingDays('Администрация', prevMonth, prevYear), [departments, prevMonth, prevYear, calculateWorkingDays]);
  const workshopWorkingDays = useMemo(() => calculateWorkingDays('Цех', prevMonth, prevYear), [departments, prevMonth, prevYear, calculateWorkingDays]);

  const employeesByDepartment = useMemo(() => {
      const sortedEmployees = [...employeeList].sort((a, b) => a.name.localeCompare(b.name));
      return sortedEmployees.reduce((acc, employee) => {
          const dept = employee.department || 'Без отдел';
          if (!acc[dept]) {
              acc[dept] = [];
          }
          acc[dept].push(employee);
          return acc;
      }, {});
  }, [employeeList]);

  const toggleDepartment = (deptName) => {
      setExpandedDepartments(prev => ({
          ...prev,
          [deptName]: !prev[deptName]
      }));
  };
  
  const handleCashPaymentBillChange = (employeeId, year, month, denomIndex, count) => {
      const monthKey = `${year}-${month}`;
      const currentPayments = salaryCashPayments[employeeId]?.[monthKey] || {};
      const newPayments = {
          ...currentPayments,
          [denomIndex]: parseInt(count) || 0
      };
      handleSalaryCashChange(employeeId, year, month, newPayments);
  };

  const handleUsedLeaveChange = (employeeId, days) => {
      handleLeaveDataChange(employeeId, prevYear, prevMonth, parseInt(days) || 0);
  };
  
  // REQUEST 5: Handler to update salary input fields.
  const handleSalaryInputChange = (employeeId, field, value) => {
    setSalaryInputs(prev => {
        const newInputs = JSON.parse(JSON.stringify(prev));
        if (!newInputs[salaryInputKey]) {
            newInputs[salaryInputKey] = {};
        }
        if (!newInputs[salaryInputKey][employeeId]) {
            newInputs[salaryInputKey][employeeId] = {};
        }
        newInputs[salaryInputKey][employeeId][field] = value;
        return newInputs;
    });
  };
  
  const calculateCashSalary = useCallback((employee) => {
    // REQUEST 5: Get salary inputs for the specific month/year.
    const data = salaryInputs[salaryInputKey]?.[employee.id] || {};
    const deptWorkingDays = calculateWorkingDays(employee.department, prevMonth, prevYear);
    
    const workedDays = data.workedDays !== undefined ? data.workedDays : deptWorkingDays;
    const dailyRate = deptWorkingDays > 0 ? (employee.totalSalary || 0) / deptWorkingDays : 0;
    
    const grossPay = dailyRate * workedDays;
    const additionalIncome = parseFloat(data.additionalIncome) || 0;
    const bonuses = parseFloat(data.bonuses) || 0;
    const deductions = parseFloat(data.deductions) || 0;
    const manualAdvance = parseFloat(data.manualAdvance) || 0;
    // REQUEST 3: Get sick leave amount.
    const sickLeave = parseFloat(data.sickLeave) || 0;
    
    // Pro-rated social security
    const proratedSocialSecurity = deptWorkingDays > 0 && workedDays < deptWorkingDays
      ? ((employee.socialSecurity || 0) / deptWorkingDays) * workedDays
      : (employee.socialSecurity || 0);
    
    // REQUEST 3 & 4: Final calculation for cash payment.
    return grossPay + additionalIncome + bonuses - deductions - proratedSocialSecurity - manualAdvance - (employee.bankSalary || 0) - sickLeave;
  }, [salaryInputs, salaryInputKey, prevMonth, prevYear, calculateWorkingDays, employeeList]);

  const handleNameClick = (employee) => {
    setPopupData({ isOpen: true, employee: employee, month: prevMonth, year: prevYear });
  };

  const navigateCashMonth = (employeeId, direction) => {
    setCashPaymentView(prev => {
      const currentView = prev[employeeId] || { year: prevYear, month: prevMonth };
      const currentDate = new Date(currentView.year, currentView.month, 1);
      currentDate.setMonth(currentDate.getMonth() + direction);
      return {
        ...prev,
        [employeeId]: {
          year: currentDate.getFullYear(),
          month: currentDate.getMonth()
        }
      };
    });
  };
  
  return (
    <div className="space-y-4">
      {popupData.isOpen && (
          <SalaryDetailPopup
              popupData={popupData}
              setPopupData={setPopupData}
              onClose={() => setPopupData({ isOpen: false, employee: null, month: 0, year: 0 })}
              allTransactions={allTransactions}
              monthNames={monthNames}
              isDarkMode={isDarkMode}
          />
      )}

      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
        <h3 className="font-bold text-lg mb-4 flex items-center flex-wrap">
          Изчисляване на заплати за {monthNames[prevMonth]} {prevYear}
          <span className="text-base font-semibold text-gray-500 ml-4">
            (Раб. дни: {standardWorkingDays} | Раб. дни Цех: {workshopWorkingDays})
          </span>
        </h3>
      </div>
      
      {Object.entries(employeesByDepartment).map(([departmentName, employees]) => {
        const isExpanded = !!expandedDepartments[departmentName];

        return (
          <div key={departmentName} className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md overflow-hidden`}>
            <div 
              onClick={() => toggleDepartment(departmentName)}
              className={`flex justify-between items-center p-4 cursor-pointer ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}
            >
              <h4 className="font-bold text-xl">{departmentName} ({employees.length} служители)</h4>
              {isExpanded ? <ChevronUp className="w-6 h-6" /> : <ChevronDown className="w-6 h-6" />}
            </div>
            
            {isExpanded && (
              <div className={`border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} p-4 space-y-4`}>
                {employees.map(employee => {
                  const totalCashSalary = calculateCashSalary(employee);
                  // REQUEST 5: Get salary inputs for the specific employee and period.
                  const data = salaryInputs[salaryInputKey]?.[employee.id] || {};
                  const workingDaysInDept = calculateWorkingDays(employee.department, prevMonth, prevYear);
                  
                  const isCashSectionExpanded = expandedCashPayment === employee.id;

                  const viewYear = cashPaymentView[employee.id]?.year || prevYear;
                  const viewMonth = cashPaymentView[employee.id]?.month !== undefined ? cashPaymentView[employee.id].month : prevMonth;
                  const monthKey = `${viewYear}-${viewMonth}`;
                  const employeeCashPaymentForMonth = salaryCashPayments[employee.id]?.[monthKey] || {};
                  const employeeCashPaymentTotal = calculateTotal(BGN_DENOMINATIONS, BGN_DENOMINATIONS.map((_, i) => employeeCashPaymentForMonth[i] || 0));

                  
                  return (
                    <div key={employee.id} className={`${isDarkMode ? 'bg-gray-900/50' : 'bg-gray-50'} rounded-lg shadow-sm p-4 border ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                      <div className="flex justify-between items-start mb-3">
                        <div>
                            <button onClick={() => handleNameClick(employee)} className="font-semibold text-lg text-blue-600 hover:underline text-left">
                                {employee.name}
                            </button>
                            <p className="text-sm text-gray-500">{employee.department}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-500">Обща заплата</div>
                          <div className="font-semibold">{(employee.totalSalary || 0).toFixed(2)} лв</div>
                          <div className="text-sm text-gray-500">Платени по банка</div>
                          <div className="font-semibold text-blue-500">{(employee.bankSalary || 0).toFixed(2)} лв</div>
                          <div className="text-sm text-gray-500">Осигуровки (удръжка)</div>
                          <div className="font-semibold text-red-500">{(employee.socialSecurity || 0).toFixed(2)} лв</div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-7 gap-3 mb-3">
                        <div>
                          <label className="text-sm text-gray-600">Работни дни (от {workingDaysInDept})</label>
                          <input type="number" value={data.workedDays !== undefined ? data.workedDays : ''} placeholder={workingDaysInDept.toString()} onChange={(e) => handleSalaryInputChange(employee.id, 'workedDays', e.target.value === '' ? undefined : parseFloat(e.target.value) || 0)} className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1 w-full`}/>
                        </div>
                        <div>
                          <label className="text-sm text-gray-600">Изп. отпуск (дни)</label>
                          <input type="number" value={leaveData[employee.id]?.[prevYear]?.[prevMonth] || ''} onChange={(e) => handleUsedLeaveChange(employee.id, e.target.value)} className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1 w-full`} />
                        </div>
                        <div>
                          <label className="text-sm text-gray-600">Допълнителни</label>
                          <input type="number" value={data.additionalIncome || ''} onChange={(e) => handleSalaryInputChange(employee.id, 'additionalIncome', e.target.value)} className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1 w-full`} />
                        </div>
                        <div>
                          <label className="text-sm text-gray-600">Бонуси</label>
                          <input type="number" value={data.bonuses || ''} onChange={(e) => handleSalaryInputChange(employee.id, 'bonuses', e.target.value)} className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1 w-full`} />
                        </div>
                        <div>
                          <label className="text-sm text-gray-600">Аванс заплата</label>
                          <input type="number" value={data.manualAdvance || ''} onChange={(e) => handleSalaryInputChange(employee.id, 'manualAdvance', e.target.value)} className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1 w-full`} />
                        </div>
                        {/* REQUEST 3: Added Sick Leave (Болнични) field */}
                        <div>
                          <label className="text-sm text-red-500">Болнични (лв)</label>
                          <input type="number" value={data.sickLeave || ''} onChange={(e) => handleSalaryInputChange(employee.id, 'sickLeave', e.target.value)} className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1 w-full`} />
                        </div>
                        <div>
                          <label className="text-sm text-gray-600">Други удръжки</label>
                          <input type="number" value={data.deductions || ''} onChange={(e) => handleSalaryInputChange(employee.id, 'deductions', e.target.value)} className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1 w-full`} />
                        </div>
                      </div>
                                              
                      <div className={`border-t pt-3 mt-3 ${isDarkMode ? 'border-gray-700' : 'border-gray-300'}`}>
                        <div className="flex justify-between items-center">
                          <div className="font-semibold flex items-center">
                            За плащане в брой:
                            <button 
                              onClick={() => setExpandedCashPayment(isCashSectionExpanded ? null : employee.id)}
                              className="ml-4 bg-blue-500 text-white px-3 py-1 text-sm rounded hover:bg-blue-600 flex items-center"
                            >
                                <DollarSign className="w-4 h-4 mr-1" />
                                Пари
                                {isCashSectionExpanded ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />}
                            </button>
                          </div>
                          <span className={`text-xl font-bold ${totalCashSalary >= 0 ? 'text-green-600' : 'text-red-500'}`}>{totalCashSalary.toFixed(2)} лв</span>
                        </div>
                      </div>
                      
                      {isCashSectionExpanded && (
                          <div className={`mt-2 p-3 ${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'} border ${isDarkMode ? 'border-blue-800' : 'border-blue-200'} rounded-lg`}>
                              <div className="flex justify-between items-center mb-3">
                                <span className="font-semibold">Плащане в брой за:</span>
                                <div className="flex items-center gap-2">
                                  <button onClick={() => navigateCashMonth(employee.id, -1)} className="p-1 rounded hover:bg-gray-300"><ChevronLeft className="w-5 h-5"/></button>
                                  <span className="font-bold text-lg">{monthNames[viewMonth]} {viewYear}</span>
                                  <button onClick={() => navigateCashMonth(employee.id, 1)} className="p-1 rounded hover:bg-gray-300"><ChevronRight className="w-5 h-5"/></button>
                                </div>
                              </div>
                              <div className="grid grid-cols-7 gap-2 items-center">
                                  {BGN_DENOMINATIONS.slice(0, 7).map((denom, i) => (
                                      <div key={i}>
                                          <label className="text-xs text-center block">{denom} лв</label>
                                          <ControlledInput
                                              type="number"
                                              min="0"
                                              value={employeeCashPaymentForMonth[i] || ''}
                                              onChange={(val) => handleCashPaymentBillChange(employee.id, viewYear, viewMonth, i, val)}
                                              className={`w-full px-1 py-1 text-base text-center border rounded ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'}`}
                                          />
                                      </div>
                                  ))}
                              </div>
                              <div className="grid grid-cols-6 gap-2 mt-2 items-center">
                                  {BGN_DENOMINATIONS.slice(7).map((denom, i) => {
                                      const denomIndex = i + 7;
                                      return (
                                          <div key={denomIndex}>
                                              <label className="text-xs text-center block">{denom} лв</label>
                                              <ControlledInput
                                                  type="number"
                                                  min="0"
                                                  value={employeeCashPaymentForMonth[denomIndex] || ''}
                                                  onChange={(val) => handleCashPaymentBillChange(employee.id, viewYear, viewMonth, denomIndex, val)}
                                                  className={`w-full px-1 py-1 text-base text-center border rounded ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'}`}
                                              />
                                          </div>
                                      );
                                  })}
                              </div>
                              <div className="mt-2 text-right font-semibold">
                                  Въведена сума: 
                                  <span className={`ml-2 ${employeeCashPaymentTotal.toFixed(2) !== totalCashSalary.toFixed(2) && `${viewYear}-${viewMonth}` === `${prevYear}-${prevMonth}` ? 'text-red-500' : 'text-green-600'}`}>
                                      {employeeCashPaymentTotal.toFixed(2)} лв
                                  </span>
                              </div>
                          </div>
                      )}

                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )
      })}

      {/* NEW: Global Cash Payment Section */}
       <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4 mt-6`}>
            <GlobalCashPaymentSection
                isDarkMode={isDarkMode}
                employeesByDepartment={employeesByDepartment}
                monthNames={monthNames}
                salaryCashPayments={salaryCashPayments}
                handleSalaryCashChange={handleSalaryCashChange}
                initialYear={prevYear}
                initialMonth={prevMonth}
                calculateTotal={calculateTotal}
            />
        </div>
    </div>
  );
};


// New Turnover Tab Component
const TurnoverTab = ({
  isDarkMode,
  currentYear,
  currentMonth,
  monthNames,
  dailyData,
  turnoverOverrides,
  setTurnoverOverrides,
  isHoliday
}) => {
  const columns = ["Иван Р-С", "Недялко Р-С", "Сини Камъни", "Добрович", "Денонощен", "Хали"];
  const daysOfWeekBG = ["Нд", "Пн", "Вт", "Ср", "Чт", "Пт", "Сб"];
  const turnoverScrollRef = useRef(null);

  // FIX [2]: Implement robust scroll preservation for Turnover tab inputs
  const runTurnoverScrollPreservation = (callback) => {
      const scrollContainer = turnoverScrollRef.current;
      if (!scrollContainer) {
          callback();
          return;
      }
      const savedScrollTop = scrollContainer.scrollTop;
      const savedActiveElement = document.activeElement; // Keep track of focused input

      callback();

      requestAnimationFrame(() => {
          if (turnoverScrollRef.current) {
              turnoverScrollRef.current.scrollTop = savedScrollTop;
          }
          // Restore focus if it was lost
          if (savedActiveElement && typeof savedActiveElement.focus === 'function') {
               savedActiveElement.focus();
          }
      });
  };

  const handleTurnoverChange = (day, colIndex, value) => {
      runTurnoverScrollPreservation(() => {
          const dateKey = `${currentYear}-${currentMonth}-${day}`;
          setTurnoverOverrides(prev => {
              const newOverrides = JSON.parse(JSON.stringify(prev));
              if (!newOverrides[dateKey]) {
                  newOverrides[dateKey] = {};
              }
              newOverrides[dateKey][colIndex] = value;
              return newOverrides;
          });
      });
  };

const turnoverData = useMemo(() => {
    const daysInTargetMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    const data           = [];
    const monthlyTotals  = Array(columns.length).fill(0);

    /* 1 ─ create an empty bucket for every day that WILL be rendered */
    const calculatedDailyTotals = {};
    for (let d = 1; d <= daysInTargetMonth; d++) {
        calculatedDailyTotals[`${currentYear}-${currentMonth}-${d}`] = Array(columns.length).fill(0);
    }

    /* 2 ─ decide which calendar day a transaction really belongs to */
    const resolveTargetKey = (sheetDate /* Date */, name /* string */) => {
        /* a) explicit DD.MM suffix, e.g. "Сини Камъни 03.08" */
        const m = name.trim().match(/(\d{2})\.(\d{2})$/);
        if (m) {
            const day   = parseInt(m[1], 10);
            const month = parseInt(m[2], 10) - 1;     // JS Date month is 0-based
            let year    = sheetDate.getFullYear();    // fallback year

            // Handle cross-year Mondays (e.g. 31.12 on Monday 01.01)
            if (month === 11 && sheetDate.getMonth() === 0)  year -= 1;
            if (month === 0  && sheetDate.getMonth() === 11) year += 1;

            return `${year}-${month}-${day}`;
        }

        /* b) otherwise: put it on the previous calendar day */
        const prev = new Date(sheetDate);
        prev.setDate(prev.getDate() - 1);
        return `${prev.getFullYear()}-${prev.getMonth()}-${prev.getDate()}`;
    };

    /* 3 ─ aggregate every transaction from every sheet in memory */
    Object.entries(dailyData).forEach(([sheetKey, sheet]) => {
        if (!sheet?.income?.length) return;

        const [y, m, d] = sheetKey.split('-').map(Number);
        const sheetDate = new Date(y, m, d);

        sheet.income.forEach(tr => {
            if (!tr || !tr.name) return;

            columns.forEach((colName, colIdx) => {
                if (!flexibleTextSearch(tr.name, colName)) return;

                const targetKey = resolveTargetKey(sheetDate, tr.name);

                /* collect only if the target date is inside the month being viewed */
                const [ty, tm] = targetKey.split('-').map(Number);
                if (ty === currentYear && tm === currentMonth) {
                    if (!calculatedDailyTotals[targetKey]) {
                        calculatedDailyTotals[targetKey] = Array(columns.length).fill(0);
                    }
                    calculatedDailyTotals[targetKey][colIdx] += tr.bgnTotal || 0;
                }
            });
        });
    });

    /* 4 ─ build the rows for the table and the monthly totals */
    for (let day = 1; day <= daysInTargetMonth; day++) {
        const dateKey     = `${currentYear}-${currentMonth}-${day}`;
        const dayTotals   = [];
        const overrides   = turnoverOverrides[dateKey] || {};

        for (let i = 0; i < columns.length; i++) {
            const calcVal     = calculatedDailyTotals[dateKey][i];
            const overrideVal = overrides[i];

            const displayVal =
                overrideVal !== undefined && overrideVal !== ''
                    ? overrideVal
                    : calcVal > 0
                        ? calcVal.toFixed(2)
                        : '';

            dayTotals.push(displayVal);

            const valueForTotal =
                overrideVal !== undefined && overrideVal !== ''
                    ? (parseFloat(overrideVal) || 0)
                    : calcVal;

            monthlyTotals[i] += valueForTotal;
        }

        data.push({ day, totals: dayTotals });
    }

    return { dailyData: data, monthlyTotals };
}, [dailyData, turnoverOverrides, currentMonth, currentYear]);


  return (
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4 h-full flex flex-col`}>
          <h3 className="font-bold text-xl mb-4">
              Оборот за {monthNames[currentMonth]} {currentYear}
          </h3>
          <div className="flex-grow overflow-auto" ref={turnoverScrollRef}>
              <table className="w-full text-base text-center">
                  <thead className={`sticky top-0 z-10 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
                      {/* Monthly Totals Row */}
                      <tr className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'} font-bold`}>
                          <th className="py-3 px-3 text-left" colSpan="2">Общо за месеца</th>
                          {turnoverData.monthlyTotals.map((total, index) => (
                              <th key={index} className="py-3 px-3 text-center text-green-600 font-semibold">
                                  {total > 0 ? total.toFixed(2) : '-'} лв
                              </th>
                          ))}
                      </tr>
                      {/* Column Names Row */}
                      <tr className={`${isDarkMode ? 'bg-gray-900' : 'bg-gray-100'} border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-300'} font-semibold`}>
                          <th className="py-3 px-3 text-left w-20">Дата</th>
                          <th className="py-3 px-3 text-left w-16">Ден</th>
                          {columns.map(name => (
                              <th key={name} className="py-3 px-3 text-center">{name}</th>
                          ))}
                      </tr>
                  </thead>
                  <tbody>
                      {turnoverData.dailyData.map(({ day, totals }) => {
                          const date = new Date(currentYear, currentMonth, day);
                          const dayOfWeek = date.getDay();
                          const weekend = dayOfWeek === 0 || dayOfWeek === 6;
                          const holiday = isHoliday(day);
                          
                          const rowClass = holiday
                              ? (isDarkMode ? 'bg-red-900/40' : 'bg-red-100')
                              : weekend
                              ? (isDarkMode ? 'bg-gray-700/50' : 'bg-gray-100')
                              : '';

                          return (
                              <tr key={day} className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} ${rowClass}`}>
                                  <td className="py-2 px-3 text-left">{`${day.toString().padStart(2, '0')}.${(currentMonth + 1).toString().padStart(2, '0')}.${currentYear}`}</td>
                                  <td className="py-2 px-3 text-left">{daysOfWeekBG[dayOfWeek]}</td>
                                  {totals.map((total, index) => (
                                      <td key={index} className="py-1 px-2 text-center font-mono">
                                          <ControlledInput
                                              type="number"
                                              value={total}
                                              onChange={(val) => handleTurnoverChange(day, index, val)}
                                              className={`w-full px-2 py-2 text-base text-center font-mono ${isDarkMode ? 'bg-transparent border-gray-600 hover:border-gray-400' : 'bg-transparent border-gray-300 hover:border-gray-400'} border-b focus:outline-none focus:ring-1 focus:ring-blue-500 rounded-sm`}
                                              placeholder="-"
                                              blurOnEnter={false}
                                          />
                                      </td>
                                  ))}
                              </tr>
                          );
                      })}
                  </tbody>
              </table>
          </div>
      </div>
  );
};

// Lists Management Component - UPDATED
const ListsTab = ({
  isDarkMode,
  clientList,
  setClientList,
  customHolidays,
  setCustomHolidays,
  transactionGroups,
  setTransactionGroups,
  importFromFile
}) => {
  const [clientSearch, setClientSearch] = useState('');
  const [newClient, setNewClient] = useState({ name: '', group: '' });
  const [editingClient, setEditingClient] = useState(null); // Will hold client object
  const [editValue, setEditValue] = useState({ name: '', group: '' });

  const [newHoliday, setNewHoliday] = useState({ date: '', name: '' });
  const [newGroup, setNewGroup] = useState('');
  const [editingGroup, setEditingGroup] = useState(null);
  const [editGroupValue, setEditGroupValue] = useState('');

  const filteredClients = useMemo(() => {
    if (!clientSearch.trim()) {
      return clientList;
    }
    return clientList.filter(client =>
      flexibleTextSearch(client.name, clientSearch)
    );
  }, [clientList, clientSearch]);
  
  const addClient = () => {
    if (newClient.name.trim() && !clientList.find(c => c.name.toLowerCase() === newClient.name.trim().toLowerCase())) {
        const clientToAdd = { name: newClient.name.trim(), group: newClient.group || '' };
        setClientList(prev => [...prev, clientToAdd].sort((a, b) => a.name.localeCompare(b.name)));
        setNewClient({ name: '', group: '' });
    }
  };
  
  const updateClient = () => {
    if (!editValue.name.trim() || !editingClient) return;
    
    const isDuplicate = clientList.some(c => c.name.toLowerCase() === editValue.name.trim().toLowerCase() && c.name.toLowerCase() !== editingClient.name.toLowerCase());
    if (isDuplicate) {
        alert("Това име на клиент вече съществува.");
        return;
    }

    const updated = clientList.map(c => (c.name === editingClient.name ? { ...c, name: editValue.name.trim(), group: editValue.group } : c));
    setClientList(updated.sort((a, b) => a.name.localeCompare(b.name)));
    setEditingClient(null);
    setEditValue({ name: '', group: '' });
  };
  
  const deleteClient = (clientToDelete) => {
    setClientList(clientList.filter(c => c.name !== clientToDelete.name));
  };
  
  const formatDisplayDate = (isoDate) => {
    if (!isoDate || !/^\d{4}-\d{2}-\d{2}$/.test(isoDate)) return '';
    const [y, m, d] = isoDate.split('-');
    return `${d}/${m}/${y}`;
  };

  const addHoliday = () => {
    if (newHoliday.date && newHoliday.name) {
      const dateRegex = /^(\d{1,2})[\.\/](\d{1,2})[\.\/](\d{4})$/;
      const match = newHoliday.date.match(dateRegex);

      if (!match) {
        alert("Моля, въведете дата във формат ДД/ММ/ГГГГ.");
        return;
      }
      
      const day = match[1].padStart(2, '0');
      const month = match[2].padStart(2, '0');
      const year = match[3];

      const isoDate = `${year}-${month}-${day}`;

      setCustomHolidays([...customHolidays, { date: isoDate, name: newHoliday.name }]);
      setNewHoliday({ date: '', name: '' });
    }
  };
  
  const deleteHoliday = (index) => {
    setCustomHolidays(customHolidays.filter((_, i) => i !== index));
  };
  
  const addGroup = () => {
    if (newGroup.trim() && !transactionGroups.includes(newGroup.trim())) {
      setTransactionGroups([...transactionGroups, newGroup.trim()]);
      setNewGroup('');
    }
  };
  
  const updateGroup = (index) => {
    const updated = [...transactionGroups];
    updated[index] = editGroupValue;
    setTransactionGroups(updated);
    setEditingGroup(null);
    setEditGroupValue('');
  };
  
  const deleteGroup = (index) => {
    setTransactionGroups(transactionGroups.filter((_, i) => i !== index));
  };
  
  return (
    <div className="space-y-4">
      {/* Transaction Groups Management */}
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
        <h3 className="font-bold text-lg mb-4">Групи транзакции</h3>
        
        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={newGroup}
            onChange={(e) => setNewGroup(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addGroup()}
            placeholder="Добави нова група"
            className={`flex-1 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}
          />
          <button
            onClick={addGroup}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            <Plus className="w-5 h-5" />
          </button>
        </div>
        
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {transactionGroups.map((group, index) => (
            <div key={index} className={`flex items-center gap-2 p-2 border rounded ${isDarkMode ? 'border-gray-700 hover:bg-gray-700' : 'hover:bg-gray-50'}`}>
              {editingGroup === index ? (
                <>
                  <input
                    type="text"
                    value={editGroupValue}
                    onChange={(e) => setEditGroupValue(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && updateGroup(index)}
                    className={`flex-1 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1`}
                  />
                  <button
                    onClick={() => updateGroup(index)}
                    className="text-green-500 hover:text-green-700"
                  >
                    ✓
                  </button>
                  <button
                    onClick={() => {
                      setEditingGroup(null);
                      setEditGroupValue('');
                    }}
                    className="text-red-500 hover:text-red-700"
                  >
                    ✗
                  </button>
                </>
              ) : (
                <>
                  <span className="flex-1">{group}</span>
                  <button
                    onClick={() => {
                      setEditingGroup(index);
                      setEditGroupValue(group);
                    }}
                    className="text-blue-500 hover:text-blue-700"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => deleteGroup(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </>
              )}
            </div>
          ))}
        </div>
      </div>
      
      {/* Client Lists */}
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
        <h3 className="font-bold text-lg mb-4">Списък клиенти</h3>
        
        <div className="flex items-center gap-2 mb-2 p-2 border rounded-md">
           <Search className={`w-5 h-5 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
           <input
             type="text"
             value={clientSearch}
             onChange={(e) => setClientSearch(e.target.value)}
             placeholder="Търси клиент..."
             className={`flex-1 bg-transparent focus:outline-none w-full`}
           />
        </div>

        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={newClient.name}
            onChange={(e) => setNewClient({...newClient, name: e.target.value})}
            onKeyPress={(e) => e.key === 'Enter' && addClient()}
            placeholder="Добави нов клиент"
            className={`flex-1 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}
          />
           <select
            value={newClient.group}
            onChange={(e) => setNewClient({...newClient, group: e.target.value})}
            className={`w-1/3 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}
            >
                <option value="">Без група</option>
                {transactionGroups.map(group => <option key={group} value={group}>{group}</option>)}
            </select>
          <button
            onClick={addClient}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            <Plus className="w-5 h-5" />
          </button>
        </div>
        
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {filteredClients.map((client, index) => (
            <div key={index} className={`flex items-center gap-2 p-2 border rounded ${isDarkMode ? 'border-gray-700 hover:bg-gray-700' : 'hover:bg-gray-50'}`}>
              {editingClient && editingClient.name === client.name ? (
                <>
                  <input
                    type="text"
                    value={editValue.name}
                    onChange={(e) => setEditValue({...editValue, name: e.target.value})}
                    className={`flex-1 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1`}
                    autoFocus
                  />
                  <select
                    value={editValue.group}
                    onChange={(e) => setEditValue({...editValue, group: e.target.value})}
                    className={`w-1/3 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-2 py-1`}
                  >
                    <option value="">Без група</option>
                    {transactionGroups.map(group => <option key={group} value={group}>{group}</option>)}
                  </select>
                  <button
                    onClick={updateClient}
                    className="text-green-500 hover:text-green-700"
                  >
                    ✓
                  </button>
                  <button
                    onClick={() => setEditingClient(null)}
                    className="text-red-500 hover:text-red-700"
                  >
                    ✗
                  </button>
                </>
              ) : (
                <>
                  <span className="flex-1">{client.name}</span>
                  {client.group && <span className="text-xs bg-gray-500 text-white px-2 py-1 rounded-full">{client.group}</span>}
                  <button
                    onClick={() => {
                      setEditingClient(client);
                      setEditValue({ name: client.name, group: client.group || '' });
                    }}
                    className="text-blue-500 hover:text-blue-700"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => deleteClient(client)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </>
              )}
            </div>
          ))}
        </div>
      </div>
      
      {/* Custom Holidays */}
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
        <h3 className="font-bold text-lg mb-4">Персонализирани празници (Ден/Месец/Година)</h3>
        
        <div className="grid grid-cols-2 gap-2 mb-4">
           <input
            type="text"
            value={newHoliday.date}
            onChange={(e) => setNewHoliday({...newHoliday, date: e.target.value})}
            placeholder="ДД/ММ/ГГГГ"
            className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}
          />
          <div className="flex gap-2">
            <input
              type="text"
              value={newHoliday.name}
              onChange={(e) => setNewHoliday({...newHoliday, name: e.target.value})}
              placeholder="Име на празник"
              className={`flex-1 ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2`}
            />
            <button
              onClick={addHoliday}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              <Plus className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <div className="space-y-2">
          {customHolidays.map((holiday, index) => (
            <div key={index} className={`flex items-center justify-between p-2 border rounded ${isDarkMode ? 'border-gray-700' : ''}`}>
              <span>{formatDisplayDate(holiday.date)} - {holiday.name}</span>
              <button
                onClick={() => deleteHoliday(index)}
                className="text-red-500 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// History Controls Component
const HistoryControls = ({ isDarkMode, changeHistory, setChangeHistory }) => {
  const [showArchives, setShowArchives] = useState(false);
  const [archives, setArchives] = useState([]);

  const handleClearHistory = () => {
    if (window.confirm('Сигурни ли сте, че искате да изтриете цялата история на промените? Тази операция е необратима.')) {
        setChangeHistory([]);
        alert('Историята е изчистена.');
    }
  };
  
  const saveHistoryOnly = async () => {
    if (!window.electronAPI) return;
    
    const result = await window.electronAPI.saveHistory(changeHistory);
    if (result.success) {
      alert('Историята е запазена успешно!');
    } else {
      alert(`Грешка: ${result.error}`);
    }
  };
  
  const loadHistoryOnly = async () => {
    if (!window.electronAPI) return;
    
    const result = await window.electronAPI.loadHistory();
    if (result.success && result.data) {
        setChangeHistory(result.data);
        alert('Историята е заредена успешно!');
    } else {
        alert('Няма запазена история или възникна грешка.');
    }
  };
  
  const loadArchivesList = async () => {
    if (!window.electronAPI) return;
    
    const list = await window.electronAPI.listHistoryArchives();
    setArchives(list);
    setShowArchives(true);
  };
  
  const loadSpecificArchive = async (filepath) => {
    if (!window.electronAPI) return;
    
    const result = await window.electronAPI.loadHistoryArchive(filepath);
    if (result.success && result.data) {
        if (result.data.entries) {
            // It's an archive with entries
            setChangeHistory(result.data.entries);
            alert(`Архив зареден: ${result.data.entryCount} записа`);
        } else if (Array.isArray(result.data)) {
            // It's a direct array of history
            setChangeHistory(result.data);
            alert('Историята е заредена успешно!');
        }
        setShowArchives(false);
    }
  };
  
  return (
    <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4 mb-4`}>
      <h3 className="font-bold text-lg mb-3">Управление на историята</h3>
      
      <div className="flex gap-2 flex-wrap">
        <button
          onClick={saveHistoryOnly}
          className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center"
        >
          <Save className="w-4 h-4 mr-1" />
          Запази история
        </button>
        
        <button
          onClick={loadHistoryOnly}
          className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center"
        >
          <FolderOpen className="w-4 h-4 mr-1" />
          Зареди история
        </button>
        
        <button
          onClick={loadArchivesList}
          className="bg-purple-500 text-white px-3 py-1 rounded hover:bg-purple-600 flex items-center"
        >
          <List className="w-4 h-4 mr-1" />
          Архиви
        </button>
        
        <button
          onClick={async () => {
            if (window.electronAPI && changeHistory && changeHistory.length > 0) {
              const result = await window.electronAPI.archiveHistory(changeHistory);
              if (result.success) {
                setChangeHistory([]);
                alert(`Архивирано: ${result.filename}`);
              } else {
                alert(`Грешка при архивиране: ${result.error}`);
              }
            } else {
              alert('Няма история за архивиране');
            }
          }}
          className="bg-orange-500 text-white px-3 py-1 rounded hover:bg-orange-600 flex items-center"
        >
          <Download className="w-4 h-4 mr-1" />
          Архивирай и изчисти
        </button>

         <button
          onClick={handleClearHistory}
          className="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 flex items-center"
        >
          <Trash2 className="w-4 h-4 mr-1" />
          Изчисти история
        </button>
      </div>
      
      {showArchives && archives.length > 0 && (
        <div className="mt-4 max-h-60 overflow-y-auto border rounded p-2">
          <h4 className="font-semibold mb-2">Налични архиви:</h4>
          {archives.map((archive) => (
            <div key={archive.filename} className="flex justify-between items-center p-2 hover:bg-gray-100 rounded">
              <div>
                <p className="font-medium">{archive.filename}</p>
                <p className="text-sm text-gray-500">
                  {new Date(archive.modified).toLocaleString('bg-BG')} • {archive.size}
                </p>
              </div>
              <button
                onClick={() => loadSpecificArchive(archive.filepath)}
                className="bg-blue-500 text-white px-2 py-1 rounded text-sm hover:bg-blue-600"
              >
                Зареди
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};


// NEW: History Tab Component - MODIFIED
const HistoryTab = ({
  isDarkMode,
  changeHistory,
  setChangeHistory, // Prop added
  isElectron, // Prop added
  setActiveTab,
  setCurrentYear,
  setCurrentMonth,
  setSelectedDay,
  setScrollToRowId,
  hasScrolledRef
}) => {
  const [historyFilters, setHistoryFilters] = useState({
    searchName: '',
    amountSearch: '',
  });
  const [displayMode, setDisplayMode] = useState('all'); // 'all', 'excludeDeleted', 'onlyDeleted', 'grouped'
  const [expandedGroups, setExpandedGroups] = useState({});

  // Helper to reliably get transaction info from a log entry
  const getTransactionInfo = useCallback((log) => {
    let name = '';
    let bgnTotal = 0;
    let eurTotal = 0;
    let transactionContext = null;

    if (log.details.updatedTransaction) {
      transactionContext = log.details.updatedTransaction;
    } else if (log.type === 'delete' && log.details.deletedData) {
      transactionContext = log.details.deletedData;
    } else if (log.type === 'add' && log.details.addedData) {
      transactionContext = log.details.addedData;
    }

    if (transactionContext) {
      name = transactionContext.name || '';
      bgnTotal = transactionContext.bgnTotal || 0;
      eurTotal = transactionContext.eurTotal || 0;
    }

    if (log.type === 'edit' && log.details.fieldName === 'Име') {
      name = log.details.newValue || name;
    }
    
    return { name, bgnTotal, eurTotal };
  }, []);

  const filteredHistory = useMemo(() => {
    const searchTerm = historyFilters.searchName.trim();
    const amountSearchTerm = historyFilters.amountSearch.trim();
    const byAmount = amountSearchTerm !== '';

    let filtered = [...changeHistory].filter(log => {
      const { name, bgnTotal, eurTotal } = getTransactionInfo(log);
      const nameMatch = searchTerm ? flexibleTextSearch(name, searchTerm) : true;
      const amountMatch = byAmount
        ? (bgnTotal?.toFixed(2).startsWith(amountSearchTerm) || eurTotal?.toFixed(2).startsWith(amountSearchTerm))
        : true;
      return nameMatch && amountMatch;
    });

    if (displayMode === 'excludeDeleted') {
      return filtered.filter(log => log.type !== 'delete');
    }
    if (displayMode === 'onlyDeleted') {
      return filtered.filter(log => log.type === 'delete');
    }

    return filtered;
  }, [changeHistory, historyFilters, getTransactionInfo, displayMode]);

  const groupedHistory = useMemo(() => {
    if (displayMode !== 'grouped') return null;

    const groups = {};
    filteredHistory.forEach(log => {
      const rowId = log.location.rowId;
      if (!groups[rowId]) {
        groups[rowId] = {
          latestTimestamp: log.timestamp,
          location: log.location,
          transactionInfo: getTransactionInfo(log),
          changes: []
        };
      }
      groups[rowId].changes.push(log);
      if (new Date(log.timestamp) > new Date(groups[rowId].latestTimestamp)) {
        groups[rowId].latestTimestamp = log.timestamp;
        // Update transaction info to the one from the latest log
        groups[rowId].transactionInfo = getTransactionInfo(log);
      }
    });

    return Object.values(groups).sort((a, b) => new Date(b.latestTimestamp) - new Date(a.latestTimestamp));
  }, [filteredHistory, displayMode, getTransactionInfo]);

  const handleGoToTransaction = (logEntry) => {
    const { year, month, day, section, rowId } = logEntry.location;
    const uniqueKey = `${year}-${month}-${day}-${section}-${rowId}`;

    hasScrolledRef.current = true;
    setActiveTab('daily');
    setCurrentYear(year);
    setCurrentMonth(month);
    setSelectedDay(day);
    setScrollToRowId(uniqueKey);
  };

  const formatLogEntry = (log) => {
    const { year, month, day, section, rowIndex } = log.location;
    const dateStr = `${day}.${month + 1}.${year}`;
    const sectionStr = section === 'income' ? 'Приходи' : 'Разходи';
    
    switch(log.type) {
      case 'edit':
        const oldValueStr = log.details.oldValue !== undefined && log.details.oldValue !== '' ? `"${log.details.oldValue}"` : 'празно';
        const newValueStr = log.details.newValue !== undefined && log.details.newValue !== '' ? `"${log.details.newValue}"` : 'празно';
        return `Промяна на ред ${rowIndex + 1} в "${sectionStr}" за ${dateStr}: полето "${log.details.fieldName}" е променено от ${oldValueStr} на ${newValueStr}.`;
      case 'add':
        return `Добавен нов ред на позиция ${rowIndex + 1} в "${sectionStr}" за ${dateStr}.`;
      case 'delete':
        const deletedData = log.details.deletedData || {};
        const name = deletedData.name || 'празно';
        const bgn = (deletedData.bgnTotal || 0).toFixed(2);
        const eur = (deletedData.eurTotal || 0).toFixed(2);
        return `Изтрит ред от поз. ${rowIndex + 1} в "${sectionStr}" за ${dateStr}. (Име: "${name}", Сума: ${bgn}лв / ${eur}€)`;
      default:
        return 'Неизвестно действие';
    }
  };

  const renderLogEntry = (log) => {
    const { name, bgnTotal, eurTotal } = getTransactionInfo(log);
    const totals = [];
    if (bgnTotal) totals.push(`${bgnTotal.toFixed(2)} лв`);
    if (eurTotal) totals.push(`€${eurTotal.toFixed(2)}`);
    const totalString = totals.join(' / ');
    
    const rowClass = log.type === 'delete' 
      ? (isDarkMode ? 'bg-red-900/50' : 'bg-red-100')
      : (isDarkMode ? 'bg-gray-700/50' : 'bg-gray-100');

    return (
      <div key={log.id} className={`p-3 rounded-md flex justify-between items-start ${rowClass}`}>
        <div className="flex-grow">
          <div className="text-xs text-gray-500 font-mono">{new Date(log.timestamp).toLocaleString('bg-BG')}</div>
          <p className="text-sm">{formatLogEntry(log)}</p>
          {(name || totalString) &&
            <div className="text-xs text-gray-400 mt-1 pl-2 border-l-2 border-gray-500">
              Име: "{name || 'няма'}", Сума: {totalString || '0.00 лв'}
            </div>
          }
        </div>
        {log.type !== 'delete' && (
          <button
            onClick={() => handleGoToTransaction(log)}
            className="px-4 py-2 text-blue-500 hover:text-blue-700 rounded-full hover:bg-blue-100 ml-4 flex-shrink-0"
            title="Отиди до транзакцията"
          >
            <Eye className="w-5 h-5" />
          </button>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* MODIFICATION START: Insert HistoryControls component */}
      {isElectron && (
        <HistoryControls
          isDarkMode={isDarkMode}
          changeHistory={changeHistory}
          setChangeHistory={setChangeHistory}
        />
      )}
      {/* MODIFICATION END */}

      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4`}>
        <h3 className="font-bold text-lg mb-3 flex items-center">
          <Filter className="w-5 h-5 mr-2" />
          Търсене в историята
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
          <ControlledInput
            type="text"
            value={historyFilters.searchName}
            onChange={(value) => setHistoryFilters(prev => ({ ...prev, searchName: value }))}
            placeholder="Търси по име..."
            className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2 w-full`}
          />
          <ControlledInput
            type="number"
            value={historyFilters.amountSearch}
            onChange={value => setHistoryFilters(p => ({ ...p, amountSearch: value }))}
            placeholder="Търси по сума..."
            className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white'} border rounded px-3 py-2 w-full`}
          />
        </div>
        
        <div className="flex flex-wrap gap-2">
          <button onClick={() => setDisplayMode('all')} className={`px-3 py-1 text-sm rounded ${displayMode === 'all' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-black hover:bg-gray-300'}`}>Всички</button>
          <button onClick={() => setDisplayMode('grouped')} className={`px-3 py-1 text-sm rounded ${displayMode === 'grouped' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-black hover:bg-gray-300'}`}>Групирай по ред</button>
          <button onClick={() => setDisplayMode('onlyDeleted')} className={`px-3 py-1 text-sm rounded ${displayMode === 'onlyDeleted' ? 'bg-red-600 text-white' : 'bg-gray-200 text-black hover:bg-gray-300'}`}>Само изтрити</button>
          <button onClick={() => setDisplayMode('excludeDeleted')} className={`px-3 py-1 text-sm rounded ${displayMode === 'excludeDeleted' ? 'bg-yellow-500 text-white' : 'bg-gray-200 text-black hover:bg-gray-300'}`}>Без изтрити</button>
        </div>
      </div>

      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-4 flex-grow overflow-y-auto`}>
        <h3 className="font-bold text-lg mb-4 flex items-center">
          <RefreshCw className="w-5 h-5 mr-2" />
          История на промените
        </h3>
        <div className="flex-grow">
          {displayMode === 'grouped' ? (
            // Grouped view
            <div className="space-y-2">
              {groupedHistory.map(group => {
                const { name, bgnTotal, eurTotal } = group.transactionInfo;
                const totals = [];
                if (bgnTotal) totals.push(`${bgnTotal.toFixed(2)} лв`);
                if (eurTotal) totals.push(`€${eurTotal.toFixed(2)}`);
                const totalString = totals.join(' / ');
                const isExpanded = !!expandedGroups[group.location.rowId];

                return (
                  <div key={group.location.rowId}>
                    <div 
                      onClick={() => setExpandedGroups(prev => ({...prev, [group.location.rowId]: !prev[group.location.rowId]}))}
                      className={`p-3 rounded-md flex justify-between items-center cursor-pointer ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
                    >
                      <div className="flex items-center gap-3">
                        <span className="p-1">{isExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}</span>
                        <div>
                          <div className="font-semibold">{name || 'Без име'}</div>
                          <div className="text-sm text-gray-500">
                            {group.changes.length} промени • Последна: {new Date(group.latestTimestamp).toLocaleString('bg-BG')}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-mono">{totalString || '0.00 лв'}</div>
                        <div className="text-sm text-gray-500">
                          Ред {group.location.rowIndex + 1} в {group.location.section === 'income' ? 'Приходи' : 'Разходи'}
                        </div>
                      </div>
                    </div>
                    {isExpanded && (
                      <div className="pl-8 pt-2 space-y-2 border-l-2 ml-4 mt-1 border-gray-400">
                        {group.changes.sort((a,b) => new Date(b.timestamp) - new Date(a.timestamp)).map(log => renderLogEntry(log))}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          ) : (
            // Default flat list view
            <div className="space-y-3">
              {filteredHistory.length === 0 ? (
                <p className="text-center text-gray-500 py-8">Няма намерени промени, отговарящи на търсенето.</p>
              ) : (
                filteredHistory.map(log => renderLogEntry(log))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};


/** Returns the previous (or next) valid calendar date */
function shiftDay({ day, month, year }, delta = 1) {
  const d = new Date(year, month, day);
  d.setDate(d.getDate() + delta);
  return { day: d.getDate(), month: d.getMonth(), year: d.getFullYear() };
}

/* --------------------------------------------------------------------
   5.  MAIN APP  (CashManagementApp)
   ------------------------------------------------------------------ */

const AppWrapper = () => {
    return (
        <ToastProvider>
            <CashManagementApp />
        </ToastProvider>
    );
};

const CashManagementApp = () => {
  // State management
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [selectedDay, setSelectedDay] = useState(new Date().getDate());
  const [activeTab, setActiveTab] = useState('daily');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isEnglish, setIsEnglish] = useState(false);
  const [nameColumnWidth, setNameColumnWidth] = useState(250); // Increased width
  const [isResizing, setIsResizing] = useState(false);
  const [columnWidths, setColumnWidths] = useState({});
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [viewMode, setViewMode] = useState('bgn'); // UPDATED: Default view mode set to 'bgn'
  const [frozenHeaders, setFrozenHeaders] = useState({
    income: false,
    expense: false,
    balance: false
  });
  // Analysis filter state
  const [analysisFilters, setAnalysisFilters] = useState({
    searchName: '',
    selectedGroups: [],
    excludedGroups: [],
    dateRange: 'month',
    amountSearch: '',
    excludeCarryOver: true, // Changed to true to make "Изключи „Пренос"" checked by default
    showOnlyAutoSalaries: false, // REQUEST 1: New filter state
  });
  const resizeRef = useRef(null);
  const scrollContainerRef = useRef(null);
  
  const hasScrolledRef = useRef(false);

  // State for scrolling to a specific transaction from the Analysis tab
  const [scrollToRowId, setScrollToRowId] = useState(null);
  const [highlightedRowId, setHighlightedRowId] = useState(null);

  // NEW: State to trigger a manual refresh of the daily sheet
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // NEW: State for focus mode and resizable panel
  const [isFocusMode, setIsFocusMode] = useState(false);
  const [panelHeight, setPanelHeight] = useState(320); // Default height for the resizable panel
  
  // NEW: Get toast function from context
  const { showToast } = useToast();

  const [tabZoom, setTabZoom] = useState(() => storageManager.load(
    'cashManagement_tabZoom',
    { daily:100, analysis:100, salary:100, employees:100, turnover:100, lists:100, history: 100 }
  ));
  
  // NEW: Add isSaving state
  const [isSaving, setIsSaving] = useState(false);

  const changeZoom = (delta) => {
    setTabZoom(prev => {
      const z = Math.max(70, Math.min(200, (prev[activeTab] || 100) + delta));
      const next = { ...prev, [activeTab]: z };
      storageManager.save('cashManagement_tabZoom', next);
      return next;
    });
  };

  // Initialize data from localStorage using StorageManager
  const [dailyData, setDailyData] = useState(() => storageManager.load('cashManagement_dailyData', {}));
  
  const [clientList, setClientList] = useState(() => storageManager.load('cashManagement_clients', [
        { name: 'ВЪРШИНА СУНА', group: 'Продажби' },
        { name: 'СИНИ КАМЪНИ', group: 'Продажби' },
        { name: 'ДОБРОВИН', group: 'Продажби' },
        { name: 'ДЕНОШОЩЕН', group: 'Продажби' },
        { name: 'КЛИЕНТ', group: '' }
    ]));
  
  const [employeeList, setEmployeeList] = useState(() => storageManager.load('cashManagement_employees', [
      { id: 1, name: 'Иван Иванов', totalSalary: 1200, bankSalary: 800, socialSecurity: 150, department: 'Администрация', annualLeave: 20 },
      { id: 2, name: 'Петър Петров', totalSalary: 1500, bankSalary: 1000, socialSecurity: 200, department: 'Цех', annualLeave: 22 }
    ]));
  
  const [customHolidays, setCustomHolidays] = useState(() => storageManager.load('cashManagement_holidays', []));
  
  const [employeeTransactions, setEmployeeTransactions] = useState(() => storageManager.load('cashManagement_employeeTransactions', {}));
  
  const [transactionGroups, setTransactionGroups] = useState(() => storageManager.load('cashManagement_transactionGroups', INITIAL_TRANSACTION_GROUPS));
  
  const [departments, setDepartments] = useState(() => storageManager.load('cashManagement_departments', INITIAL_DEPARTMENTS));
  
  const [turnoverOverrides, setTurnoverOverrides] = useState(() => storageManager.load('cashManagement_turnoverOverrides', {}));

  const [salaryCashPayments, setSalaryCashPayments] = useState(() => storageManager.load('cashManagement_salaryCashPayments', {}));

  const [leaveData, setLeaveData] = useState(() => storageManager.load('cashManagement_leaveData', {}));

  const [salaryInputs, setSalaryInputs] = useState(() => storageManager.load('cashManagement_salaryInputs', {}));

  const [changeHistory, setChangeHistory] = useState(() => storageManager.load('cashManagement_changeHistory', []));

    // Check if running in Electron
    const isElectron = typeof window !== 'undefined' && window.electronAPI !== undefined;

    // In your CashManagementApp component, add this useEffect at the beginning:
    useEffect(() => {
        // Prevent Electron throttling issues
        preventThrottling();
    }, []);

    // Collect all app data for saving
    const collectAllAppData = useCallback(() => {
        const allTransactions = []; // Re-calculating here to ensure it's up-to-date
        Object.entries(dailyData).forEach(([key, dayData]) => {
          if (!dayData || !dayData.income || !dayData.expense) return;
          const [year, month, day] = key.split('-').map(Number);
          dayData.income.forEach(t => t && t.id && t.name && allTransactions.push({ ...t, type: 'income', year, month, day, date: new Date(year, month, day), uniqueKey: `${key}-income-${t.id}` }));
          dayData.expense.forEach(t => t && t.id && t.name && allTransactions.push({ ...t, type: 'expense', year, month, day, date: new Date(year, month, day), uniqueKey: `${key}-expense-${t.id}` }));
        });

        return {
            version: '1.0',
            savedAt: new Date().toISOString(),
            data: {
                dailyData,
                clientList,
                employeeList,
                customHolidays,
                employeeTransactions,
                transactionGroups,
                departments,
                turnoverOverrides,
                salaryCashPayments,
                leaveData,
                salaryInputs,
            },
            metadata: {
                currentMonth,
                currentYear,
                selectedDay,
                totalTransactions: allTransactions.length,
                totalHistoryEntries: changeHistory.length,
            }
        };
    }, [
        dailyData, clientList, employeeList, customHolidays,
        employeeTransactions, transactionGroups, departments,
        turnoverOverrides, salaryCashPayments, leaveData,
        salaryInputs, changeHistory, currentMonth, currentYear,
        selectedDay
    ]);

    // --- INTEGRATION: AUTO-SAVE every 24 hours ---
    const [lastAutoSave, setLastAutoSave] = useState(null);

    useEffect(() => {
        if (!isElectron) return;

        const autoSave = async () => {
            try {
                const result = await window.electronAPI.autoSave(JSON.stringify(collectAllAppData()));
                if (result.success) {
                    setLastAutoSave(new Date());
                    console.log('Auto-saved:', result.filename);
                }
            } catch (error) {
                console.error('Auto-save error:', error);
            }
        };

        // Initial save after 1 minute
        const initialTimer = setTimeout(autoSave, 60000);
        
        // Then save every 24 hours
        const interval = setInterval(autoSave, 24 * 60 * 60 * 1000);

        return () => {
            clearTimeout(initialTimer);
            clearInterval(interval);
        };
    }, [collectAllAppData, isElectron]);
    
    // START UPDATE: restoreStateFromData and its helpers
    // Replace your restoreStateFromData function with this optimized version:
    const restoreStateFromData = async (loadedData) => {
        try {
            if (!loadedData || !loadedData.data) {
                throw new Error("Файлът за възстановяване е непълен или повреден.");
            }
            
            const data = loadedData.data;
            const loadingToast = showToast('Зареждане на данните...', 'info');
            
            // Keep renderer active during restore
            keepRendererActive();
            
            // Use the optimized restore function
            await performantStateRestore(data, {
                setDailyData,
                setClientList,
                setEmployeeList,
                setCustomHolidays,
                setEmployeeTransactions,
                setTransactionGroups,
                setDepartments,
                setTurnoverOverrides,
                setSalaryCashPayments,
                setLeaveData,
                setSalaryInputs,
                setChangeHistory
            });
            
            // Update history
            const newHistory = [JSON.parse(JSON.stringify(data.dailyData || {}))];
            setHistory(newHistory);
            setHistoryIndex(0);
            
            loadingToast.update('Данните са заредени успешно!', 'success');
            
            // Defer any heavy recalculation
            setTimeout(() => {
                if (needsRecalculation(data.dailyData)) {
                    handleGlobalRefreshAsync();
                }
            }, 100);
            
        } catch (error) {
            console.error("Error restoring data:", error);
            throw error;
        }
    };


    // Helper function to check if recalculation is needed
    const needsRecalculation = (dailyData) => {
        // Check if any carry-over rows need updating
        const keys = Object.keys(dailyData || {});
        for (const key of keys) {
            const dayData = dailyData[key];
            if (dayData?.income?.[0]?.isCarryOver === undefined) {
                return true; // Old data format, needs update
            }
        }
        return false;
    };
    // END UPDATE
    
    // ============================================
    // 11. Fix for Electron file operations - Add to your React component
    // ============================================

    // START UPDATE: handleQuickSaveEnhanced
    const handleQuickSaveEnhanced = async () => {
        if (!isElectron) return;
        
        const savingToast = showToast('Запазване...', 'info');
        
        try {
            const allData = collectAllAppData();
            
            if (!allData || !allData.data) {
                throw new Error('Invalid data structure');
            }
            
            // Add timestamp to make each save unique
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const jsonString = await stringifyJSONAsync({ ...allData, timestamp });
            const result     = await window.electronAPI.quickSave(jsonString);
            
            if (result.success) {
                savingToast.update(`Успешно запазено! ${result.filename}`, 'success');
                storageManager.save('lastSuccessfulSave', new Date().toISOString());
                
                // Log the save action
                console.log(`Quick save completed: ${result.filename}`);
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('Quick save error:', error);
            savingToast.update(`Грешка: ${error.message}`, 'error');
            
            try {
                storageManager.save('emergencyBackup', collectAllAppData());
                savingToast.update('Запазено локално като резервно копие', 'warning');
            } catch (e) {
                console.error('Failed to save emergency backup:', e);
            }
        }
    };
    // END UPDATE

    // Also update your handleLoadBackupEnhanced function:
    const handleLoadBackupEnhanced = async (filepath) => {
        if (!isElectron) return;
        
        const loadingToast = showToast('Зареждане...', 'info');
        
        try {
            // Keep renderer active before the load operation
            keepRendererActive();
            
            const result = await window.electronAPI.loadBackup(filepath);
            
            if (!result.success) {
                if (result.error === 'cancelled') {
                    loadingToast.remove();
                    return;
                }
                throw new Error(result.error);
            }
            
            // Parse in chunks to prevent blocking
            loadingToast.update('Обработка на данните...', 'info');
            const loadedData = await parseJSONAsync(result.dataString);
            
            if (!loadedData || !loadedData.data) {
                throw new Error('Невалиден файл');
            }
            
            const version = loadedData.version || '0.0';
            if (version !== '1.0') {
                const confirm = window.confirm(
                    `Файлът е от версия ${version}. Възможни са проблеми със съвместимостта. Продължаване?`
                );
                if (!confirm) {
                    loadingToast.remove();
                    return;
                }
            }
            
            // Use the optimized restore
            await restoreStateFromData(loadedData);
            
        } catch (error) {
            console.error('Load error:', error);
            loadingToast.update(`Грешка: ${error.message}`, 'error');
            
            const emergencyData = storageManager.load('emergencyBackup', null);
            if (emergencyData) {
                const useEmergency = window.confirm(
                    'Има локално резервно копие. Искате ли да го заредите?'
                );
                if (useEmergency) {
                    await restoreStateFromData(emergencyData);
                    loadingToast.update('Заредено от резервно копие', 'warning');
                }
            }
        }
    };


    const handleLoadLast = async () => {
        if (!isElectron) return;
        
        try {
            const list = await window.electronAPI.listBackups();
            if (!list || list.length === 0) {
                showToast('Няма намерени архивни копия!', 'warning');
                return;
            }
            
            const mostRecent = list.find(f => f.filename.includes('quicksave')) || list[0];
            
            if (window.confirm(`Сигурни ли сте, че искате да заредите архивно копие от ${new Date(mostRecent.modified).toLocaleString('bg-BG')}? Всички незапазени промени ще бъдат загубени.`)) {
                await handleLoadBackupEnhanced(mostRecent.filepath);
            }
        } catch (error) {
            console.error("Failed to load last backup:", error);
            showToast(`Грешка при зареждане на архива: ${error.message}`, 'error');
        }
    };

    // History archive effect (every 3 months)
    useEffect(() => {
      if (!isElectron) return;
      
      const checkAndArchiveHistory = async () => {
        const lastArchive = localStorage.getItem('lastHistoryArchive');
        const now = new Date();
        const threeMonthsAgo = new Date(now);
        threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
        
        if (!lastArchive || new Date(lastArchive) < threeMonthsAgo) {
          if (changeHistory.length > 100) { // Only archive if significant history
            const result = await window.electronAPI.archiveHistory(changeHistory);
            
            if (result.success) {
              // Clear history after successful archive
              setChangeHistory([]);
              localStorage.setItem('lastHistoryArchive', now.toISOString());
              showToast(`Историята е автоматично архивирана: ${result.filename}`, 'info');
            }
          }
        }
      };
      
      checkAndArchiveHistory();
      
      const interval = setInterval(checkAndArchiveHistory, 30 * 24 * 60 * 60 * 1000);
      
      return () => clearInterval(interval);
    }, [changeHistory, isElectron, showToast]);

  
  const saveToHistory = useCallback((newData) => {
    const newHistoryState = produce(history, draft => {
        const newHistory = draft.slice(0, historyIndex + 1);
        newHistory.push(newData);
        return newHistory;
    });
    setHistory(newHistoryState);
    setHistoryIndex(newHistoryState.length - 1);
  }, [history, historyIndex]);


const logChange = useCallback((changeDetails) => {
      // FIX: Create a deep, "clean" copy of the details to remove any Immer proxies
      // before it gets stored in the React state. This is the crucial step.
      const cleanDetails = JSON.parse(JSON.stringify(changeDetails));

      const newEntry = {
          id: Math.random(),
          timestamp: new Date().toISOString(),
          ...cleanDetails
      };

      // Now, update the state using produce for safety and consistency.
      setChangeHistory(
          produce(draft => {
              draft.unshift(newEntry); // Prepend the new, clean entry
              // Limit the history size
              if (draft.length > 500) {
                  draft.splice(500);
              }
          })
      );
  }, []); 


  const undo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setDailyData(history[historyIndex - 1]);
    }
  }, [history, historyIndex]);

  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setDailyData(history[historyIndex + 1]);
    }
  }, [history, historyIndex]);

  useEffect(() => {
    if (history.length === 0) {
      setHistory([JSON.parse(JSON.stringify(dailyData))]);
      setHistoryIndex(0);
    }
  }, [dailyData, history.length]);

  // Use StorageManager for all state persistence
  if (!isElectron) { 
    useEffect(() => { storageManager.save('cashManagement_dailyData', dailyData); }, [dailyData]);
    useEffect(() => { storageManager.save('cashManagement_clients', clientList); }, [clientList]);
    useEffect(() => { storageManager.save('cashManagement_employees', employeeList); }, [employeeList]);
    useEffect(() => { storageManager.save('cashManagement_holidays', customHolidays); }, [customHolidays]);
    useEffect(() => { storageManager.save('cashManagement_employeeTransactions', employeeTransactions); }, [employeeTransactions]);
    useEffect(() => { storageManager.save('cashManagement_transactionGroups', transactionGroups); }, [transactionGroups]);
    useEffect(() => { storageManager.save('cashManagement_departments', departments); }, [departments]);
    useEffect(() => { storageManager.save('cashManagement_turnoverOverrides', turnoverOverrides); }, [turnoverOverrides]);
    useEffect(() => { storageManager.save('cashManagement_salaryCashPayments', salaryCashPayments); }, [salaryCashPayments]);
    useEffect(() => { storageManager.save('cashManagement_leaveData', leaveData); }, [leaveData]);
    useEffect(() => { storageManager.save('cashManagement_salaryInputs', salaryInputs); }, [salaryInputs]);
    useEffect(() => { storageManager.save('cashManagement_changeHistory', changeHistory); }, [changeHistory]);
  }


  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
  const monthNames = isEnglish ? 
    ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'] :
    ['Януари', 'Февруари', 'Март', 'Април', 'Май', 'Юни', 'Юли', 'Август', 'Септември', 'Октомври', 'Ноември', 'Декември'];
  const monthNamesShort = ['Яну', 'Фев', 'Мар', 'Апр', 'Май', 'Юни', 'Юли', 'Авг', 'Сеп', 'Окт', 'Ное', 'Дек'];
  
  const actualMonth = new Date().getMonth();
  const actualYear = new Date().getFullYear();
  const isCurrentMonth = currentMonth === actualMonth && currentYear === actualYear;

  const isWeekend = (day) => {
    const date = new Date(currentYear, currentMonth, day);
    const dayOfWeek = date.getDay();
    return dayOfWeek === 0 || dayOfWeek === 6;
  };

  const isHoliday = useCallback((day, month = currentMonth, year = currentYear) => {
    const dateStr = `${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const yearHolidays = BULGARIAN_HOLIDAYS[year] || [];
    return yearHolidays.some(h => h.date === dateStr) || 
           customHolidays.some(h => {
             const [hYear, hMonth, hDay] = h.date.split('-');
             return parseInt(hDay) === day && parseInt(hMonth) - 1 === month && parseInt(hYear) === year;
           });
  }, [customHolidays, currentMonth, currentYear]);

  const calculateWorkingDays = useCallback((department, month, year) => {
    const deptConfig = departments[department];
    if (!deptConfig) return 22;
    
    const daysInTargetMonth = new Date(year, month + 1, 0).getDate();
    let workingDays = 0;
    
    for (let day = 1; day <= daysInTargetMonth; day++) {
      const date = new Date(year, month, day);
      const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
      
      if (isHoliday(day, month, year)) continue;
      
      if (deptConfig.workDays.includes(dayOfWeek)) {
        workingDays += 1;
      } else if (deptConfig.halfDays.includes(dayOfWeek)) {
        workingDays += 0.5;
      }
    }
    
    return workingDays;
  }, [departments, customHolidays, isHoliday]);
  
    const allTransactions = useMemo(() => {
      const transactions = [];
      const processedKeys = new Set();
      
      Object.entries(dailyData).forEach(([key, dayData]) => {
        if (!dayData || !dayData.income || !dayData.expense) {
          return;
        }
    
        const [year, month, day] = key.split('-').map(Number);
        
        dayData.income.forEach(t => {
          if (t && t.id && t.name) {
            const transactionKey = `${key}-income-${t.id}`;
            if (!processedKeys.has(transactionKey)) {
              processedKeys.add(transactionKey);
              transactions.push({
                ...t,
                type: 'income',
                year,
                month,
                day,
                date: new Date(year, month, day),
                isCarryOver: t.isCarryOver || false,
                uniqueKey: transactionKey
              });
            }
          }
        });
        
        dayData.expense.forEach(t => {
          if (t && t.id && t.name) {
            const transactionKey = `${key}-expense-${t.id}`;
            if (!processedKeys.has(transactionKey)) {
              processedKeys.add(transactionKey);
              transactions.push({
                ...t,
                type: 'expense',
                year,
                month,
                day,
                date: new Date(year, month, day),
                uniqueKey: transactionKey
              });
            }
          }
        });
      });
      
      return transactions;
    }, [dailyData]);


  const calculateTotal = (denominations, counts) => {
    return denominations.reduce((sum, denom, index) => sum + (denom * (counts[index] || 0)), 0);
  };

  const calculateDayBalance = (key, dataContext) => {
    const sourceData = dataContext || dailyData;
    const dayData = sourceData[key];
    if (!dayData) return { bgnBalance: 0, eurBalance: 0, bgnDenominations: {}, eurDenominations: {} };

    let bgnIncome = 0, eurIncome = 0;
    let bgnExpense = 0, eurExpense = 0;
    const bgnDenominations = {};
    BGN_DENOMINATIONS.forEach((_, i) => bgnDenominations[i] = 0);
    const eurDenominations = {};
    EUR_DENOMINATIONS.forEach((_, i) => eurDenominations[i] = 0);
    
    dayData.income?.forEach(transaction => {
      if (transaction) {
        bgnIncome += transaction.bgnTotal || 0;
        eurIncome += transaction.eurTotal || 0;
        Object.entries(transaction.bgnCounts || {}).forEach(([index, count]) => {
          bgnDenominations[index] = (bgnDenominations[index] || 0) + (count || 0);
        });
        Object.entries(transaction.eurCounts || {}).forEach(([index, count]) => {
          eurDenominations[index] = (eurDenominations[index] || 0) + (count || 0);
        });
      }
    });

    dayData.expense?.forEach(transaction => {
       if (transaction) {
        bgnExpense += transaction.bgnTotal || 0;
        eurExpense += transaction.eurTotal || 0;
        Object.entries(transaction.bgnCounts || {}).forEach(([index, count]) => {
          bgnDenominations[index] = (bgnDenominations[index] || 0) - (count || 0);
        });
        Object.entries(transaction.eurCounts || {}).forEach(([index, count]) => {
          eurDenominations[index] = (eurDenominations[index] || 0) - (count || 0);
        });
      }
    });

    return {
      bgnBalance: bgnIncome - bgnExpense,
      eurBalance: eurIncome - eurExpense,
      bgnDenominations,
      eurDenominations
    };
  };

  const initializeDayData = useCallback((dayOfWeek) => {
    const incomeRowCount = dayOfWeek === 1 ? 30 : 20;
    const expenseRowCount = 15;

    return {
        income: Array(incomeRowCount).fill(null).map(() => ({
            id: Math.random(),
            name: '',
            group: '',
            bgnCounts: {},
            eurCounts: {},
            bgnTotal: 0,
            eurTotal: 0,
            isCarryOver: false
        })),
        expense: Array(expenseRowCount).fill(null).map(() => ({
            id: Math.random(),
            name: '',
            group: '',
            bgnCounts: {},
            eurCounts: {},
            bgnTotal: 0,
            eurTotal: 0,
            isCarryOver: false
        }))
    };
  }, []);

  // ##### FIX STARTS HERE #####
  // This useEffect now uses a functional update to prevent re-render loops when loading data.
  useEffect(() => {
    if (hasScrolledRef.current) {
        return;
    }
      
    setDailyData(currentDailyData => {
        const getPreviousDayInfo = (day, month, year) => {
            let searchDate = new Date(year, month, day);
            searchDate.setDate(searchDate.getDate() - 1);

            for (let i = 0; i < 365; i++) {
                const prevKey = `${searchDate.getFullYear()}-${searchDate.getMonth()}-${searchDate.getDate()}`;
                if (currentDailyData[prevKey]) {
                    const balance = calculateDayBalance(prevKey, currentDailyData);
                    if (balance.bgnBalance !== 0 || balance.eurBalance !== 0) {
                        return { 
                            balance,
                            date: `${searchDate.getDate()}.${searchDate.getMonth() + 1}`
                        };
                    }
                }
                searchDate.setDate(searchDate.getDate() - 1);
            }
            return null;
        };

        const prevDayInfo = getPreviousDayInfo(selectedDay, currentMonth, currentYear);
        const key = `${currentYear}-${currentMonth}-${selectedDay}`;

        return produce(currentDailyData, draft => {
            const date = new Date(currentYear, currentMonth, selectedDay);
            const dayOfWeek = date.getDay();
            
            if (!draft[key] || (dayOfWeek === 1 && draft[key].income.length < 30) || (dayOfWeek !== 1 && draft[key].income.length < 20)) {
                draft[key] = initializeDayData(dayOfWeek);
            }
            
            const dayData = draft[key];
            const income = dayData.income;

            if (prevDayInfo) {
                const carryOverRow = {
                    ...income[0],
                    name: `Пари от ${prevDayInfo.date}`,
                    group: 'Пренос',
                    bgnCounts: prevDayInfo.balance.bgnDenominations || {},
                    eurCounts: prevDayInfo.balance.eurDenominations || {},
                    bgnTotal: prevDayInfo.balance.bgnBalance,
                    eurTotal: prevDayInfo.balance.eurBalance,
                    isCarryOver: true,
                };
                income[0] = carryOverRow;
            } else if (income[0] && income[0].isCarryOver) {
                income[0] = { ...initializeDayData(dayOfWeek).income[0], id: income[0].id };
            }

            const prePopulateRow = (index, name) => {
                if (income[index] && income[index].name === '') {
                    income[index].name = name;
                }
            };

            if (dayOfWeek === 1) { // Monday
                const friday = new Date(date); friday.setDate(date.getDate() - 3);
                const saturday = new Date(date); saturday.setDate(date.getDate() - 2);
                const sunday = new Date(date); sunday.setDate(date.getDate() - 1);

                const formatDate = (d) => `${d.getDate().toString().padStart(2, '0')}.${(d.getMonth() + 1).toString().padStart(2, '0')}`;
                
                prePopulateRow(1, `Сини Камъни ${formatDate(friday)}`);
                prePopulateRow(2, `Денонощен ${formatDate(friday)}`);
                prePopulateRow(3, `Добрович ${formatDate(friday)}`);
                prePopulateRow(4, `Сини Камъни ${formatDate(saturday)}`);
                prePopulateRow(5, `Денонощен ${formatDate(saturday)}`);
                prePopulateRow(6, `Добрович ${formatDate(saturday)}`);
                prePopulateRow(7, `Сини Камъни ${formatDate(sunday)}`);
                prePopulateRow(8, `Денонощен ${formatDate(sunday)}`);
                prePopulateRow(9, `Добрович ${formatDate(sunday)}`);
                prePopulateRow(10, `Хали ${formatDate(friday)}`);
                prePopulateRow(11, `Хали ${formatDate(saturday)}`);
            }

            if (dayOfWeek >= 2 && dayOfWeek <= 5) {
                prePopulateRow(1, 'Сини Камъни');
                prePopulateRow(2, 'Денонощен');
                prePopulateRow(3, 'Добрович');
            }

            if (dayOfWeek >= 3 && dayOfWeek <= 5) {
                prePopulateRow(4, 'Хали');
            }
        });
    });
  }, [selectedDay, currentMonth, currentYear, refreshTrigger, initializeDayData]);
  // ##### FIX ENDS HERE #####


    const handleSalaryCashChange = useCallback((employeeId, year, month, payments) => {
        setSalaryCashPayments(prev => {
            const newEmployeeData = { ...(prev[employeeId] || {}) };
            const key = `${year}-${month}`;

            if (Object.values(payments).some(count => count > 0)) {
                newEmployeeData[key] = payments;
            } else {
                delete newEmployeeData[key];
            }
            
            const newData = { ...prev, [employeeId]: newEmployeeData };

            if (Object.keys(newEmployeeData).length === 0) {
                delete newData[employeeId];
            }

            return newData;
        });
    }, []);

    const handleLeaveDataChange = useCallback((employeeId, year, month, days) => {
        setLeaveData(prev => {
            const newData = JSON.parse(JSON.stringify(prev));
            if (!newData[employeeId]) newData[employeeId] = {};
            if (!newData[employeeId][year]) newData[employeeId][year] = {};
            
            if (days > 0) {
                newData[employeeId][year][month] = days;
            } else {
                if (newData[employeeId]?.[year]?.[month]) {
                    delete newData[employeeId][year][month];
                }
                if (newData[employeeId]?.[year] && Object.keys(newData[employeeId][year]).length === 0) {
                    delete newData[employeeId][year];
                }
                if (newData[employeeId] && Object.keys(newData[employeeId]).length === 0) {
                    delete newData[employeeId];
                }
            }
            return newData;
        });
    }, []);

  const runWithScrollPreservation = (callback) => {
    if (hasScrolledRef.current) return callback();

    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) {
        callback();
        return;
    }
    const savedScrollTop = scrollContainer.scrollTop;
    const activeElement = document.activeElement;
    
    callback();
    
    requestAnimationFrame(() => {
        if (scrollContainerRef.current && !hasScrolledRef.current) {
            scrollContainerRef.current.scrollTop = savedScrollTop;
        }
        if (document.body.contains(activeElement)) {
            activeElement.focus();
        }
    });
  };

  const addRowAt = (type, index) => {
    runWithScrollPreservation(() => {
        const key = `${currentYear}-${currentMonth}-${selectedDay}`;
        const date = new Date(currentYear, currentMonth, selectedDay);
        const dayOfWeek = date.getDay();
    
        const newRow = {
            id: Math.random(),
            name: '',
            group: '',
            bgnCounts: {},
            eurCounts: {},
            bgnTotal: 0,
            eurTotal: 0,
            isCarryOver: false
        };
        
        // IMMER FIX: Use produce for adding a row.
        const nextState = produce(dailyData, draft => {
            if (!draft[key]) {
                draft[key] = initializeDayData(dayOfWeek);
            }
            draft[key][type].splice(index + 1, 0, newRow);
        });

        setDailyData(nextState);
        saveToHistory(nextState);

        logChange({
            type: 'add',
            location: { year: currentYear, month: currentMonth, day: selectedDay, section: type, rowIndex: index + 1, rowId: newRow.id },
            details: { addedData: newRow }
        });
    });
  };

  const deleteRowAt = (type, index) => {
    runWithScrollPreservation(() => {
        const key = `${currentYear}-${currentMonth}-${selectedDay}`;
        const rowToDelete = dailyData[key]?.[type]?.[index];

        if (rowToDelete) {
             logChange({
                type: 'delete',
                location: { year: currentYear, month: currentMonth, day: selectedDay, section: type, rowIndex: index, rowId: rowToDelete.id },
                details: { deletedData: JSON.parse(JSON.stringify(rowToDelete)) }
            });
        }
        
        // IMMER FIX: Use produce for deleting a row.
        const nextState = produce(dailyData, draft => {
            if (draft[key] && draft[key][type].length > 1) {
                draft[key][type].splice(index, 1);
            }
        });

        setDailyData(nextState);
        saveToHistory(nextState);
    });
  };

  const updateTransactionFields = useCallback((type, index, updates) => {
    runWithScrollPreservation(() => {
        const key = `${currentYear}-${currentMonth}-${selectedDay}`;
        
        // IMMER FIX: Use produce for all field updates.
        const nextState = produce(dailyData, draft => {
            const date = new Date(currentYear, currentMonth, selectedDay);
            const dayOfWeek = date.getDay();
            if (!draft[key]) {
                draft[key] = initializeDayData(dayOfWeek);
            }
            
            const oldTransaction = draft[key]?.[type]?.[index];
            if (!oldTransaction) return; // Should not happen with produce, but good for safety

            const updatedTransaction = draft[key][type][index];

            // Log changes before updating state
            Object.entries(updates).forEach(([field, newValue]) => {
                const oldValue = oldTransaction[field];
                if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
                    // (The logging logic remains the same as it reads data before modification)
                    if (field === 'bgnCounts' || field === 'eurCounts') {
                        const denominations = field === 'bgnCounts' ? BGN_DENOMINATIONS : EUR_DENOMINATIONS;
                        denominations.forEach((denom, denomIndex) => {
                            const oldDenomCount = oldValue?.[denomIndex] || 0;
                            const newDenomCount = newValue?.[denomIndex] || 0;
                            if (oldDenomCount !== newDenomCount) {
                                logChange({
                                    type: 'edit',
                                    location: { year: currentYear, month: currentMonth, day: selectedDay, section: type, rowIndex: index, rowId: oldTransaction.id },
                                    details: {
                                        field: `${field}.${denomIndex}`,
                                        fieldName: `${denom} ${field === 'bgnCounts' ? 'лв' : '€'}`,
                                        oldValue: oldDenomCount,
                                        newValue: newDenomCount,
                                        updatedTransaction: { ...updatedTransaction, ...updates }
                                    }
                                });
                            }
                        });
                    } else {
                        logChange({
                            type: 'edit',
                            location: { year: currentYear, month: currentMonth, day: selectedDay, section: type, rowIndex: index, rowId: oldTransaction.id },
                            details: {
                                field: field,
                                fieldName: field === 'name' ? 'Име' : 'Група',
                                oldValue: oldValue,
                                newValue: newValue,
                                updatedTransaction: { ...updatedTransaction, ...updates }
                            }
                        });
                    }
                }
            });
            
            // Now, apply updates to the draft
            Object.assign(updatedTransaction, updates);

            if ('bgnCounts' in updates) {
                updatedTransaction.bgnTotal = calculateTotal(BGN_DENOMINATIONS, BGN_DENOMINATIONS.map((_, i) => updatedTransaction.bgnCounts[i] || 0));
            }
            if ('eurCounts' in updates) {
                updatedTransaction.eurTotal = calculateTotal(EUR_DENOMINATIONS, EUR_DENOMINATIONS.map((_, i) => updatedTransaction.eurCounts[i] || 0));
            }
        });

        setDailyData(nextState);
        saveToHistory(nextState);
    });
  }, [dailyData, currentYear, currentMonth, selectedDay, saveToHistory, logChange]);

  const updateTransaction = useCallback((type, index, field, value) => {
    if (field === 'bgnCount' || field === 'eurCount') {
      const key = `${currentYear}-${currentMonth}-${selectedDay}`;
      const transaction = (dailyData[key]?.[type] || [])[index] || {};
      const [currency, denomIndex, countValue] = value;
      const countsField = currency === 'bgn' ? 'bgnCounts' : 'eurCounts';
      
      const newCounts = { ...transaction[countsField], [denomIndex]: parseInt(countValue) || 0 };
      
      updateTransactionFields(type, index, { [countsField]: newCounts });

    } else {
      updateTransactionFields(type, index, { [field]: value });
    }
  }, [dailyData, currentYear, currentMonth, selectedDay, updateTransactionFields]);
  
  const handleNameChange = useCallback((type, index, newName) => {
      const allSearchableNames = [
        ...clientList,
        ...employeeList.map(e => ({ name: e.name, group: 'Заплати' }))
      ];
      const selectedClient = allSearchableNames.find(c => c.name.toLowerCase() === newName.toLowerCase());
      
      const updates = { name: newName };
      if (selectedClient && selectedClient.group) {
          updates.group = selectedClient.group;
      }

      updateTransactionFields(type, index, updates);
  }, [updateTransactionFields, clientList, employeeList]);


  const handleViewModeChange = useCallback((mode) => {
    runWithScrollPreservation(() => {
        setViewMode(mode);
    });
  }, []);

  const exportToCSV = () => {
    const rows = [];
    rows.push(['Дата', 'Тип', 'Група', 'Име', 'BGN', 'EUR', 'Месец', 'Година']);
    
    Object.entries(dailyData).forEach(([key, dayData]) => {
      const [year, month, day] = key.split('-');
      const date = `${day}.${parseInt(month) + 1}.${year}`;
      const monthName = monthNamesShort[parseInt(month)];
      
      dayData.income?.forEach(t => {
        if (t.name) {
          rows.push([date, 'Приход', t.group, t.name, t.bgnTotal, t.eurTotal, monthName, year]);
        }
      });
      
      dayData.expense?.forEach(t => {
        if (t.name) {
          rows.push([date, 'Разход', t.group, t.name, t.bgnTotal, t.eurTotal, monthName, year]);
        }
      });
    });
    
    const csvContent = rows.map(e => e.join(',')).join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `cash_management_${currentMonth + 1}_${currentYear}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  const importCashExcel = useCallback((file) => {
    let target = parseMonthYear(file.name);
    if (!target) {
      const ans = prompt('Не можах да определя месец и година от името на файла.\n' +
                         'Моля въведете в формат ММ.ГГ (напр. 04.25 за април 2025):');
      target = parseMonthYear('x' + ans);
      if (!target) { alert('Невалиден формат.'); return; }
    }
    const { month: tgtMonth, year: tgtYear } = target;

    const reader = new FileReader();
    reader.onload = (e) => {
      const wb = XLSX.read(e.target.result, { type: 'array' });
      
      // IMMER FIX: Use produce to efficiently update from the imported Excel file.
      const nextState = produce(dailyData, draft => {
          wb.SheetNames.forEach((sn) => {
            const day = Number(sn);
            if (!day || day > 31) return;
            const ws = wb.Sheets[sn];
            const rows = XLSX.utils.sheet_to_json(ws, { header: 1, raw: false });

            let mode = 'income';
            const incomeRows  = [];
            const expenseRows = [];

            for (let r = 2; r < rows.length; r++) {
              const row = rows[r];
              const nameCell = (row[1] || '').toString().trim();
              const flagC    = (row[2] || '').toString().trim();

              if (/^приход/i.test(nameCell) || /^приход/i.test(flagC)) { mode = 'income'; continue; }
              if (/^разход/i.test(nameCell) || /^разход/i.test(flagC)) { mode = 'expense'; continue; }
              if (/^наличност/i.test(nameCell)) continue;
              if (!nameCell)      continue;

              const bgnCounts = Array(BGN_DENOMINATIONS.length).fill(0);
              const eurCounts = Array(EUR_DENOMINATIONS.length).fill(0);

              Object.entries(BGN_COL_MAP).forEach(([denom, cIdx]) => {
                const v = Number(row[cIdx] || 0);
                const ix = BGN_DENOMINATIONS.indexOf(Number(denom));
                if (ix >= 0 && v) bgnCounts[ix] = v;
              });
              Object.entries(EUR_COL_MAP).forEach(([denom, cIdx]) => {
                const v = Number(row[cIdx] || 0);
                const ix = EUR_DENOMINATIONS.indexOf(Number(denom));
                if (ix >= 0 && v) eurCounts[ix] = v;
              });

              const tr = {
                ...blankRow(),
                name: nameCell,
                bgnCounts,
                eurCounts,
                bgnTotal: BGN_DENOMINATIONS.reduce((s, d, i) => s + d * bgnCounts[i], 0),
                eurTotal: EUR_DENOMINATIONS.reduce((s, d, i) => s + d * eurCounts[i], 0),
              };
              (mode === 'income' ? incomeRows : expenseRows).push(tr);
            }

            const key = `${tgtYear}-${tgtMonth}-${day}`;
            const template = initializeDayData(new Date(tgtYear, tgtMonth, day).getDay());
            
            // Ensure dayData exists in the draft
            if (!draft[key]) {
              draft[key] = template;
            }
            const dayData  = draft[key];

            const incomeStart = 1;
            if (dayData.income.length < incomeStart + incomeRows.length) {
              const diff = incomeStart + incomeRows.length - dayData.income.length;
              for (let i = 0; i < diff; i++) dayData.income.push(blankRow());
            }
            incomeRows.forEach((tr, idx) => {
              dayData.income[incomeStart + idx] = { ...dayData.income[incomeStart + idx], ...tr };
            });

            if (dayData.expense.length < expenseRows.length) {
              const diff = expenseRows.length - dayData.expense.length;
              for (let i = 0; i < diff; i++) dayData.expense.push(blankRow());
            }
            expenseRows.forEach((tr, idx) => {
              dayData.expense[idx] = { ...dayData.expense[idx], ...tr };
            });
          });
      });

      setDailyData(nextState);
      alert(`Данните за ${tgtMonth + 1}.${tgtYear} бяха импортирани успешно.`);
    };
    reader.readAsArrayBuffer(file);
  }, [dailyData, initializeDayData]);

  const importFromFile = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const ext = file.name.split('.').pop().toLowerCase();
    if (ext === 'xls' || ext === 'xlsx') {
      importCashExcel(file);
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target.result;
      const lines = text.split('\n');
      
      const imported = [];
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',');
        if (values.length > 1) {
          imported.push({ name: values[0].trim(), group: '' });
        }
      }
      
      if (confirm(`Искате ли да добавите ${imported.length} имена към списъка с клиенти?`)) {
        setClientList(prev => {
            const existingNames = new Set(prev.map(c => c.name.toLowerCase()));
            const uniqueNewClients = imported.filter(c => !existingNames.has(c.name.toLowerCase()));
            return [...prev, ...uniqueNewClients].sort((a,b) => a.name.localeCompare(b.name));
        });
      }
    };
    reader.readAsText(file);
  };
  
  // NEW: Function to import employees from CSV/Excel
  const importEmployeesFromFile = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target.result;
        const workbook = XLSX.read(data, { type: 'binary' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, range: 1 });

        const importedEmployees = [];
        const newDepartmentNames = new Set();

        jsonData.forEach((row, i) => {
          if (row && row.length > 0 && row[0]) {
            const name = row[0].toString().trim();
            const department = row[1] ? row[1].toString().trim() : 'Администрация';
            const totalSalary = parseFloat(row[2]) || 0;
            const bankSalary = parseFloat(row[3]) || 0;

            if (name) {
              importedEmployees.push({
                id: Date.now() + i,
                name,
                department,
                totalSalary,
                bankSalary,
                socialSecurity: 0,
                annualLeave: 0
              });

              if (!departments[department]) {
                newDepartmentNames.add(department);
              }
            }
          }
        });

        if (importedEmployees.length === 0) {
          alert("Не са намерени служители за импортиране във файла.");
          return;
        }

        if (window.confirm(`Намерени са ${importedEmployees.length} служители. Искате ли да ги добавите? (Съществуващите служители с еднакви имена няма да бъдат променяни.)`)) {
          if (newDepartmentNames.size > 0) {
            setDepartments(prevDepts => {
              const newDepts = { ...prevDepts };
              newDepartmentNames.forEach(deptName => {
                newDepts[deptName] = {
                  workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
                  halfDays: []
                };
              });
              return newDepts;
            });
          }
          
          let addedCount = 0;
          setEmployeeList(prevList => {
            const existingNames = new Set(prevList.map(emp => emp.name.toLowerCase()));
            const uniqueNewEmployees = importedEmployees.filter(impEmp => !existingNames.has(impEmp.name.toLowerCase()));
            addedCount = uniqueNewEmployees.length;

            if (uniqueNewEmployees.length > 0) {
              setClientList(prevClientList => {
                const newClientsToAdd = uniqueNewEmployees.map(emp => ({ name: emp.name, group: 'Заплати' }));
                const existingClientNames = new Set(prevClientList.map(c => c.name.toLowerCase()));
                const uniqueNewClientsForClientList = newClientsToAdd.filter(c => !existingClientNames.has(c.name.toLowerCase()));
                return [...prevClientList, ...uniqueNewClientsForClientList].sort((a,b) => a.name.localeCompare(b.name));
              });
              return [...prevList, ...uniqueNewEmployees].sort((a, b) => a.name.localeCompare(b.name));
            }
            return prevList;
          });
          
          if (addedCount < importedEmployees.length) {
            alert(`${addedCount} служители бяха добавени. ${importedEmployees.length - addedCount} бяха пропуснати, защото вече съществуват.`);
          } else {
            alert(`${addedCount} служители бяха добавени успешно.`);
          }
        }
      } catch (error) {
        console.error("Грешка при импортиране на служители:", error);
        alert(`Възникна грешка при четене на файла: ${error.message}`);
      } finally {
        event.target.value = null;
      }
    };
    reader.readAsBinaryString(file);
  };
    
  const saveAllData = async () => {
    const allData = collectAllAppData();
    
    if (isElectron) {
      // This path is now handled by handleSaveWithDialog
      handleSaveWithDialog();
    } else {
      // Fallback for browser environment
      const jsonData = await stringifyJSONAsync(allData.data);
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      const dateStr = new Date().toISOString().split('T')[0];
      link.href = url;
      link.download = `cash-management-backup-${dateStr}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };

  const loadAllData = (event) => {
      const file = event.target.files[0];
      if (!file) return;

      if (!window.confirm("Сигурни ли сте, че искате да заредите нов файл? Всички текущи данни ще бъдат презаписани.")) {
          event.target.value = null;
          return;
      }

      const reader = new FileReader();
      reader.onload = async (e) => {
          try {
              const text = e.target.result;
              // Use async JSON parsing for better performance
              const loadedData = await parseJSONAsync(text);
              // Use the new async restore function
              await restoreStateFromData(loadedData.data ? loadedData : { data: loadedData });
          } catch (error) {
              console.error("Грешка при зареждане на данните:", error);
              showToast(`Грешка при зареждане на файла: ${error.message}`, 'error');
          } finally {
              event.target.value = null;
          }
      };
      reader.readAsText(file);
  };
  
    // START UPDATE: handleGlobalRefresh and its async version
   const handleGlobalRefreshAsync = async () => {
  const toast = showToast('Обновяване на всички месеци…', 'info');
  try {
    // Sort all known calendar days (keys are YYYY-MM-DD)
    const orderedDates = Object.keys(dailyData).sort();
    let carry = 0;                  // balance that rolls to the next day

    // Immer-produce a fresh copy with correct carry-overs everywhere
    const next = produce(dailyData, draft => {
      orderedDates.forEach(dateKey => {
        const day = draft[dateKey];
        if (!day) return;

        // 1. Throw away any OLD auto rows
        day.income  = day.income .filter(r => !r.isCarry);
        day.expense = day.expense.filter(r => !r.isCarry);

        // 2. Insert TODAY’s incoming carry
        if (carry > 0) {
          day.income.unshift({
            id: 'carry-in',
            name: 'Налично от вчера',
            amount: carry,
            isCarry: true
          });
        } else if (carry < 0) {
          day.expense.unshift({
            id: 'carry-in',
            name: 'Пренесен разход',
            amount: -carry,
            isCarry: true
          });
        }

        // 3. Work out the new end-of-day balance
        const sum = arr => arr.reduce((s, r) => s + (+r.amount || 0), 0);
        carry = sum(day.income) - sum(day.expense);
      });
    });

    setDailyData(next);
    saveToHistory(next);            // undo/redo friendliness
    toast.update('Готово – всички месеци са пресметнати!', 'success');
  } catch (e) {
    console.error(e);
    toast.update(`Грешка: ${e.message}`, 'error');
  }
};

    // Also update the sync version to call the async one:
    const handleGlobalRefresh = () => {
        handleGlobalRefreshAsync();
    };
    // END UPDATE


  const handleMouseDown = (e, columnId) => {
      e.preventDefault();
      setIsResizing(true);
      const thElement = e.target.parentElement.closest('th');
      resizeRef.current = {
          startX: e.clientX,
          columnId,
          startWidth: thElement ? thElement.offsetWidth : (columnId === 'name' ? nameColumnWidth : columnWidths[columnId] || 60),
      };
  };
  
  useEffect(() => {
      const handleMouseMove = (e) => {
          if (!isResizing || !resizeRef.current) return;
          
          const { startX, startWidth, columnId } = resizeRef.current;
          const deltaX = e.clientX - startX;
          const newWidth = startWidth + deltaX;
  
          if (columnId === 'name') {
              setNameColumnWidth(Math.max(100, Math.min(600, newWidth)));
          } else {
              setColumnWidths(prev => ({ 
                  ...prev, 
                  [columnId]: Math.max(40, Math.min(200, newWidth)) 
              }));
          }
      };
  
      const handleMouseUp = () => {
          setIsResizing(false);
          resizeRef.current = null;
      };
  
      if (isResizing) {
          document.addEventListener('mousemove', handleMouseMove);
          document.addEventListener('mouseup', handleMouseUp);
      }
  
      return () => {
          document.removeEventListener('mousemove', handleMouseMove);
          document.removeEventListener('mouseup', handleMouseUp);
      };
  }, [isResizing]);

useEffect(() => {
  const handleKeyDown = (e) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        undo();
      } else if (e.key === 'y' || (e.key === 'z' && e.shiftKey)) {
        e.preventDefault();
        redo();
      }
    }
    // ADD F5 FUNCTIONALITY: Open save dialog and immediately close it
    if (e.key === 'F5') {
      e.preventDefault(); // Prevent default F5 behavior (page refresh)
      handleF5SaveAction();
    }
  };

  window.addEventListener('keydown', handleKeyDown);
  return () => window.removeEventListener('keydown', handleKeyDown);
}, [undo, redo]); //
  
  const [popupData, setPopupData] = useState({ isOpen: false, employee: null, month: 0, year: 0 });

    // START UPDATE: Add handleSaveWithDialog function
    const handleSaveWithDialog = async () => {
        if (isElectron) {
            setIsSaving(true);
            try {
                const allData = collectAllAppData();

            // Use async stringify to prevent blocking
            const jsonString = await stringifyJSONAsync(allData);

            const result = await window.electronAPI.saveAs(jsonString);

            if (result.success) {
                showToast(`Файлът е запазен успешно в: ${result.path}`, 'success');
            } else if (result.error && result.error !== 'cancelled') {
                showToast(`Грешка при запис: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Save error:', error);
            showToast(`Грешка при запис: ${error.message}`, 'error');
        } finally {
            setIsSaving(false);
        }
    } else {
        // Fallback for non-Electron environment
        saveAllData();
    }
};

    // F5 Save Action: Opens save dialog briefly and then performs quick save
    const handleF5SaveAction = async () => {
        if (isElectron) {
            setIsSaving(true);
            try {
                const allData = collectAllAppData();

                // Use async stringify to prevent blocking
                const jsonString = await stringifyJSONAsync(allData);

                // Show a brief toast to indicate the save dialog is opening
                showToast('F5: Отваряне на диалог за запазване и автоматично затваряне...', 'info');

                // Use the new F5 save action that opens dialog briefly and then quick saves
                const result = await window.electronAPI.f5SaveAction(jsonString);

                if (result.success) {
                    showToast(`F5: Диалогът е отворен и затворен! Запазено като ${result.filename}`, 'success');
                } else {
                    showToast(`F5: Грешка при запис: ${result.error}`, 'error');
                }

            } catch (error) {
                console.error('F5 Save error:', error);
                showToast(`F5: Грешка при запис: ${error.message}`, 'error');
            } finally {
                setIsSaving(false);
            }
        } else {
            // Fallback for non-Electron environment
            saveAllData();
        }
    };
    // END UPDATE

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-100'}`}>
      {/* Header */}
      <header className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-sm border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="px-4 py-3">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold">
              Система за управление на пари в брой
            </h1>
            <div className="flex items-center gap-4">
              
              {/* START UPDATE: Header button section */}
              <div className="flex gap-2">
                  {isElectron && (
                      <>
                          <button
                              onClick={handleQuickSaveEnhanced}
                              className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-700 flex items-center"
                              title="Бързо запазване с автоматично име"
                          >
                              <Save className="w-4 h-4 mr-1" />
                              Бърз запис
                          </button>
                          <button
                              onClick={handleLoadLast}
                              className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-700 flex items-center"
                              title="Зареди последното автоматично запазване"
                          >
                              <FolderOpen className="w-4 h-4 mr-1" />
                              Зареди Последно
                          </button>
                          <button
                              onClick={handleSaveWithDialog}
                              className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center"
                              title="Запази с избор на място и име"
                          >
                              <Save className="w-4 h-4 mr-1" />
                              Запази като...
                          </button>
                      </>
                  )}
                  
                  {/* Remove the duplicate "Запази като" button and the non-functional "Запази..." button */}
                  
                  <label 
                      className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center cursor-pointer"
                      title="Зареди всички данни от файл"
                  >
                      <FolderOpen className="w-4 h-4 mr-1" />
                      Зареди
                      <input
                          type="file"
                          className="hidden"
                          accept=".json"
                          onChange={loadAllData}
                      />
                  </label>

                  {activeTab === 'daily' && (
                      <>
                          <button
                              onClick={() => {
                                  const newFocusState = !isFocusMode;
                                  setIsFocusMode(newFocusState);
                                  if (newFocusState) {
                                      setPanelHeight(160);
                                  } else {
                                      setPanelHeight(320);
                                  }
                              }}
                              className="bg-yellow-500 text-white px-3 py-1 rounded hover:bg-yellow-600 flex items-center"
                              title={isFocusMode ? "Покажи календар" : "Скрий календар"}
                          >
                              {isFocusMode ? <PanelTopOpen className="w-4 h-4 mr-1" /> : <PanelTopClose className="w-4 h-4 mr-1" />}
                              {isFocusMode ? "Покажи" : "Скрий"}
                          </button>

                          <button
                              onClick={handleGlobalRefresh}
                              className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center"
                              title="Обнови данните в текущата таблица"
                          >
                              <RefreshCw className="w-4 h-4 mr-1" />
                              Обнови
                          </button>
                      </>
                  )}
              </div>
              {/* END UPDATE */}


              <div className="flex gap-2">
                <button
                  onClick={undo}
                  disabled={historyIndex <= 0}
                  className={`px-2 py-1 rounded ${historyIndex <= 0 ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`}
                  title="Undo (Ctrl+Z)"
                >
                  <Undo className="w-4 h-4" />
                </button>
                <button
                  onClick={redo}
                  disabled={historyIndex >= history.length - 1}
                  className={`px-2 py-1 rounded ${historyIndex >= history.length - 1 ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`}
                  title="Redo (Ctrl+Y)"
                >
                  <Redo className="w-4 h-4" />
                </button>
              </div>
              <div className="flex items-center gap-2">
                <button onClick={()=>changeZoom(-10)} className="bg-gray-500 text-white px-2 py-1 rounded">-</button>
                <span>{tabZoom[activeTab] || 100}%</span>
                <button onClick={()=>changeZoom(+10)} className="bg-gray-500 text-white px-2 py-1 rounded">+</button>
              </div>
              <label className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 cursor-pointer flex items-center">
                <Upload className="w-4 h-4 mr-1" />
                Импорт
                <input
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  onChange={importFromFile}
                  className="hidden"
                />
              </label>
              <button
                onClick={exportToCSV}
                className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 flex items-center"
              >
                <Download className="w-4 h-4 mr-1" />
                Експорт
              </button>
              <button
                onClick={() => setIsDarkMode(!isDarkMode)}
                className={`p-2 rounded ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
              >
                {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
              </button>
              <button
                onClick={() => setIsEnglish(!isEnglish)}
                className={`px-3 py-1 rounded ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
              >
                {isEnglish ? 'БГ' : 'EN'}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="px-4">
          <div className="flex gap-4 overflow-x-auto">
            <button
              onClick={() => setActiveTab('daily')}
              className={`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${
                activeTab === 'daily' ? 'border-blue-500 text-blue-600' : 'border-transparent hover:text-blue-600'
              }`}
            >
              <Calendar className="w-5 h-5 inline mr-2" />
              Дневно въвеждане
            </button>
            <button
              onClick={() => setActiveTab('turnover')}
              className={`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${
                activeTab === 'turnover' ? 'border-blue-500 text-blue-600' : 'border-transparent hover:text-blue-600'
              }`}
            >
              <FileText className="w-5 h-5 inline mr-2" />
              Оборот
            </button>
            <button
              onClick={() => setActiveTab('analysis')}
              className={`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${
                activeTab === 'analysis' ? 'border-blue-500 text-blue-600' : 'border-transparent hover:text-blue-600'
              }`}
            >
              <TrendingUp className="w-5 h-5 inline mr-2" />
              Анализ
            </button>
            <button
              onClick={() => setActiveTab('salary')}
              className={`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${
                activeTab === 'salary' ? 'border-blue-500 text-blue-600' : 'border-transparent hover:text-blue-600'
              }`}
            >
              <DollarSign className="w-5 h-5 inline mr-2" />
              Заплати
            </button>
            <button
              onClick={() => setActiveTab('history')}
              className={`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${
                activeTab === 'history' ? 'border-blue-500 text-blue-600' : 'border-transparent hover:text-blue-600'
              }`}
            >
              <RefreshCw className="w-5 h-5 inline mr-2" />
              История
            </button>
            <button
              onClick={() => setActiveTab('employees')}
              className={`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${
                activeTab === 'employees' ? 'border-blue-500 text-blue-600' : 'border-transparent hover:text-blue-600'
              }`}
            >
              <Users className="w-5 h-5 inline mr-2" />
              Служители
            </button>
            <button
              onClick={() => setActiveTab('lists')}
              className={`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${
                activeTab === 'lists' ? 'border-blue-500 text-blue-600' : 'border-transparent hover:text-blue-600'
              }`}
            >
              <List className="w-5 h-5 inline mr-2" />
              Списъци
            </button>
            
            {activeTab === 'daily' && (
              <div className="ml-auto flex items-center gap-3">
                <button
                  onClick={() => {
                    const p = shiftDay({ day: selectedDay, month: currentMonth, year: currentYear }, -1);
                    setCurrentYear(p.year); setCurrentMonth(p.month); setSelectedDay(p.day);
                  }}
                  className="px-3 py-1 rounded bg-gray-500 text-white hover:bg-gray-600"
                  title="Предишен ден"
                >
                  Ден &lt;
                </button>
                <button
                  onClick={() => {
                    const n = shiftDay({ day: selectedDay, month: currentMonth, year: currentYear }, +1);
                    setCurrentYear(n.year); setCurrentMonth(n.month); setSelectedDay(n.day);
                  }}
                  className="px-3 py-1 rounded bg-gray-500 text-white hover:bg-gray-600"
                  title="Следващ ден"
                >
                  Ден &gt;
                </button>
                
                <div className="flex gap-1 rounded p-0.5">
                  {['both','bgn','eur'].map(mode => (
                    <button
                      key={mode}
                      onClick={() => handleViewModeChange(mode)}
                      className={`px-3 py-1 rounded transition-colors ${
                        viewMode === mode 
                        ? 'bg-blue-500 text-white' 
                        : (isDarkMode ? 'bg-gray-600 hover:bg-gray-500' : 'bg-gray-200 text-black hover:bg-gray-300')
                      }`}
                    >
                      {{
                        both:'И двете',
                        bgn:'Само BGN',
                        eur:'Само EUR',
                      }[mode]}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="px-4 py-6">
        <div style={{ zoom:`${tabZoom[activeTab]||100}%` }} className="h-[calc(100vh-150px)]">
            {activeTab === 'daily' && (
              <DailySheet
                  isDarkMode={isDarkMode}
                  currentYear={currentYear}
                  currentMonth={currentMonth}
                  selectedDay={selectedDay}
                  dailyData={dailyData}
                  initializeDayData={initializeDayData}
                  scrollToRowId={scrollToRowId}
                  setScrollToRowId={setScrollToRowId}
                  highlightedRowId={highlightedRowId}
                  setHighlightedRowId={setHighlightedRowId}
                  hasScrolledRef={hasScrolledRef}
                  monthNames={monthNames}
                  isWeekend={isWeekend}
                  isHoliday={isHoliday}
                  salaryCashPayments={salaryCashPayments}
                  calculateTotal={calculateTotal}
                  viewMode={viewMode}
                  frozenHeaders={frozenHeaders}
                  setFrozenHeaders={setFrozenHeaders}
                  scrollContainerRef={scrollContainerRef}
                  columnWidths={columnWidths}
                  handleMouseDown={handleMouseDown}
                  nameColumnWidth={nameColumnWidth}
                  transactionGroups={transactionGroups}
                  clientList={clientList}
                  employeeList={employeeList}
                  handleNameChange={handleNameChange}
                  deleteRowAt={deleteRowAt}
                  addRowAt={addRowAt}
                  updateTransaction={updateTransaction}
                  panelHeight={panelHeight}
                  setPanelHeight={setPanelHeight}
                  isFocusMode={isFocusMode}
                  setIsFocusMode={setIsFocusMode}
                  setCurrentMonth={setCurrentMonth}
                  setCurrentYear={setCurrentYear}
                  setSelectedDay={setSelectedDay}
                  daysInMonth={daysInMonth}
                  isCurrentMonth={isCurrentMonth}
              />
            )}

            
            {activeTab === 'analysis' && <AnalysisTab
              isDarkMode={isDarkMode}
              analysisFilters={analysisFilters}
              setAnalysisFilters={setAnalysisFilters}
              allTransactions={allTransactions}
              currentYear={currentYear}
              currentMonth={currentMonth}
              dailyData={dailyData}
              transactionGroups={transactionGroups}
              setActiveTab={setActiveTab}
              setCurrentYear={setCurrentYear}
              setCurrentMonth={setCurrentMonth}
              setSelectedDay={setSelectedDay}
              setScrollToRowId={setScrollToRowId}
              hasScrolledRef={hasScrolledRef}
              salaryCashPayments={salaryCashPayments}
              monthNames={monthNames}
              calculateTotal={calculateTotal}
            />}
            {activeTab === 'history' && <HistoryTab
              isDarkMode={isDarkMode}
              changeHistory={changeHistory}
              setChangeHistory={setChangeHistory}
              isElectron={isElectron}
              setActiveTab={setActiveTab}
              setCurrentYear={setCurrentYear}
              setCurrentMonth={setCurrentMonth}
              setSelectedDay={setSelectedDay}
              setScrollToRowId={setScrollToRowId}
              hasScrolledRef={hasScrolledRef}
            />}
            {activeTab === 'employees' && <EmployeeListTab
              isDarkMode={isDarkMode}
              departments={departments}
              setDepartments={setDepartments}
              employeeList={employeeList}
              setEmployeeList={setEmployeeList}
              employeeTransactions={employeeTransactions}
              setEmployeeTransactions={setEmployeeTransactions}
              setClientList={setClientList}
              leaveData={leaveData}
              currentYear={currentYear}
              importEmployeesFromFile={importEmployeesFromFile}
            />}
            {activeTab === 'salary' && <SalaryTab
              isDarkMode={isDarkMode}
              currentMonth={currentMonth}
              currentYear={currentYear}
              monthNames={monthNames}
              calculateWorkingDays={calculateWorkingDays}
              employeeList={employeeList}
              allTransactions={allTransactions}
              salaryCashPayments={salaryCashPayments}
              handleSalaryCashChange={handleSalaryCashChange}
              leaveData={leaveData}
              handleLeaveDataChange={handleLeaveDataChange}
              calculateTotal={calculateTotal}
              popupData={popupData}
              setPopupData={setPopupData}
              departments={departments}
              salaryInputs={salaryInputs}
              setSalaryInputs={setSalaryInputs}
            />}
            {activeTab === 'turnover' && <TurnoverTab
              isDarkMode={isDarkMode}
              currentYear={currentYear}
              currentMonth={currentMonth}
              monthNames={monthNames}
              dailyData={dailyData}
              turnoverOverrides={turnoverOverrides}
              setTurnoverOverrides={setTurnoverOverrides}
              isHoliday={isHoliday}
            />}
            {activeTab === 'lists' && <ListsTab
              isDarkMode={isDarkMode}
              clientList={clientList}
              setClientList={setClientList}
              customHolidays={customHolidays}
              setCustomHolidays={setCustomHolidays}
              transactionGroups={transactionGroups}
              setTransactionGroups={setTransactionGroups}
              importFromFile={importFromFile}
            />}
        </div>
      </main>
      
    </div>
  );
};

export default AppWrapper;