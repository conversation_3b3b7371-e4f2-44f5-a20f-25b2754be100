import{a as Kc,b as zc,g as Yc,R as ds,r as J,H as Jc}from"./vendor-B-iGViyw.js";import{p as bt,S as Qn,F as es,P as qc,a as Zc,R as rs,U as Qc,b as el,c as ni,D as si,d as rl,M as tl,C as al,e as nl,T as sl,f as ms,g as ii,L as ci,h as Mn,i as $n,j as li,E as oi,k as fn,l as un,m as Aa,n as hn,o as ia,q as gs,r as ps,s as il,t as cl,u as ll}from"./utils-CnGi3iDr.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))n(a);new MutationObserver(a=>{for(const s of a)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function r(a){const s={};return a.integrity&&(s.integrity=a.integrity),a.referrerPolicy&&(s.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?s.credentials="include":a.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function n(a){if(a.ep)return;a.ep=!0;const s=r(a);fetch(a.href,s)}})();var Un={exports:{}},Ea={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ks;function ol(){if(Ks)return Ea;Ks=1;var e=Kc(),t=Symbol.for("react.element"),r=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,a=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function i(c,o,u){var f,x={},d=null,p=null;u!==void 0&&(d=""+u),o.key!==void 0&&(d=""+o.key),o.ref!==void 0&&(p=o.ref);for(f in o)n.call(o,f)&&!s.hasOwnProperty(f)&&(x[f]=o[f]);if(c&&c.defaultProps)for(f in o=c.defaultProps,o)x[f]===void 0&&(x[f]=o[f]);return{$$typeof:t,type:c,key:d,ref:p,props:x,_owner:a.current}}return Ea.Fragment=r,Ea.jsx=i,Ea.jsxs=i,Ea}var zs;function fl(){return zs||(zs=1,Un.exports=ol()),Un.exports}var l=fl();if(!window.electronAPI){console.warn("[electron-shim] Running outside Electron – IPC disabled.");const e=t=>async()=>{throw new Error(`IPC method "${t}" is unavailable outside Electron`)};window.electronAPI={quickSave:e("quickSave"),autoSave:e("autoSave"),saveAs:e("saveAs"),f5SaveAction:e("f5SaveAction"),loadBackup:e("loadBackup"),listBackups:e("listBackups"),archiveHistory:e("archiveHistory"),saveHistory:e("saveHistory"),loadHistory:e("loadHistory"),listHistoryArchives:e("listHistoryArchives"),loadHistoryArchive:e("loadHistoryArchive")}}var Qa={},Ys;function ul(){if(Ys)return Qa;Ys=1;var e=zc();return Qa.createRoot=e.createRoot,Qa.hydrateRoot=e.hydrateRoot,Qa}var hl=ul();const xl=Yc(hl);/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */var fi=1252,dl=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],vs={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},ys=function(e){dl.indexOf(e)!=-1&&(fi=vs[0]=e)};function ml(){ys(1252)}var ct=function(e){ys(e)};function ui(){ct(1200),ml()}function Js(e){for(var t=[],r=0,n=e.length;r<n;++r)t[r]=e.charCodeAt(r);return t}function gl(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}function hi(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var Ta=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return t==255&&r==254?gl(e.slice(2)):t==254&&r==255?hi(e.slice(2)):t==65279?e.slice(1):e},en=function(t){return String.fromCharCode(t)},qs=function(t){return String.fromCharCode(t)},ts,At="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function Zs(e){for(var t="",r=0,n=0,a=0,s=0,i=0,c=0,o=0,u=0;u<e.length;)r=e.charCodeAt(u++),s=r>>2,n=e.charCodeAt(u++),i=(r&3)<<4|n>>4,a=e.charCodeAt(u++),c=(n&15)<<2|a>>6,o=a&63,isNaN(n)?c=o=64:isNaN(a)&&(o=64),t+=At.charAt(s)+At.charAt(i)+At.charAt(c)+At.charAt(o);return t}function Xr(e){var t="",r=0,n=0,a=0,s=0,i=0,c=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var u=0;u<e.length;)s=At.indexOf(e.charAt(u++)),i=At.indexOf(e.charAt(u++)),r=s<<2|i>>4,t+=String.fromCharCode(r),c=At.indexOf(e.charAt(u++)),n=(i&15)<<4|c>>2,c!==64&&(t+=String.fromCharCode(n)),o=At.indexOf(e.charAt(u++)),a=(c&3)<<6|o,o!==64&&(t+=String.fromCharCode(a));return t}var We=function(){return typeof Buffer<"u"&&typeof process<"u"&&typeof process.versions<"u"&&!!process.versions.node}(),Xt=function(){if(typeof Buffer<"u"){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch{e=!0}return e?function(t,r){return r?new Buffer(t,r):new Buffer(t)}:Buffer.from.bind(Buffer)}return function(){}}();function Ct(e){return We?Buffer.alloc?Buffer.alloc(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}function Qs(e){return We?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):typeof Uint8Array<"u"?new Uint8Array(e):new Array(e)}var it=function(t){return We?Xt(t,"binary"):t.split("").map(function(r){return r.charCodeAt(0)&255})};function Kt(e){if(Array.isArray(e))return e.map(function(n){return String.fromCharCode(n)}).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function ws(e){if(typeof ArrayBuffer>"u")throw new Error("Unsupported");if(e instanceof ArrayBuffer)return ws(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var St=We?function(e){return Buffer.concat(e.map(function(t){return Buffer.isBuffer(t)?t:Xt(t)}))}:function(e){if(typeof Uint8Array<"u"){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if(typeof e[t]=="string")throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map(function(s){return Array.isArray(s)?s:[].slice.call(s)}))};function pl(e){for(var t=[],r=0,n=e.length+250,a=Ct(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)a[r++]=i;else if(i<2048)a[r++]=192|i>>6&31,a[r++]=128|i&63;else if(i>=55296&&i<57344){i=(i&1023)+64;var c=e.charCodeAt(++s)&1023;a[r++]=240|i>>8&7,a[r++]=128|i>>2&63,a[r++]=128|c>>6&15|(i&3)<<4,a[r++]=128|c&63}else a[r++]=224|i>>12&15,a[r++]=128|i>>6&63,a[r++]=128|i&63;r>n&&(t.push(a.slice(0,r)),r=0,a=Ct(65535),n=65530)}return t.push(a.slice(0,r)),St(t)}var $r=/\u0000/g,ba=/[\u0001-\u0006]/g;function ca(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function lt(e,t){var r=""+e;return r.length>=t?r:lr("0",t-r.length)+r}function _s(e,t){var r=""+e;return r.length>=t?r:lr(" ",t-r.length)+r}function xn(e,t){var r=""+e;return r.length>=t?r:r+lr(" ",t-r.length)}function vl(e,t){var r=""+Math.round(e);return r.length>=t?r:lr("0",t-r.length)+r}function yl(e,t){var r=""+e;return r.length>=t?r:lr("0",t-r.length)+r}var e0=Math.pow(2,32);function aa(e,t){if(e>e0||e<-e0)return vl(e,t);var r=Math.round(e);return yl(r,t)}function dn(e,t){return t=t||0,e.length>=7+t&&(e.charCodeAt(t)|32)===103&&(e.charCodeAt(t+1)|32)===101&&(e.charCodeAt(t+2)|32)===110&&(e.charCodeAt(t+3)|32)===101&&(e.charCodeAt(t+4)|32)===114&&(e.charCodeAt(t+5)|32)===97&&(e.charCodeAt(t+6)|32)===108}var r0=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Hn=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function wl(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var Me={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},t0={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},_l={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function mn(e,t,r){for(var n=e<0?-1:1,a=e*n,s=0,i=1,c=0,o=1,u=0,f=0,x=Math.floor(a);u<t&&(x=Math.floor(a),c=x*i+s,f=x*u+o,!(a-x<5e-8));)a=1/(a-x),s=i,i=c,o=u,u=f;if(f>t&&(u>t?(f=o,c=s):(f=u,c=i)),!r)return[0,n*c,f];var d=Math.floor(n*c/f);return[d,n*c-d*f,f]}function $t(e,t,r){if(e>2958465||e<0)return null;var n=e|0,a=Math.floor(86400*(e-n)),s=0,i=[],c={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(c.u)<1e-6&&(c.u=0),t&&t.date1904&&(n+=1462),c.u>.9999&&(c.u=0,++a==86400&&(c.T=a=0,++n,++c.D)),n===60)i=r?[1317,10,29]:[1900,2,29],s=3;else if(n===0)i=r?[1317,8,29]:[1900,1,0],s=6;else{n>60&&--n;var o=new Date(1900,0,1);o.setDate(o.getDate()+n-1),i=[o.getFullYear(),o.getMonth()+1,o.getDate()],s=o.getDay(),n<60&&(s=(s+6)%7),r&&(s=Al(o,i))}return c.y=i[0],c.m=i[1],c.d=i[2],c.S=a%60,a=Math.floor(a/60),c.M=a%60,a=Math.floor(a/60),c.H=a,c.q=s,c}var xi=new Date(1899,11,31,0,0,0),El=xi.getTime(),Tl=new Date(1900,2,1,0,0,0);function di(e,t){var r=e.getTime();return t?r-=1461*24*60*60*1e3:e>=Tl&&(r+=1440*60*1e3),(r-(El+(e.getTimezoneOffset()-xi.getTimezoneOffset())*6e4))/(1440*60*1e3)}function Es(e){return e.indexOf(".")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function bl(e){return e.indexOf("E")==-1?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function kl(e){var t=e<0?12:11,r=Es(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10),r.length<=t)?r:e.toExponential(5)}function Sl(e){var t=Es(e.toFixed(11));return t.length>(e<0?12:11)||t==="0"||t==="-0"?e.toPrecision(6):t}function La(e){var t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),r;return t>=-4&&t<=-1?r=e.toPrecision(10+t):Math.abs(t)<=9?r=kl(e):t===10?r=e.toFixed(10).substr(0,12):r=Sl(e),Es(bl(r.toUpperCase()))}function Gt(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(e|0)===e?e.toString(10):La(e);case"undefined":return"";case"object":if(e==null)return"";if(e instanceof Date)return tt(14,di(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function Al(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}function Fl(e,t,r,n){var a="",s=0,i=0,c=r.y,o,u=0;switch(e){case 98:c=r.y+543;case 121:switch(t.length){case 1:case 2:o=c%100,u=2;break;default:o=c%1e4,u=4;break}break;case 109:switch(t.length){case 1:case 2:o=r.m,u=t.length;break;case 3:return Hn[r.m-1][1];case 5:return Hn[r.m-1][0];default:return Hn[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:o=r.d,u=t.length;break;case 3:return r0[r.q][0];default:return r0[r.q][1]}break;case 104:switch(t.length){case 1:case 2:o=1+(r.H+11)%12,u=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:o=r.H,u=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:o=r.M,u=t.length;break;default:throw"bad minute format: "+t}break;case 115:if(t!="s"&&t!="ss"&&t!=".0"&&t!=".00"&&t!=".000")throw"bad second format: "+t;return r.u===0&&(t=="s"||t=="ss")?lt(r.S,t.length):(n>=2?i=n===3?1e3:100:i=n===1?10:1,s=Math.round(i*(r.S+r.u)),s>=60*i&&(s=0),t==="s"?s===0?"0":""+s/i:(a=lt(s,2+n),t==="ss"?a.substr(0,2):"."+a.substr(2,t.length-1)));case 90:switch(t){case"[h]":case"[hh]":o=r.D*24+r.H;break;case"[m]":case"[mm]":o=(r.D*24+r.H)*60+r.M;break;case"[s]":case"[ss]":o=((r.D*24+r.H)*60+r.M)*60+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}u=t.length===3?1:2;break;case 101:o=c,u=1;break}var f=u>0?lt(o,u):"";return f}function Ft(e){var t=3;if(e.length<=t)return e;for(var r=e.length%t,n=e.substr(0,r);r!=e.length;r+=t)n+=(n.length>0?",":"")+e.substr(r,t);return n}var mi=/%/g;function Nl(e,t,r){var n=t.replace(mi,""),a=t.length-n.length;return yt(e,n,r*Math.pow(10,2*a))+lr("%",a)}function Cl(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return yt(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function gi(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+gi(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%a;if(s<0&&(s+=a),r=(t/Math.pow(10,s)).toPrecision(n+1+(a+s)%a),r.indexOf("e")===-1){var i=Math.floor(Math.log(t)*Math.LOG10E);for(r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s);r.substr(0,2)==="0.";)r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a),r=r.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(c,o,u,f){return o+u+f.substr(0,(a+s)%a)+"."+f.substr(s)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var pi=/# (\?+)( ?)\/( ?)(\d+)/;function Ol(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),s=Math.floor(a/n),i=a-s*n,c=n;return r+(s===0?"":""+s)+" "+(i===0?lr(" ",e[1].length+1+e[4].length):_s(i,e[1].length)+e[2]+"/"+e[3]+lt(c,e[4].length))}function Dl(e,t,r){return r+(t===0?"":""+t)+lr(" ",e[1].length+2+e[4].length)}var vi=/^#*0*\.([0#]+)/,yi=/\).*[0#]/,wi=/\(###\) ###\\?-####/;function Lr(e){for(var t="",r,n=0;n!=e.length;++n)switch(r=e.charCodeAt(n)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function a0(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function n0(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function Rl(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}function Il(e){return e<2147483647&&e>-2147483648?""+(e>=0?e|0:e-1|0):""+Math.floor(e)}function Qr(e,t,r){if(e.charCodeAt(0)===40&&!t.match(yi)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Qr("n",n,r):"("+Qr("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return Cl(e,t,r);if(t.indexOf("%")!==-1)return Nl(e,t,r);if(t.indexOf("E")!==-1)return gi(t,r);if(t.charCodeAt(0)===36)return"$"+Qr(e,t.substr(t.charAt(1)==" "?2:1),r);var a,s,i,c,o=Math.abs(r),u=r<0?"-":"";if(t.match(/^00+$/))return u+aa(o,t.length);if(t.match(/^[#?]+$/))return a=aa(r,0),a==="0"&&(a=""),a.length>t.length?a:Lr(t.substr(0,t.length-a.length))+a;if(s=t.match(pi))return Ol(s,o,u);if(t.match(/^#+0+$/))return u+aa(o,t.length-t.indexOf("0"));if(s=t.match(vi))return a=a0(r,s[1].length).replace(/^([^\.]+)$/,"$1."+Lr(s[1])).replace(/\.$/,"."+Lr(s[1])).replace(/\.(\d*)$/,function(g,m){return"."+m+lr("0",Lr(s[1]).length-m.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return u+a0(o,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return u+Ft(aa(o,0));if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Qr(e,t,-r):Ft(""+(Math.floor(r)+Rl(r,s[1].length)))+"."+lt(n0(r,s[1].length),s[1].length);if(s=t.match(/^#,#*,#0/))return Qr(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=ca(Qr(e,t.replace(/[\\-]/g,""),r)),i=0,ca(ca(t.replace(/\\/g,"")).replace(/[0#]/g,function(g){return i<a.length?a.charAt(i++):g==="0"?"0":""}));if(t.match(wi))return a=Qr(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=mn(o,Math.pow(10,i)-1,!1),a=""+u,f=yt("n",s[1],c[1]),f.charAt(f.length-1)==" "&&(f=f.substr(0,f.length-1)+"0"),a+=f+s[2]+"/"+s[3],f=xn(c[2],i),f.length<s[4].length&&(f=Lr(s[4].substr(s[4].length-f.length))+f),a+=f,a;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),c=mn(o,Math.pow(10,i)-1,!0),u+(c[0]||(c[1]?"":"0"))+" "+(c[1]?_s(c[1],i)+s[2]+"/"+s[3]+xn(c[2],i):lr(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return a=aa(r,0),t.length<=a.length?a:Lr(t.substr(0,t.length-a.length))+a;if(s=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=a.indexOf(".");var x=t.indexOf(".")-i,d=t.length-a.length-x;return Lr(t.substr(0,x)+a+t.substr(t.length-d))}if(s=t.match(/^00,000\.([#0]*0)$/))return i=n0(r,s[1].length),r<0?"-"+Qr(e,t,-r):Ft(Il(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(g){return"00,"+(g.length<3?lt(0,3-g.length):"")+g})+"."+lt(i,s[1].length);switch(t){case"###,##0.00":return Qr(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var p=Ft(aa(o,0));return p!=="0"?u+p:"";case"###,###.00":return Qr(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Qr(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function jl(e,t,r){for(var n=t.length-1;t.charCodeAt(n-1)===44;)--n;return yt(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}function Pl(e,t,r){var n=t.replace(mi,""),a=t.length-n.length;return yt(e,n,r*Math.pow(10,2*a))+lr("%",a)}function _i(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(t==0)return"0.0E+0";if(t<0)return"-"+_i(e,-t);var a=e.indexOf(".");a===-1&&(a=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%a;if(s<0&&(s+=a),r=(t/Math.pow(10,s)).toPrecision(n+1+(a+s)%a),!r.match(/[Ee]/)){var i=Math.floor(Math.log(t)*Math.LOG10E);r.indexOf(".")===-1?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(c,o,u,f){return o+u+f.substr(0,(a+s)%a)+"."+f.substr(s)+"E"})}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function ut(e,t,r){if(e.charCodeAt(0)===40&&!t.match(yi)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?ut("n",n,r):"("+ut("n",n,-r)+")"}if(t.charCodeAt(t.length-1)===44)return jl(e,t,r);if(t.indexOf("%")!==-1)return Pl(e,t,r);if(t.indexOf("E")!==-1)return _i(t,r);if(t.charCodeAt(0)===36)return"$"+ut(e,t.substr(t.charAt(1)==" "?2:1),r);var a,s,i,c,o=Math.abs(r),u=r<0?"-":"";if(t.match(/^00+$/))return u+lt(o,t.length);if(t.match(/^[#?]+$/))return a=""+r,r===0&&(a=""),a.length>t.length?a:Lr(t.substr(0,t.length-a.length))+a;if(s=t.match(pi))return Dl(s,o,u);if(t.match(/^#+0+$/))return u+lt(o,t.length-t.indexOf("0"));if(s=t.match(vi))return a=(""+r).replace(/^([^\.]+)$/,"$1."+Lr(s[1])).replace(/\.$/,"."+Lr(s[1])),a=a.replace(/\.(\d*)$/,function(g,m){return"."+m+lr("0",Lr(s[1]).length-m.length)}),t.indexOf("0.")!==-1?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return u+(""+o).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return u+Ft(""+o);if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+ut(e,t,-r):Ft(""+r)+"."+lr("0",s[1].length);if(s=t.match(/^#,#*,#0/))return ut(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=ca(ut(e,t.replace(/[\\-]/g,""),r)),i=0,ca(ca(t.replace(/\\/g,"")).replace(/[0#]/g,function(g){return i<a.length?a.charAt(i++):g==="0"?"0":""}));if(t.match(wi))return a=ut(e,"##########",r),"("+a.substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),c=mn(o,Math.pow(10,i)-1,!1),a=""+u,f=yt("n",s[1],c[1]),f.charAt(f.length-1)==" "&&(f=f.substr(0,f.length-1)+"0"),a+=f+s[2]+"/"+s[3],f=xn(c[2],i),f.length<s[4].length&&(f=Lr(s[4].substr(s[4].length-f.length))+f),a+=f,a;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),c=mn(o,Math.pow(10,i)-1,!0),u+(c[0]||(c[1]?"":"0"))+" "+(c[1]?_s(c[1],i)+s[2]+"/"+s[3]+xn(c[2],i):lr(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:Lr(t.substr(0,t.length-a.length))+a;if(s=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=a.indexOf(".");var x=t.indexOf(".")-i,d=t.length-a.length-x;return Lr(t.substr(0,x)+a+t.substr(t.length-d))}if(s=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+ut(e,t,-r):Ft(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(g){return"00,"+(g.length<3?lt(0,3-g.length):"")+g})+"."+lt(0,s[1].length);switch(t){case"###,###":case"##,###":case"#,###":var p=Ft(""+o);return p!=="0"?u+p:"";default:if(t.match(/\.[0#?]*$/))return ut(e,t.slice(0,t.lastIndexOf(".")),r)+Lr(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function yt(e,t,r){return(r|0)===r?ut(e,t,r):Qr(e,t,r)}function Ll(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),r===!0)throw new Error("Format |"+e+"| unterminated string ");return t}var Ei=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function ha(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":dn(e,t)&&(t+=6),t++;break;case'"':for(;e.charCodeAt(++t)!==34&&t<e.length;);++t;break;case"\\":t+=2;break;case"_":t+=2;break;case"@":++t;break;case"B":case"b":if(e.charAt(t+1)==="1"||e.charAt(t+1)==="2")return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if(e.substr(t,3).toUpperCase()==="A/P"||e.substr(t,5).toUpperCase()==="AM/PM"||e.substr(t,5).toUpperCase()==="上午/下午")return!0;++t;break;case"[":for(n=r;e.charAt(t++)!=="]"&&t<e.length;)n+=e.charAt(t);if(n.match(Ei))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||r=="\\"&&e.charAt(t+1)=="-"&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t,(e.charAt(t)==" "||e.charAt(t)=="*")&&++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;case" ":++t;break;default:++t;break}return!1}function Bl(e,t,r,n){for(var a=[],s="",i=0,c="",o="t",u,f,x,d="H";i<e.length;)switch(c=e.charAt(i)){case"G":if(!dn(e,i))throw new Error("unrecognized character "+c+" in "+e);a[a.length]={t:"G",v:"General"},i+=7;break;case'"':for(s="";(x=e.charCodeAt(++i))!==34&&i<e.length;)s+=String.fromCharCode(x);a[a.length]={t:"t",v:s},++i;break;case"\\":var p=e.charAt(++i),g=p==="("||p===")"?p:"t";a[a.length]={t:g,v:p},++i;break;case"_":a[a.length]={t:"t",v:" "},i+=2;break;case"@":a[a.length]={t:"T",v:t},++i;break;case"B":case"b":if(e.charAt(i+1)==="1"||e.charAt(i+1)==="2"){if(u==null&&(u=$t(t,r,e.charAt(i+1)==="2"),u==null))return"";a[a.length]={t:"X",v:e.substr(i,2)},o=c,i+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":c=c.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0||u==null&&(u=$t(t,r),u==null))return"";for(s=c;++i<e.length&&e.charAt(i).toLowerCase()===c;)s+=c;c==="m"&&o.toLowerCase()==="h"&&(c="M"),c==="h"&&(c=d),a[a.length]={t:c,v:s},o=c;break;case"A":case"a":case"上":var m={t:c,v:c};if(u==null&&(u=$t(t,r)),e.substr(i,3).toUpperCase()==="A/P"?(u!=null&&(m.v=u.H>=12?"P":"A"),m.t="T",d="h",i+=3):e.substr(i,5).toUpperCase()==="AM/PM"?(u!=null&&(m.v=u.H>=12?"PM":"AM"),m.t="T",i+=5,d="h"):e.substr(i,5).toUpperCase()==="上午/下午"?(u!=null&&(m.v=u.H>=12?"下午":"上午"),m.t="T",i+=5,d="h"):(m.t="t",++i),u==null&&m.t==="T")return"";a[a.length]=m,o=c;break;case"[":for(s=c;e.charAt(i++)!=="]"&&i<e.length;)s+=e.charAt(i);if(s.slice(-1)!=="]")throw'unterminated "[" block: |'+s+"|";if(s.match(Ei)){if(u==null&&(u=$t(t,r),u==null))return"";a[a.length]={t:"Z",v:s.toLowerCase()},o=s.charAt(1)}else s.indexOf("$")>-1&&(s=(s.match(/\$([^-\[\]]*)/)||[])[1]||"$",ha(e)||(a[a.length]={t:"t",v:s}));break;case".":if(u!=null){for(s=c;++i<e.length&&(c=e.charAt(i))==="0";)s+=c;a[a.length]={t:"s",v:s};break}case"0":case"#":for(s=c;++i<e.length&&"0#?.,E+-%".indexOf(c=e.charAt(i))>-1;)s+=c;a[a.length]={t:"n",v:s};break;case"?":for(s=c;e.charAt(++i)===c;)s+=c;a[a.length]={t:c,v:s},o=c;break;case"*":++i,(e.charAt(i)==" "||e.charAt(i)=="*")&&++i;break;case"(":case")":a[a.length]={t:n===1?"t":c,v:c},++i;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(s=c;i<e.length&&"0123456789".indexOf(e.charAt(++i))>-1;)s+=e.charAt(i);a[a.length]={t:"D",v:s};break;case" ":a[a.length]={t:c,v:c},++i;break;case"$":a[a.length]={t:"t",v:"$"},++i;break;default:if(",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(c)===-1)throw new Error("unrecognized character "+c+" in "+e);a[a.length]={t:"t",v:c},++i;break}var h=0,_=0,k;for(i=a.length-1,o="t";i>=0;--i)switch(a[i].t){case"h":case"H":a[i].t=d,o="h",h<1&&(h=1);break;case"s":(k=a[i].v.match(/\.0+$/))&&(_=Math.max(_,k[0].length-1)),h<3&&(h=3);case"d":case"y":case"M":case"e":o=a[i].t;break;case"m":o==="s"&&(a[i].t="M",h<2&&(h=2));break;case"X":break;case"Z":h<1&&a[i].v.match(/[Hh]/)&&(h=1),h<2&&a[i].v.match(/[Mm]/)&&(h=2),h<3&&a[i].v.match(/[Ss]/)&&(h=3)}switch(h){case 0:break;case 1:u.u>=.5&&(u.u=0,++u.S),u.S>=60&&(u.S=0,++u.M),u.M>=60&&(u.M=0,++u.H);break;case 2:u.u>=.5&&(u.u=0,++u.S),u.S>=60&&(u.S=0,++u.M);break}var v="",R;for(i=0;i<a.length;++i)switch(a[i].t){case"t":case"T":case" ":case"D":break;case"X":a[i].v="",a[i].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":a[i].v=Fl(a[i].t.charCodeAt(0),a[i].v,u,_),a[i].t="t";break;case"n":case"?":for(R=i+1;a[R]!=null&&((c=a[R].t)==="?"||c==="D"||(c===" "||c==="t")&&a[R+1]!=null&&(a[R+1].t==="?"||a[R+1].t==="t"&&a[R+1].v==="/")||a[i].t==="("&&(c===" "||c==="n"||c===")")||c==="t"&&(a[R].v==="/"||a[R].v===" "&&a[R+1]!=null&&a[R+1].t=="?"));)a[i].v+=a[R].v,a[R]={v:"",t:";"},++R;v+=a[i].v,i=R-1;break;case"G":a[i].t="t",a[i].v=Gt(t,r);break}var M="",S,w;if(v.length>0){v.charCodeAt(0)==40?(S=t<0&&v.charCodeAt(0)===45?-t:t,w=yt("n",v,S)):(S=t<0&&n>1?-t:t,w=yt("n",v,S),S<0&&a[0]&&a[0].t=="t"&&(w=w.substr(1),a[0].v="-"+a[0].v)),R=w.length-1;var j=a.length;for(i=0;i<a.length;++i)if(a[i]!=null&&a[i].t!="t"&&a[i].v.indexOf(".")>-1){j=i;break}var E=a.length;if(j===a.length&&w.indexOf("E")===-1){for(i=a.length-1;i>=0;--i)a[i]==null||"n?".indexOf(a[i].t)===-1||(R>=a[i].v.length-1?(R-=a[i].v.length,a[i].v=w.substr(R+1,a[i].v.length)):R<0?a[i].v="":(a[i].v=w.substr(0,R+1),R=-1),a[i].t="t",E=i);R>=0&&E<a.length&&(a[E].v=w.substr(0,R+1)+a[E].v)}else if(j!==a.length&&w.indexOf("E")===-1){for(R=w.indexOf(".")-1,i=j;i>=0;--i)if(!(a[i]==null||"n?".indexOf(a[i].t)===-1)){for(f=a[i].v.indexOf(".")>-1&&i===j?a[i].v.indexOf(".")-1:a[i].v.length-1,M=a[i].v.substr(f+1);f>=0;--f)R>=0&&(a[i].v.charAt(f)==="0"||a[i].v.charAt(f)==="#")&&(M=w.charAt(R--)+M);a[i].v=M,a[i].t="t",E=i}for(R>=0&&E<a.length&&(a[E].v=w.substr(0,R+1)+a[E].v),R=w.indexOf(".")+1,i=j;i<a.length;++i)if(!(a[i]==null||"n?(".indexOf(a[i].t)===-1&&i!==j)){for(f=a[i].v.indexOf(".")>-1&&i===j?a[i].v.indexOf(".")+1:0,M=a[i].v.substr(0,f);f<a[i].v.length;++f)R<w.length&&(M+=w.charAt(R++));a[i].v=M,a[i].t="t",E=i}}}for(i=0;i<a.length;++i)a[i]!=null&&"n?".indexOf(a[i].t)>-1&&(S=n>1&&t<0&&i>0&&a[i-1].v==="-"?-t:t,a[i].v=yt(a[i].t,a[i].v,S),a[i].t="t");var L="";for(i=0;i!==a.length;++i)a[i]!=null&&(L+=a[i].v);return L}var s0=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function i0(e,t){if(t==null)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0;break}return!1}function Ml(e,t){var r=Ll(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if(typeof t!="number")return[4,r.length===4||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"];break}var s=t>0?r[0]:t<0?r[1]:r[2];if(r[0].indexOf("[")===-1&&r[1].indexOf("[")===-1)return[n,s];if(r[0].match(/\[[=<>]/)!=null||r[1].match(/\[[=<>]/)!=null){var i=r[0].match(s0),c=r[1].match(s0);return i0(t,i)?[n,r[0]]:i0(t,c)?[n,r[1]]:[n,r[i!=null&&c!=null?2:1]]}return[n,s]}function tt(e,t,r){r==null&&(r={});var n="";switch(typeof e){case"string":e=="m/d/yy"&&r.dateNF?n=r.dateNF:n=e;break;case"number":e==14&&r.dateNF?n=r.dateNF:n=(r.table!=null?r.table:Me)[e],n==null&&(n=r.table&&r.table[t0[e]]||Me[t0[e]]),n==null&&(n=_l[e]||"General");break}if(dn(n,0))return Gt(t,r);t instanceof Date&&(t=di(t,r.date1904));var a=Ml(n,t);if(dn(a[1]))return Gt(t,r);if(t===!0)t="TRUE";else if(t===!1)t="FALSE";else if(t===""||t==null)return"";return Bl(a[1],t,r,a[0])}function Ut(e,t){if(typeof t!="number"){t=+t||-1;for(var r=0;r<392;++r){if(Me[r]==null){t<0&&(t=r);continue}if(Me[r]==e){t=r;break}}t<0&&(t=391)}return Me[t]=e,t}function Ti(){Me=wl()}var $l={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},bi=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function Ul(e){var t=typeof e=="number"?Me[e]:e;return t=t.replace(bi,"(\\d+)"),new RegExp("^"+t+"$")}function Hl(e,t,r){var n=-1,a=-1,s=-1,i=-1,c=-1,o=-1;(t.match(bi)||[]).forEach(function(x,d){var p=parseInt(r[d+1],10);switch(x.toLowerCase().charAt(0)){case"y":n=p;break;case"d":s=p;break;case"h":i=p;break;case"s":o=p;break;case"m":i>=0?c=p:a=p;break}}),o>=0&&c==-1&&a>=0&&(c=a,a=-1);var u=(""+(n>=0?n:new Date().getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);u.length==7&&(u="0"+u),u.length==8&&(u="20"+u);var f=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2);return i==-1&&c==-1&&o==-1?u:n==-1&&a==-1&&s==-1?f:u+"T"+f}var Gl=function(){var e={};e.version="1.2.0";function t(){for(var w=0,j=new Array(256),E=0;E!=256;++E)w=E,w=w&1?-306674912^w>>>1:w>>>1,w=w&1?-306674912^w>>>1:w>>>1,w=w&1?-306674912^w>>>1:w>>>1,w=w&1?-306674912^w>>>1:w>>>1,w=w&1?-306674912^w>>>1:w>>>1,w=w&1?-306674912^w>>>1:w>>>1,w=w&1?-306674912^w>>>1:w>>>1,w=w&1?-306674912^w>>>1:w>>>1,j[E]=w;return typeof Int32Array<"u"?new Int32Array(j):j}var r=t();function n(w){var j=0,E=0,L=0,G=typeof Int32Array<"u"?new Int32Array(4096):new Array(4096);for(L=0;L!=256;++L)G[L]=w[L];for(L=0;L!=256;++L)for(E=w[L],j=256+L;j<4096;j+=256)E=G[j]=E>>>8^w[E&255];var C=[];for(L=1;L!=16;++L)C[L-1]=typeof Int32Array<"u"?G.subarray(L*256,L*256+256):G.slice(L*256,L*256+256);return C}var a=n(r),s=a[0],i=a[1],c=a[2],o=a[3],u=a[4],f=a[5],x=a[6],d=a[7],p=a[8],g=a[9],m=a[10],h=a[11],_=a[12],k=a[13],v=a[14];function R(w,j){for(var E=j^-1,L=0,G=w.length;L<G;)E=E>>>8^r[(E^w.charCodeAt(L++))&255];return~E}function M(w,j){for(var E=j^-1,L=w.length-15,G=0;G<L;)E=v[w[G++]^E&255]^k[w[G++]^E>>8&255]^_[w[G++]^E>>16&255]^h[w[G++]^E>>>24]^m[w[G++]]^g[w[G++]]^p[w[G++]]^d[w[G++]]^x[w[G++]]^f[w[G++]]^u[w[G++]]^o[w[G++]]^c[w[G++]]^i[w[G++]]^s[w[G++]]^r[w[G++]];for(L+=15;G<L;)E=E>>>8^r[(E^w[G++])&255];return~E}function S(w,j){for(var E=j^-1,L=0,G=w.length,C=0,I=0;L<G;)C=w.charCodeAt(L++),C<128?E=E>>>8^r[(E^C)&255]:C<2048?(E=E>>>8^r[(E^(192|C>>6&31))&255],E=E>>>8^r[(E^(128|C&63))&255]):C>=55296&&C<57344?(C=(C&1023)+64,I=w.charCodeAt(L++)&1023,E=E>>>8^r[(E^(240|C>>8&7))&255],E=E>>>8^r[(E^(128|C>>2&63))&255],E=E>>>8^r[(E^(128|I>>6&15|(C&3)<<4))&255],E=E>>>8^r[(E^(128|I&63))&255]):(E=E>>>8^r[(E^(224|C>>12&15))&255],E=E>>>8^r[(E^(128|C>>6&63))&255],E=E>>>8^r[(E^(128|C&63))&255]);return~E}return e.table=r,e.bstr=R,e.buf=M,e.str=S,e}(),Xe=function(){var t={};t.version="1.2.1";function r(y,N){for(var T=y.split("/"),b=N.split("/"),A=0,O=0,X=Math.min(T.length,b.length);A<X;++A){if(O=T[A].length-b[A].length)return O;if(T[A]!=b[A])return T[A]<b[A]?-1:1}return T.length-b.length}function n(y){if(y.charAt(y.length-1)=="/")return y.slice(0,-1).indexOf("/")===-1?y:n(y.slice(0,-1));var N=y.lastIndexOf("/");return N===-1?y:y.slice(0,N+1)}function a(y){if(y.charAt(y.length-1)=="/")return a(y.slice(0,-1));var N=y.lastIndexOf("/");return N===-1?y:y.slice(N+1)}function s(y,N){typeof N=="string"&&(N=new Date(N));var T=N.getHours();T=T<<6|N.getMinutes(),T=T<<5|N.getSeconds()>>>1,y.write_shift(2,T);var b=N.getFullYear()-1980;b=b<<4|N.getMonth()+1,b=b<<5|N.getDate(),y.write_shift(2,b)}function i(y){var N=y.read_shift(2)&65535,T=y.read_shift(2)&65535,b=new Date,A=T&31;T>>>=5;var O=T&15;T>>>=4,b.setMilliseconds(0),b.setFullYear(T+1980),b.setMonth(O-1),b.setDate(A);var X=N&31;N>>>=5;var ee=N&63;return N>>>=6,b.setHours(N),b.setMinutes(ee),b.setSeconds(X<<1),b}function c(y){br(y,0);for(var N={},T=0;y.l<=y.length-4;){var b=y.read_shift(2),A=y.read_shift(2),O=y.l+A,X={};switch(b){case 21589:T=y.read_shift(1),T&1&&(X.mtime=y.read_shift(4)),A>5&&(T&2&&(X.atime=y.read_shift(4)),T&4&&(X.ctime=y.read_shift(4))),X.mtime&&(X.mt=new Date(X.mtime*1e3));break}y.l=O,N[b]=X}return N}var o;function u(){return o||(o={})}function f(y,N){if(y[0]==80&&y[1]==75)return Za(y,N);if((y[0]|32)==109&&(y[1]|32)==105)return ea(y,N);if(y.length<512)throw new Error("CFB file size "+y.length+" < 512");var T=3,b=512,A=0,O=0,X=0,ee=0,W=0,K=[],z=y.slice(0,512);br(z,0);var fe=x(z);switch(T=fe[0],T){case 3:b=512;break;case 4:b=4096;break;case 0:if(fe[1]==0)return Za(y,N);default:throw new Error("Major Version: Expected 3 or 4 saw "+T)}b!==512&&(z=y.slice(0,b),br(z,28));var ve=y.slice(0,b);d(z,T);var Te=z.read_shift(4,"i");if(T===3&&Te!==0)throw new Error("# Directory Sectors: Expected 0 saw "+Te);z.l+=4,X=z.read_shift(4,"i"),z.l+=4,z.chk("00100000","Mini Stream Cutoff Size: "),ee=z.read_shift(4,"i"),A=z.read_shift(4,"i"),W=z.read_shift(4,"i"),O=z.read_shift(4,"i");for(var ge=-1,_e=0;_e<109&&(ge=z.read_shift(4,"i"),!(ge<0));++_e)K[_e]=ge;var Pe=p(y,b);h(W,O,Pe,b,K);var nr=k(Pe,X,K,b);nr[X].name="!Directory",A>0&&ee!==I&&(nr[ee].name="!MiniFAT"),nr[K[0]].name="!FAT",nr.fat_addrs=K,nr.ssz=b;var tr={},_r=[],Tt=[],jt=[];v(X,nr,Pe,_r,A,tr,Tt,ee),g(Tt,jt,_r),_r.shift();var Pt={FileIndex:Tt,FullPaths:jt};return N&&N.raw&&(Pt.raw={header:ve,sectors:Pe}),Pt}function x(y){if(y[y.l]==80&&y[y.l+1]==75)return[0,0];y.chk(q,"Header Signature: "),y.l+=16;var N=y.read_shift(2,"u");return[y.read_shift(2,"u"),N]}function d(y,N){var T=9;switch(y.l+=2,T=y.read_shift(2)){case 9:if(N!=3)throw new Error("Sector Shift: Expected 9 saw "+T);break;case 12:if(N!=4)throw new Error("Sector Shift: Expected 12 saw "+T);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+T)}y.chk("0600","Mini Sector Shift: "),y.chk("000000000000","Reserved: ")}function p(y,N){for(var T=Math.ceil(y.length/N)-1,b=[],A=1;A<T;++A)b[A-1]=y.slice(A*N,(A+1)*N);return b[T-1]=y.slice(T*N),b}function g(y,N,T){for(var b=0,A=0,O=0,X=0,ee=0,W=T.length,K=[],z=[];b<W;++b)K[b]=z[b]=b,N[b]=T[b];for(;ee<z.length;++ee)b=z[ee],A=y[b].L,O=y[b].R,X=y[b].C,K[b]===b&&(A!==-1&&K[A]!==A&&(K[b]=K[A]),O!==-1&&K[O]!==O&&(K[b]=K[O])),X!==-1&&(K[X]=b),A!==-1&&b!=K[b]&&(K[A]=K[b],z.lastIndexOf(A)<ee&&z.push(A)),O!==-1&&b!=K[b]&&(K[O]=K[b],z.lastIndexOf(O)<ee&&z.push(O));for(b=1;b<W;++b)K[b]===b&&(O!==-1&&K[O]!==O?K[b]=K[O]:A!==-1&&K[A]!==A&&(K[b]=K[A]));for(b=1;b<W;++b)if(y[b].type!==0){if(ee=b,ee!=K[ee])do ee=K[ee],N[b]=N[ee]+"/"+N[b];while(ee!==0&&K[ee]!==-1&&ee!=K[ee]);K[b]=-1}for(N[0]+="/",b=1;b<W;++b)y[b].type!==2&&(N[b]+="/")}function m(y,N,T){for(var b=y.start,A=y.size,O=[],X=b;T&&A>0&&X>=0;)O.push(N.slice(X*C,X*C+C)),A-=C,X=Mt(T,X*4);return O.length===0?vr(0):St(O).slice(0,y.size)}function h(y,N,T,b,A){var O=I;if(y===I){if(N!==0)throw new Error("DIFAT chain shorter than expected")}else if(y!==-1){var X=T[y],ee=(b>>>2)-1;if(!X)return;for(var W=0;W<ee&&(O=Mt(X,W*4))!==I;++W)A.push(O);h(Mt(X,b-4),N-1,T,b,A)}}function _(y,N,T,b,A){var O=[],X=[];A||(A=[]);var ee=b-1,W=0,K=0;for(W=N;W>=0;){A[W]=!0,O[O.length]=W,X.push(y[W]);var z=T[Math.floor(W*4/b)];if(K=W*4&ee,b<4+K)throw new Error("FAT boundary crossed: "+W+" 4 "+b);if(!y[z])break;W=Mt(y[z],K)}return{nodes:O,data:g0([X])}}function k(y,N,T,b){var A=y.length,O=[],X=[],ee=[],W=[],K=b-1,z=0,fe=0,ve=0,Te=0;for(z=0;z<A;++z)if(ee=[],ve=z+N,ve>=A&&(ve-=A),!X[ve]){W=[];var ge=[];for(fe=ve;fe>=0;){ge[fe]=!0,X[fe]=!0,ee[ee.length]=fe,W.push(y[fe]);var _e=T[Math.floor(fe*4/b)];if(Te=fe*4&K,b<4+Te)throw new Error("FAT boundary crossed: "+fe+" 4 "+b);if(!y[_e]||(fe=Mt(y[_e],Te),ge[fe]))break}O[ve]={nodes:ee,data:g0([W])}}return O}function v(y,N,T,b,A,O,X,ee){for(var W=0,K=b.length?2:0,z=N[y].data,fe=0,ve=0,Te;fe<z.length;fe+=128){var ge=z.slice(fe,fe+128);br(ge,64),ve=ge.read_shift(2),Te=As(ge,0,ve-K),b.push(Te);var _e={name:Te,type:ge.read_shift(1),color:ge.read_shift(1),L:ge.read_shift(4,"i"),R:ge.read_shift(4,"i"),C:ge.read_shift(4,"i"),clsid:ge.read_shift(16),state:ge.read_shift(4,"i"),start:0,size:0},Pe=ge.read_shift(2)+ge.read_shift(2)+ge.read_shift(2)+ge.read_shift(2);Pe!==0&&(_e.ct=R(ge,ge.l-8));var nr=ge.read_shift(2)+ge.read_shift(2)+ge.read_shift(2)+ge.read_shift(2);nr!==0&&(_e.mt=R(ge,ge.l-8)),_e.start=ge.read_shift(4,"i"),_e.size=ge.read_shift(4,"i"),_e.size<0&&_e.start<0&&(_e.size=_e.type=0,_e.start=I,_e.name=""),_e.type===5?(W=_e.start,A>0&&W!==I&&(N[W].name="!StreamData")):_e.size>=4096?(_e.storage="fat",N[_e.start]===void 0&&(N[_e.start]=_(T,_e.start,N.fat_addrs,N.ssz)),N[_e.start].name=_e.name,_e.content=N[_e.start].data.slice(0,_e.size)):(_e.storage="minifat",_e.size<0?_e.size=0:W!==I&&_e.start!==I&&N[W]&&(_e.content=m(_e,N[W].data,(N[ee]||{}).data))),_e.content&&br(_e.content,0),O[Te]=_e,X.push(_e)}}function R(y,N){return new Date((Vr(y,N+4)/1e7*Math.pow(2,32)+Vr(y,N)/1e7-11644473600)*1e3)}function M(y,N){return u(),f(o.readFileSync(y),N)}function S(y,N){var T=N&&N.type;switch(T||We&&Buffer.isBuffer(y)&&(T="buffer"),T||"base64"){case"file":return M(y,N);case"base64":return f(it(Xr(y)),N);case"binary":return f(it(y),N)}return f(y,N)}function w(y,N){var T=N||{},b=T.root||"Root Entry";if(y.FullPaths||(y.FullPaths=[]),y.FileIndex||(y.FileIndex=[]),y.FullPaths.length!==y.FileIndex.length)throw new Error("inconsistent CFB structure");y.FullPaths.length===0&&(y.FullPaths[0]=b+"/",y.FileIndex[0]={name:b,type:5}),T.CLSID&&(y.FileIndex[0].clsid=T.CLSID),j(y)}function j(y){var N="Sh33tJ5";if(!Xe.find(y,"/"+N)){var T=vr(4);T[0]=55,T[1]=T[3]=50,T[2]=54,y.FileIndex.push({name:N,type:2,content:T,size:4,L:69,R:69,C:69}),y.FullPaths.push(y.FullPaths[0]+N),E(y)}}function E(y,N){w(y);for(var T=!1,b=!1,A=y.FullPaths.length-1;A>=0;--A){var O=y.FileIndex[A];switch(O.type){case 0:b?T=!0:(y.FileIndex.pop(),y.FullPaths.pop());break;case 1:case 2:case 5:b=!0,isNaN(O.R*O.L*O.C)&&(T=!0),O.R>-1&&O.L>-1&&O.R==O.L&&(T=!0);break;default:T=!0;break}}if(!(!T&&!N)){var X=new Date(1987,1,19),ee=0,W=Object.create?Object.create(null):{},K=[];for(A=0;A<y.FullPaths.length;++A)W[y.FullPaths[A]]=!0,y.FileIndex[A].type!==0&&K.push([y.FullPaths[A],y.FileIndex[A]]);for(A=0;A<K.length;++A){var z=n(K[A][0]);b=W[z],b||(K.push([z,{name:a(z).replace("/",""),type:1,clsid:ce,ct:X,mt:X,content:null}]),W[z]=!0)}for(K.sort(function(Te,ge){return r(Te[0],ge[0])}),y.FullPaths=[],y.FileIndex=[],A=0;A<K.length;++A)y.FullPaths[A]=K[A][0],y.FileIndex[A]=K[A][1];for(A=0;A<K.length;++A){var fe=y.FileIndex[A],ve=y.FullPaths[A];if(fe.name=a(ve).replace("/",""),fe.L=fe.R=fe.C=-(fe.color=1),fe.size=fe.content?fe.content.length:0,fe.start=0,fe.clsid=fe.clsid||ce,A===0)fe.C=K.length>1?1:-1,fe.size=0,fe.type=5;else if(ve.slice(-1)=="/"){for(ee=A+1;ee<K.length&&n(y.FullPaths[ee])!=ve;++ee);for(fe.C=ee>=K.length?-1:ee,ee=A+1;ee<K.length&&n(y.FullPaths[ee])!=n(ve);++ee);fe.R=ee>=K.length?-1:ee,fe.type=1}else n(y.FullPaths[A+1]||"")==n(ve)&&(fe.R=A+1),fe.type=2}}}function L(y,N){var T=N||{};if(T.fileType=="mad")return Rn(y,T);switch(E(y),T.fileType){case"zip":return On(y,T)}var b=function(Te){for(var ge=0,_e=0,Pe=0;Pe<Te.FileIndex.length;++Pe){var nr=Te.FileIndex[Pe];if(nr.content){var tr=nr.content.length;tr>0&&(tr<4096?ge+=tr+63>>6:_e+=tr+511>>9)}}for(var _r=Te.FullPaths.length+3>>2,Tt=ge+7>>3,jt=ge+127>>7,Pt=Tt+_e+_r+jt,gt=Pt+127>>7,ya=gt<=109?0:Math.ceil((gt-109)/127);Pt+gt+ya+127>>7>gt;)ya=++gt<=109?0:Math.ceil((gt-109)/127);var zr=[1,ya,gt,jt,_r,_e,ge,0];return Te.FileIndex[0].size=ge<<6,zr[7]=(Te.FileIndex[0].start=zr[0]+zr[1]+zr[2]+zr[3]+zr[4]+zr[5])+(zr[6]+7>>3),zr}(y),A=vr(b[7]<<9),O=0,X=0;{for(O=0;O<8;++O)A.write_shift(1,ae[O]);for(O=0;O<8;++O)A.write_shift(2,0);for(A.write_shift(2,62),A.write_shift(2,3),A.write_shift(2,65534),A.write_shift(2,9),A.write_shift(2,6),O=0;O<3;++O)A.write_shift(2,0);for(A.write_shift(4,0),A.write_shift(4,b[2]),A.write_shift(4,b[0]+b[1]+b[2]+b[3]-1),A.write_shift(4,0),A.write_shift(4,4096),A.write_shift(4,b[3]?b[0]+b[1]+b[2]-1:I),A.write_shift(4,b[3]),A.write_shift(-4,b[1]?b[0]-1:I),A.write_shift(4,b[1]),O=0;O<109;++O)A.write_shift(-4,O<b[2]?b[1]+O:-1)}if(b[1])for(X=0;X<b[1];++X){for(;O<236+X*127;++O)A.write_shift(-4,O<b[2]?b[1]+O:-1);A.write_shift(-4,X===b[1]-1?I:X+1)}var ee=function(Te){for(X+=Te;O<X-1;++O)A.write_shift(-4,O+1);Te&&(++O,A.write_shift(-4,I))};for(X=O=0,X+=b[1];O<X;++O)A.write_shift(-4,de.DIFSECT);for(X+=b[2];O<X;++O)A.write_shift(-4,de.FATSECT);ee(b[3]),ee(b[4]);for(var W=0,K=0,z=y.FileIndex[0];W<y.FileIndex.length;++W)z=y.FileIndex[W],z.content&&(K=z.content.length,!(K<4096)&&(z.start=X,ee(K+511>>9)));for(ee(b[6]+7>>3);A.l&511;)A.write_shift(-4,de.ENDOFCHAIN);for(X=O=0,W=0;W<y.FileIndex.length;++W)z=y.FileIndex[W],z.content&&(K=z.content.length,!(!K||K>=4096)&&(z.start=X,ee(K+63>>6)));for(;A.l&511;)A.write_shift(-4,de.ENDOFCHAIN);for(O=0;O<b[4]<<2;++O){var fe=y.FullPaths[O];if(!fe||fe.length===0){for(W=0;W<17;++W)A.write_shift(4,0);for(W=0;W<3;++W)A.write_shift(4,-1);for(W=0;W<12;++W)A.write_shift(4,0);continue}z=y.FileIndex[O],O===0&&(z.start=z.size?z.start-1:I);var ve=O===0&&T.root||z.name;if(K=2*(ve.length+1),A.write_shift(64,ve,"utf16le"),A.write_shift(2,K),A.write_shift(1,z.type),A.write_shift(1,z.color),A.write_shift(-4,z.L),A.write_shift(-4,z.R),A.write_shift(-4,z.C),z.clsid)A.write_shift(16,z.clsid,"hex");else for(W=0;W<4;++W)A.write_shift(4,0);A.write_shift(4,z.state||0),A.write_shift(4,0),A.write_shift(4,0),A.write_shift(4,0),A.write_shift(4,0),A.write_shift(4,z.start),A.write_shift(4,z.size),A.write_shift(4,0)}for(O=1;O<y.FileIndex.length;++O)if(z=y.FileIndex[O],z.size>=4096)if(A.l=z.start+1<<9,We&&Buffer.isBuffer(z.content))z.content.copy(A,A.l,0,z.size),A.l+=z.size+511&-512;else{for(W=0;W<z.size;++W)A.write_shift(1,z.content[W]);for(;W&511;++W)A.write_shift(1,0)}for(O=1;O<y.FileIndex.length;++O)if(z=y.FileIndex[O],z.size>0&&z.size<4096)if(We&&Buffer.isBuffer(z.content))z.content.copy(A,A.l,0,z.size),A.l+=z.size+63&-64;else{for(W=0;W<z.size;++W)A.write_shift(1,z.content[W]);for(;W&63;++W)A.write_shift(1,0)}if(We)A.l=A.length;else for(;A.l<A.length;)A.write_shift(1,0);return A}function G(y,N){var T=y.FullPaths.map(function(W){return W.toUpperCase()}),b=T.map(function(W){var K=W.split("/");return K[K.length-(W.slice(-1)=="/"?2:1)]}),A=!1;N.charCodeAt(0)===47?(A=!0,N=T[0].slice(0,-1)+N):A=N.indexOf("/")!==-1;var O=N.toUpperCase(),X=A===!0?T.indexOf(O):b.indexOf(O);if(X!==-1)return y.FileIndex[X];var ee=!O.match(ba);for(O=O.replace($r,""),ee&&(O=O.replace(ba,"!")),X=0;X<T.length;++X)if((ee?T[X].replace(ba,"!"):T[X]).replace($r,"")==O||(ee?b[X].replace(ba,"!"):b[X]).replace($r,"")==O)return y.FileIndex[X];return null}var C=64,I=-2,q="d0cf11e0a1b11ae1",ae=[208,207,17,224,161,177,26,225],ce="00000000000000000000000000000000",de={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:I,FREESECT:-1,HEADER_SIGNATURE:q,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:ce,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function ie(y,N,T){u();var b=L(y,T);o.writeFileSync(N,b)}function B(y){for(var N=new Array(y.length),T=0;T<y.length;++T)N[T]=String.fromCharCode(y[T]);return N.join("")}function re(y,N){var T=L(y,N);switch(N&&N.type||"buffer"){case"file":return u(),o.writeFileSync(N.filename,T),T;case"binary":return typeof T=="string"?T:B(T);case"base64":return Zs(typeof T=="string"?T:B(T));case"buffer":if(We)return Buffer.isBuffer(T)?T:Xt(T);case"array":return typeof T=="string"?it(T):T}return T}var he;function F(y){try{var N=y.InflateRaw,T=new N;if(T._processChunk(new Uint8Array([3,0]),T._finishFlushFlag),T.bytesRead)he=y;else throw new Error("zlib does not expose bytesRead")}catch(b){console.error("cannot use native zlib: "+(b.message||b))}}function U(y,N){if(!he)return qa(y,N);var T=he.InflateRaw,b=new T,A=b._processChunk(y.slice(y.l),b._finishFlushFlag);return y.l+=b.bytesRead,A}function H(y){return he?he.deflateRawSync(y):$e(y)}var P=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Q=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],me=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function ye(y){var N=(y<<1|y<<11)&139536|(y<<5|y<<15)&558144;return(N>>16|N>>8|N)&255}for(var oe=typeof Uint8Array<"u",le=oe?new Uint8Array(256):[],Ee=0;Ee<256;++Ee)le[Ee]=ye(Ee);function D(y,N){var T=le[y&255];return N<=8?T>>>8-N:(T=T<<8|le[y>>8&255],N<=16?T>>>16-N:(T=T<<8|le[y>>16&255],T>>>24-N))}function ke(y,N){var T=N&7,b=N>>>3;return(y[b]|(T<=6?0:y[b+1]<<8))>>>T&3}function Oe(y,N){var T=N&7,b=N>>>3;return(y[b]|(T<=5?0:y[b+1]<<8))>>>T&7}function Ie(y,N){var T=N&7,b=N>>>3;return(y[b]|(T<=4?0:y[b+1]<<8))>>>T&15}function je(y,N){var T=N&7,b=N>>>3;return(y[b]|(T<=3?0:y[b+1]<<8))>>>T&31}function xe(y,N){var T=N&7,b=N>>>3;return(y[b]|(T<=1?0:y[b+1]<<8))>>>T&127}function Ve(y,N,T){var b=N&7,A=N>>>3,O=(1<<T)-1,X=y[A]>>>b;return T<8-b||(X|=y[A+1]<<8-b,T<16-b)||(X|=y[A+2]<<16-b,T<24-b)||(X|=y[A+3]<<24-b),X&O}function er(y,N,T){var b=N&7,A=N>>>3;return b<=5?y[A]|=(T&7)<<b:(y[A]|=T<<b&255,y[A+1]=(T&7)>>8-b),N+3}function qe(y,N,T){var b=N&7,A=N>>>3;return T=(T&1)<<b,y[A]|=T,N+1}function te(y,N,T){var b=N&7,A=N>>>3;return T<<=b,y[A]|=T&255,T>>>=8,y[A+1]=T,N+8}function ne(y,N,T){var b=N&7,A=N>>>3;return T<<=b,y[A]|=T&255,T>>>=8,y[A+1]=T&255,y[A+2]=T>>>8,N+16}function Ne(y,N){var T=y.length,b=2*T>N?2*T:N+5,A=0;if(T>=N)return y;if(We){var O=Qs(b);if(y.copy)y.copy(O);else for(;A<y.length;++A)O[A]=y[A];return O}else if(oe){var X=new Uint8Array(b);if(X.set)X.set(y);else for(;A<T;++A)X[A]=y[A];return X}return y.length=b,y}function Be(y){for(var N=new Array(y),T=0;T<y;++T)N[T]=0;return N}function De(y,N,T){var b=1,A=0,O=0,X=0,ee=0,W=y.length,K=oe?new Uint16Array(32):Be(32);for(O=0;O<32;++O)K[O]=0;for(O=W;O<T;++O)y[O]=0;W=y.length;var z=oe?new Uint16Array(W):Be(W);for(O=0;O<W;++O)K[A=y[O]]++,b<A&&(b=A),z[O]=0;for(K[0]=0,O=1;O<=b;++O)K[O+16]=ee=ee+K[O-1]<<1;for(O=0;O<W;++O)ee=y[O],ee!=0&&(z[O]=K[ee+16]++);var fe=0;for(O=0;O<W;++O)if(fe=y[O],fe!=0)for(ee=D(z[O],b)>>b-fe,X=(1<<b+4-fe)-1;X>=0;--X)N[ee|X<<fe]=fe&15|O<<4;return b}var Ge=oe?new Uint16Array(512):Be(512),Ye=oe?new Uint16Array(32):Be(32);if(!oe){for(var Ze=0;Ze<512;++Ze)Ge[Ze]=0;for(Ze=0;Ze<32;++Ze)Ye[Ze]=0}(function(){for(var y=[],N=0;N<32;N++)y.push(5);De(y,Ye,32);var T=[];for(N=0;N<=143;N++)T.push(8);for(;N<=255;N++)T.push(9);for(;N<=279;N++)T.push(7);for(;N<=287;N++)T.push(8);De(T,Ge,288)})();var fr=function(){for(var N=oe?new Uint8Array(32768):[],T=0,b=0;T<me.length-1;++T)for(;b<me[T+1];++b)N[b]=T;for(;b<32768;++b)N[b]=29;var A=oe?new Uint8Array(259):[];for(T=0,b=0;T<Q.length-1;++T)for(;b<Q[T+1];++b)A[b]=T;function O(ee,W){for(var K=0;K<ee.length;){var z=Math.min(65535,ee.length-K),fe=K+z==ee.length;for(W.write_shift(1,+fe),W.write_shift(2,z),W.write_shift(2,~z&65535);z-- >0;)W[W.l++]=ee[K++]}return W.l}function X(ee,W){for(var K=0,z=0,fe=oe?new Uint16Array(32768):[];z<ee.length;){var ve=Math.min(65535,ee.length-z);if(ve<10){for(K=er(W,K,+(z+ve==ee.length)),K&7&&(K+=8-(K&7)),W.l=K/8|0,W.write_shift(2,ve),W.write_shift(2,~ve&65535);ve-- >0;)W[W.l++]=ee[z++];K=W.l*8;continue}K=er(W,K,+(z+ve==ee.length)+2);for(var Te=0;ve-- >0;){var ge=ee[z];Te=(Te<<5^ge)&32767;var _e=-1,Pe=0;if((_e=fe[Te])&&(_e|=z&-32768,_e>z&&(_e-=32768),_e<z))for(;ee[_e+Pe]==ee[z+Pe]&&Pe<250;)++Pe;if(Pe>2){ge=A[Pe],ge<=22?K=te(W,K,le[ge+1]>>1)-1:(te(W,K,3),K+=5,te(W,K,le[ge-23]>>5),K+=3);var nr=ge<8?0:ge-4>>2;nr>0&&(ne(W,K,Pe-Q[ge]),K+=nr),ge=N[z-_e],K=te(W,K,le[ge]>>3),K-=3;var tr=ge<4?0:ge-2>>1;tr>0&&(ne(W,K,z-_e-me[ge]),K+=tr);for(var _r=0;_r<Pe;++_r)fe[Te]=z&32767,Te=(Te<<5^ee[z])&32767,++z;ve-=Pe-1}else ge<=143?ge=ge+48:K=qe(W,K,1),K=te(W,K,le[ge]),fe[Te]=z&32767,++z}K=te(W,K,0)-1}return W.l=(K+7)/8|0,W.l}return function(W,K){return W.length<8?O(W,K):X(W,K)}}();function $e(y){var N=vr(50+Math.floor(y.length*1.1)),T=fr(y,N);return N.slice(0,T)}var rr=oe?new Uint16Array(32768):Be(32768),Pr=oe?new Uint16Array(32768):Be(32768),Qe=oe?new Uint16Array(128):Be(128),Kr=1,Ar=1;function mt(y,N){var T=je(y,N)+257;N+=5;var b=je(y,N)+1;N+=5;var A=Ie(y,N)+4;N+=4;for(var O=0,X=oe?new Uint8Array(19):Be(19),ee=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],W=1,K=oe?new Uint8Array(8):Be(8),z=oe?new Uint8Array(8):Be(8),fe=X.length,ve=0;ve<A;++ve)X[P[ve]]=O=Oe(y,N),W<O&&(W=O),K[O]++,N+=3;var Te=0;for(K[0]=0,ve=1;ve<=W;++ve)z[ve]=Te=Te+K[ve-1]<<1;for(ve=0;ve<fe;++ve)(Te=X[ve])!=0&&(ee[ve]=z[Te]++);var ge=0;for(ve=0;ve<fe;++ve)if(ge=X[ve],ge!=0){Te=le[ee[ve]]>>8-ge;for(var _e=(1<<7-ge)-1;_e>=0;--_e)Qe[Te|_e<<ge]=ge&7|ve<<3}var Pe=[];for(W=1;Pe.length<T+b;)switch(Te=Qe[xe(y,N)],N+=Te&7,Te>>>=3){case 16:for(O=3+ke(y,N),N+=2,Te=Pe[Pe.length-1];O-- >0;)Pe.push(Te);break;case 17:for(O=3+Oe(y,N),N+=3;O-- >0;)Pe.push(0);break;case 18:for(O=11+xe(y,N),N+=7;O-- >0;)Pe.push(0);break;default:Pe.push(Te),W<Te&&(W=Te);break}var nr=Pe.slice(0,T),tr=Pe.slice(T);for(ve=T;ve<286;++ve)nr[ve]=0;for(ve=b;ve<30;++ve)tr[ve]=0;return Kr=De(nr,rr,286),Ar=De(tr,Pr,30),N}function Ws(y,N){if(y[0]==3&&!(y[1]&3))return[Ct(N),2];for(var T=0,b=0,A=Qs(N||1<<18),O=0,X=A.length>>>0,ee=0,W=0;(b&1)==0;){if(b=Oe(y,T),T+=3,b>>>1)b>>1==1?(ee=9,W=5):(T=mt(y,T),ee=Kr,W=Ar);else{T&7&&(T+=8-(T&7));var K=y[T>>>3]|y[(T>>>3)+1]<<8;if(T+=32,K>0)for(!N&&X<O+K&&(A=Ne(A,O+K),X=A.length);K-- >0;)A[O++]=y[T>>>3],T+=8;continue}for(;;){!N&&X<O+32767&&(A=Ne(A,O+32767),X=A.length);var z=Ve(y,T,ee),fe=b>>>1==1?Ge[z]:rr[z];if(T+=fe&15,fe>>>=4,(fe>>>8&255)===0)A[O++]=fe;else{if(fe==256)break;fe-=257;var ve=fe<8?0:fe-4>>2;ve>5&&(ve=0);var Te=O+Q[fe];ve>0&&(Te+=Ve(y,T,ve),T+=ve),z=Ve(y,T,W),fe=b>>>1==1?Ye[z]:Pr[z],T+=fe&15,fe>>>=4;var ge=fe<4?0:fe-2>>1,_e=me[fe];for(ge>0&&(_e+=Ve(y,T,ge),T+=ge),!N&&X<Te&&(A=Ne(A,Te+100),X=A.length);O<Te;)A[O]=A[O-_e],++O}}}return N?[A,T+7>>>3]:[A.slice(0,O),T+7>>>3]}function qa(y,N){var T=y.slice(y.l||0),b=Ws(T,N);return y.l+=b[1],b[0]}function Zt(y,N){if(y)typeof console<"u"&&console.error(N);else throw new Error(N)}function Za(y,N){var T=y;br(T,0);var b=[],A=[],O={FileIndex:b,FullPaths:A};w(O,{root:N.root});for(var X=T.length-4;(T[X]!=80||T[X+1]!=75||T[X+2]!=5||T[X+3]!=6)&&X>=0;)--X;T.l=X+4,T.l+=4;var ee=T.read_shift(2);T.l+=6;var W=T.read_shift(4);for(T.l=W,X=0;X<ee;++X){T.l+=20;var K=T.read_shift(4),z=T.read_shift(4),fe=T.read_shift(2),ve=T.read_shift(2),Te=T.read_shift(2);T.l+=8;var ge=T.read_shift(4),_e=c(T.slice(T.l+fe,T.l+fe+ve));T.l+=fe+ve+Te;var Pe=T.l;T.l=ge+4,Cn(T,K,z,O,_e),T.l=Pe}return O}function Cn(y,N,T,b,A){y.l+=2;var O=y.read_shift(2),X=y.read_shift(2),ee=i(y);if(O&8257)throw new Error("Unsupported ZIP encryption");for(var W=y.read_shift(4),K=y.read_shift(4),z=y.read_shift(4),fe=y.read_shift(2),ve=y.read_shift(2),Te="",ge=0;ge<fe;++ge)Te+=String.fromCharCode(y[y.l++]);if(ve){var _e=c(y.slice(y.l,y.l+ve));(_e[21589]||{}).mt&&(ee=_e[21589].mt),((A||{})[21589]||{}).mt&&(ee=A[21589].mt)}y.l+=ve;var Pe=y.slice(y.l,y.l+K);switch(X){case 8:Pe=U(y,z);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+X)}var nr=!1;O&8&&(W=y.read_shift(4),W==134695760&&(W=y.read_shift(4),nr=!0),K=y.read_shift(4),z=y.read_shift(4)),K!=N&&Zt(nr,"Bad compressed size: "+N+" != "+K),z!=T&&Zt(nr,"Bad uncompressed size: "+T+" != "+z),va(b,Te,Pe,{unsafe:!0,mt:ee})}function On(y,N){var T=N||{},b=[],A=[],O=vr(1),X=T.compression?8:0,ee=0,W=0,K=0,z=0,fe=0,ve=y.FullPaths[0],Te=ve,ge=y.FileIndex[0],_e=[],Pe=0;for(W=1;W<y.FullPaths.length;++W)if(Te=y.FullPaths[W].slice(ve.length),ge=y.FileIndex[W],!(!ge.size||!ge.content||Te=="Sh33tJ5")){var nr=z,tr=vr(Te.length);for(K=0;K<Te.length;++K)tr.write_shift(1,Te.charCodeAt(K)&127);tr=tr.slice(0,tr.l),_e[fe]=Gl.buf(ge.content,0);var _r=ge.content;X==8&&(_r=H(_r)),O=vr(30),O.write_shift(4,67324752),O.write_shift(2,20),O.write_shift(2,ee),O.write_shift(2,X),ge.mt?s(O,ge.mt):O.write_shift(4,0),O.write_shift(-4,_e[fe]),O.write_shift(4,_r.length),O.write_shift(4,ge.content.length),O.write_shift(2,tr.length),O.write_shift(2,0),z+=O.length,b.push(O),z+=tr.length,b.push(tr),z+=_r.length,b.push(_r),O=vr(46),O.write_shift(4,33639248),O.write_shift(2,0),O.write_shift(2,20),O.write_shift(2,ee),O.write_shift(2,X),O.write_shift(4,0),O.write_shift(-4,_e[fe]),O.write_shift(4,_r.length),O.write_shift(4,ge.content.length),O.write_shift(2,tr.length),O.write_shift(2,0),O.write_shift(2,0),O.write_shift(2,0),O.write_shift(2,0),O.write_shift(4,0),O.write_shift(4,nr),Pe+=O.l,A.push(O),Pe+=tr.length,A.push(tr),++fe}return O=vr(22),O.write_shift(4,101010256),O.write_shift(2,0),O.write_shift(2,0),O.write_shift(2,fe),O.write_shift(2,fe),O.write_shift(4,Pe),O.write_shift(4,z),O.write_shift(2,0),St([St(b),St(A),O])}var Qt={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Rt(y,N){if(y.ctype)return y.ctype;var T=y.name||"",b=T.match(/\.([^\.]+)$/);return b&&Qt[b[1]]||N&&(b=(T=N).match(/[\.\\]([^\.\\])+$/),b&&Qt[b[1]])?Qt[b[1]]:"application/octet-stream"}function It(y){for(var N=Zs(y),T=[],b=0;b<N.length;b+=76)T.push(N.slice(b,b+76));return T.join(`\r
`)+`\r
`}function ga(y){var N=y.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(K){var z=K.charCodeAt(0).toString(16).toUpperCase();return"="+(z.length==1?"0"+z:z)});N=N.replace(/ $/mg,"=20").replace(/\t$/mg,"=09"),N.charAt(0)==`
`&&(N="=0D"+N.slice(1)),N=N.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,`
=0A`).replace(/([^\r\n])\n/mg,"$1=0A");for(var T=[],b=N.split(`\r
`),A=0;A<b.length;++A){var O=b[A];if(O.length==0){T.push("");continue}for(var X=0;X<O.length;){var ee=76,W=O.slice(X,X+ee);W.charAt(ee-1)=="="?ee--:W.charAt(ee-2)=="="?ee-=2:W.charAt(ee-3)=="="&&(ee-=3),W=O.slice(X,X+ee),X+=ee,X<O.length&&(W+="="),T.push(W)}}return T.join(`\r
`)}function pa(y){for(var N=[],T=0;T<y.length;++T){for(var b=y[T];T<=y.length&&b.charAt(b.length-1)=="=";)b=b.slice(0,b.length-1)+y[++T];N.push(b)}for(var A=0;A<N.length;++A)N[A]=N[A].replace(/[=][0-9A-Fa-f]{2}/g,function(O){return String.fromCharCode(parseInt(O.slice(1),16))});return it(N.join(`\r
`))}function Dn(y,N,T){for(var b="",A="",O="",X,ee=0;ee<10;++ee){var W=N[ee];if(!W||W.match(/^\s*$/))break;var K=W.match(/^(.*?):\s*([^\s].*)$/);if(K)switch(K[1].toLowerCase()){case"content-location":b=K[2].trim();break;case"content-type":O=K[2].trim();break;case"content-transfer-encoding":A=K[2].trim();break}}switch(++ee,A.toLowerCase()){case"base64":X=it(Xr(N.slice(ee).join("")));break;case"quoted-printable":X=pa(N.slice(ee));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+A)}var z=va(y,b.slice(T.length),X,{unsafe:!0});O&&(z.ctype=O)}function ea(y,N){if(B(y.slice(0,13)).toLowerCase()!="mime-version:")throw new Error("Unsupported MAD header");var T=N&&N.root||"",b=(We&&Buffer.isBuffer(y)?y.toString("binary"):B(y)).split(`\r
`),A=0,O="";for(A=0;A<b.length;++A)if(O=b[A],!!/^Content-Location:/i.test(O)&&(O=O.slice(O.indexOf("file")),T||(T=O.slice(0,O.lastIndexOf("/")+1)),O.slice(0,T.length)!=T))for(;T.length>0&&(T=T.slice(0,T.length-1),T=T.slice(0,T.lastIndexOf("/")+1),O.slice(0,T.length)!=T););var X=(b[1]||"").match(/boundary="(.*?)"/);if(!X)throw new Error("MAD cannot find boundary");var ee="--"+(X[1]||""),W=[],K=[],z={FileIndex:W,FullPaths:K};w(z);var fe,ve=0;for(A=0;A<b.length;++A){var Te=b[A];Te!==ee&&Te!==ee+"--"||(ve++&&Dn(z,b.slice(fe,A),T),fe=A)}return z}function Rn(y,N){var T=N||{},b=T.boundary||"SheetJS";b="------="+b;for(var A=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+b.slice(2)+'"',"","",""],O=y.FullPaths[0],X=O,ee=y.FileIndex[0],W=1;W<y.FullPaths.length;++W)if(X=y.FullPaths[W].slice(O.length),ee=y.FileIndex[W],!(!ee.size||!ee.content||X=="Sh33tJ5")){X=X.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(Pe){return"_x"+Pe.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(Pe){return"_u"+Pe.charCodeAt(0).toString(16)+"_"});for(var K=ee.content,z=We&&Buffer.isBuffer(K)?K.toString("binary"):B(K),fe=0,ve=Math.min(1024,z.length),Te=0,ge=0;ge<=ve;++ge)(Te=z.charCodeAt(ge))>=32&&Te<128&&++fe;var _e=fe>=ve*4/5;A.push(b),A.push("Content-Location: "+(T.root||"file:///C:/SheetJS/")+X),A.push("Content-Transfer-Encoding: "+(_e?"quoted-printable":"base64")),A.push("Content-Type: "+Rt(ee,X)),A.push(""),A.push(_e?ga(z):It(z))}return A.push(b+`--\r
`),A.join(`\r
`)}function In(y){var N={};return w(N,y),N}function va(y,N,T,b){var A=b&&b.unsafe;A||w(y);var O=!A&&Xe.find(y,N);if(!O){var X=y.FullPaths[0];N.slice(0,X.length)==X?X=N:(X.slice(-1)!="/"&&(X+="/"),X=(X+N).replace("//","/")),O={name:a(N),type:2},y.FileIndex.push(O),y.FullPaths.push(X),A||Xe.utils.cfb_gc(y)}return O.content=T,O.size=T?T.length:0,b&&(b.CLSID&&(O.clsid=b.CLSID),b.mt&&(O.mt=b.mt),b.ct&&(O.ct=b.ct)),O}function jn(y,N){w(y);var T=Xe.find(y,N);if(T){for(var b=0;b<y.FileIndex.length;++b)if(y.FileIndex[b]==T)return y.FileIndex.splice(b,1),y.FullPaths.splice(b,1),!0}return!1}function Pn(y,N,T){w(y);var b=Xe.find(y,N);if(b){for(var A=0;A<y.FileIndex.length;++A)if(y.FileIndex[A]==b)return y.FileIndex[A].name=a(T),y.FullPaths[A]=T,!0}return!1}function ra(y){E(y,!0)}return t.find=G,t.read=S,t.parse=f,t.write=re,t.writeFile=ie,t.utils={cfb_new:In,cfb_add:va,cfb_del:jn,cfb_mov:Pn,cfb_gc:ra,ReadShift:Fa,CheckField:Xi,prep_blob:br,bconcat:St,use_zlib:F,_deflateRaw:$e,_inflateRaw:qa,consts:de},t}();function Vl(e){if(typeof Deno<"u")return Deno.readFileSync(e);if(typeof $<"u"&&typeof File<"u"&&typeof Folder<"u")try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(n){if(!n.message||!n.message.match(/onstruct/))throw n}throw new Error("Cannot access file "+e)}function ht(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function Ts(e){for(var t=[],r=ht(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}var gn=new Date(1899,11,30,0,0,0);function Ur(e,t){var r=e.getTime(),n=gn.getTime()+(e.getTimezoneOffset()-gn.getTimezoneOffset())*6e4;return(r-n)/(1440*60*1e3)}var ki=new Date,Wl=gn.getTime()+(ki.getTimezoneOffset()-gn.getTimezoneOffset())*6e4,c0=ki.getTimezoneOffset();function Sn(e){var t=new Date;return t.setTime(e*24*60*60*1e3+Wl),t.getTimezoneOffset()!==c0&&t.setTime(t.getTime()+(t.getTimezoneOffset()-c0)*6e4),t}function Xl(e){var t=0,r=0,n=!1,a=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!a)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=a.length;++s)if(a[s]){switch(r=1,s>3&&(n=!0),a[s].slice(a[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+a[s].slice(a[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(n)r*=60;else throw new Error("Unsupported ISO Duration Field: M")}t+=r*parseInt(a[s],10)}return t}var l0=new Date("2017-02-19T19:06:09.000Z"),Si=isNaN(l0.getFullYear())?new Date("2/19/17"):l0,Kl=Si.getFullYear()==2017;function Tr(e,t){var r=new Date(e);if(Kl)return t>0?r.setTime(r.getTime()+r.getTimezoneOffset()*60*1e3):t<0&&r.setTime(r.getTime()-r.getTimezoneOffset()*60*1e3),r;if(e instanceof Date)return e;if(Si.getFullYear()==1917&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-s.getTimezoneOffset()*60*1e3)),s}function Vt(e,t){if(We&&Buffer.isBuffer(e)){if(t){if(e[0]==255&&e[1]==254)return Sa(e.slice(2).toString("utf16le"));if(e[1]==254&&e[2]==255)return Sa(hi(e.slice(2).toString("binary")))}return e.toString("binary")}if(typeof TextDecoder<"u")try{if(t){if(e[0]==255&&e[1]==254)return Sa(new TextDecoder("utf-16le").decode(e.slice(2)));if(e[0]==254&&e[1]==255)return Sa(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(s){return r[s]||s})}catch{}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function kr(e){if(typeof JSON<"u"&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if(typeof e!="object"||e==null)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=kr(e[r]));return t}function lr(e,t){for(var r="";r.length<t;)r+=e;return r}function ot(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return!isNaN(t=Number(n))||(n=n.replace(/[(](.*)[)]/,function(a,s){return r=-r,s}),!isNaN(t=Number(n)))?t/r:t}var zl=["january","february","march","april","may","june","july","august","september","october","november","december"];function fa(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),s=t.getDate();if(isNaN(s))return r;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),i.length>3&&zl.indexOf(i)==-1)return r}else if(i.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||s>1)&&n!=101?t:e.match(/[^-0-9:,\/\\]/)?r:t}var Yl=function(){var e="abacaba".split(/(:?b)/i).length==5;return function(r,n,a){if(e||typeof n=="string")return r.split(n);for(var s=r.split(n),i=[s[0]],c=1;c<s.length;++c)i.push(a),i.push(s[c]);return i}}();function Ai(e){return e?e.content&&e.type?Vt(e.content,!0):e.data?Ta(e.data):e.asNodeBuffer&&We?Ta(e.asNodeBuffer().toString("binary")):e.asBinary?Ta(e.asBinary()):e._data&&e._data.getContent?Ta(Vt(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function Fi(e){if(!e)return null;if(e.data)return Js(e.data);if(e.asNodeBuffer&&We)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return typeof t=="string"?Js(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function Jl(e){return e&&e.name.slice(-4)===".bin"?Fi(e):Ai(e)}function et(e,t){for(var r=e.FullPaths||ht(e.files),n=t.toLowerCase().replace(/[\/]/g,"\\"),a=n.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(n==i||a==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function bs(e,t){var r=et(e,t);if(r==null)throw new Error("Cannot find file "+t+" in zip");return r}function dr(e,t,r){if(!r)return Jl(bs(e,t));if(!t)return null;try{return dr(e,t)}catch{return null}}function Wr(e,t,r){if(!r)return Ai(bs(e,t));if(!t)return null;try{return Wr(e,t)}catch{return null}}function ql(e,t,r){return Fi(bs(e,t))}function o0(e){for(var t=e.FullPaths||ht(e.files),r=[],n=0;n<t.length;++n)t[n].slice(-1)!="/"&&r.push(t[n].replace(/^Root Entry[\/]/,""));return r.sort()}function Zl(e,t,r){if(e.FullPaths){if(typeof r=="string"){var n;return We?n=Xt(r):n=pl(r),Xe.utils.cfb_add(e,t,n)}Xe.utils.cfb_add(e,t,r)}else e.file(t,r)}function Ni(e,t){switch(t.type){case"base64":return Xe.read(e,{type:"base64"});case"binary":return Xe.read(e,{type:"binary"});case"buffer":case"array":return Xe.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function ka(e,t){if(e.charAt(0)=="/")return e.slice(1);var r=t.split("/");t.slice(-1)!="/"&&r.pop();for(var n=e.split("/");n.length!==0;){var a=n.shift();a===".."?r.pop():a!=="."&&r.push(a)}return r.join("/")}var Ci=`<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r
`,Ql=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,f0=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,eo=/<[^>]*>/g,jr=Ci.match(f0)?f0:eo,ro=/<\w*:/,to=/<(\/?)\w+:/;function Re(e,t,r){for(var n={},a=0,s=0;a!==e.length&&!((s=e.charCodeAt(a))===32||s===10||s===13);++a);if(t||(n[0]=e.slice(0,a)),a===e.length)return n;var i=e.match(Ql),c=0,o="",u=0,f="",x="",d=1;if(i)for(u=0;u!=i.length;++u){for(x=i[u],s=0;s!=x.length&&x.charCodeAt(s)!==61;++s);for(f=x.slice(0,s).trim();x.charCodeAt(s+1)==32;)++s;for(d=(a=x.charCodeAt(s+1))==34||a==39?1:0,o=x.slice(s+1+d,x.length-d),c=0;c!=f.length&&f.charCodeAt(c)!==58;++c);if(c===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),n[f]=o,n[f.toLowerCase()]=o;else{var p=(c===5&&f.slice(0,5)==="xmlns"?"xmlns":"")+f.slice(c+1);if(n[p]&&f.slice(c-3,c)=="ext")continue;n[p]=o,n[p.toLowerCase()]=o}}return n}function xt(e){return e.replace(to,"<$1")}var Oi={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},ao=Ts(Oi),ze=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,t=/_x([\da-fA-F]{4})_/ig;return function r(n){var a=n+"",s=a.indexOf("<![CDATA[");if(s==-1)return a.replace(e,function(c,o){return Oi[c]||String.fromCharCode(parseInt(o,c.indexOf("x")>-1?16:10))||c}).replace(t,function(c,o){return String.fromCharCode(parseInt(o,16))});var i=a.indexOf("]]>");return r(a.slice(0,s))+a.slice(s+9,i)+r(a.slice(i+3))}}(),no=/[&<>'"]/g,so=/[\u0000-\u001f]/g;function ks(e){var t=e+"";return t.replace(no,function(r){return ao[r]}).replace(/\n/g,"<br/>").replace(so,function(r){return"&#x"+("000"+r.charCodeAt(0).toString(16)).slice(-4)+";"})}var u0=function(){var e=/&#(\d+);/g;function t(r,n){return String.fromCharCode(parseInt(n,10))}return function(n){return n.replace(e,t)}}();function cr(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Gn(e){for(var t="",r=0,n=0,a=0,s=0,i=0,c=0;r<e.length;){if(n=e.charCodeAt(r++),n<128){t+=String.fromCharCode(n);continue}if(a=e.charCodeAt(r++),n>191&&n<224){i=(n&31)<<6,i|=a&63,t+=String.fromCharCode(i);continue}if(s=e.charCodeAt(r++),n<240){t+=String.fromCharCode((n&15)<<12|(a&63)<<6|s&63);continue}i=e.charCodeAt(r++),c=((n&7)<<18|(a&63)<<12|(s&63)<<6|i&63)-65536,t+=String.fromCharCode(55296+(c>>>10&1023)),t+=String.fromCharCode(56320+(c&1023))}return t}function h0(e){var t=Ct(2*e.length),r,n,a=1,s=0,i=0,c;for(n=0;n<e.length;n+=a)a=1,(c=e.charCodeAt(n))<128?r=c:c<224?(r=(c&31)*64+(e.charCodeAt(n+1)&63),a=2):c<240?(r=(c&15)*4096+(e.charCodeAt(n+1)&63)*64+(e.charCodeAt(n+2)&63),a=3):(a=4,r=(c&7)*262144+(e.charCodeAt(n+1)&63)*4096+(e.charCodeAt(n+2)&63)*64+(e.charCodeAt(n+3)&63),r-=65536,i=55296+(r>>>10&1023),r=56320+(r&1023)),i!==0&&(t[s++]=i&255,t[s++]=i>>>8,i=0),t[s++]=r%256,t[s++]=r>>>8;return t.slice(0,s).toString("ucs2")}function x0(e){return Xt(e,"binary").toString("utf8")}var rn="foo bar bazâð£",ar=We&&(x0(rn)==Gn(rn)&&x0||h0(rn)==Gn(rn)&&h0)||Gn,Sa=We?function(e){return Xt(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(n&63)));break;case(n>=55296&&n<57344):n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(a&63)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(n&63)))}return t.join("")},Ba=function(){var e={};return function(r,n){var a=r+"|"+(n||"");return e[a]?e[a]:e[a]=new RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",n||"")}}(),Di=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(t){return[new RegExp("&"+t[0]+";","ig"),t[1]]});return function(r){for(var n=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,`
`).replace(/<[^>]*>/g,""),a=0;a<e.length;++a)n=n.replace(e[a][0],e[a][1]);return n}}(),io=function(){var e={};return function(r){return e[r]!==void 0?e[r]:e[r]=new RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),co=/<\/?(?:vt:)?variant>/g,lo=/<(?:vt:)([^>]*)>([\s\S]*)</;function d0(e,t){var r=Re(e),n=e.match(io(r.baseType))||[],a=[];if(n.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+n.length+" != "+r.size);return a}return n.forEach(function(s){var i=s.replace(co,"").match(lo);i&&a.push({v:ar(i[2]),t:i[1]})}),a}var oo=/(^\s|\s$|\n)/;function fo(e){return ht(e).map(function(t){return" "+t+'="'+e[t]+'"'}).join("")}function uo(e,t,r){return"<"+e+(r!=null?fo(r):"")+(t!=null?(t.match(oo)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function Ss(e){if(We&&Buffer.isBuffer(e))return e.toString("utf8");if(typeof e=="string")return e;if(typeof Uint8Array<"u"&&e instanceof Uint8Array)return ar(Kt(ws(e)));throw new Error("Bad input format: expected Buffer or string")}var Ma=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,ho={CT:"http://schemas.openxmlformats.org/package/2006/content-types"},xo=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"];function mo(e,t){for(var r=1-2*(e[t+7]>>>7),n=((e[t+7]&127)<<4)+(e[t+6]>>>4&15),a=e[t+6]&15,s=5;s>=0;--s)a=a*256+e[t+s];return n==2047?a==0?r*(1/0):NaN:(n==0?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}function go(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,s=0,i=n?-t:t;isFinite(i)?i==0?a=s=0:(a=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-a),a<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?a=-1022:(s-=Math.pow(2,52),a+=1023)):(a=2047,s=isNaN(t)?26985:0);for(var c=0;c<=5;++c,s/=256)e[r+c]=s&255;e[r+6]=(a&15)<<4|s&15,e[r+7]=a>>4|n}var m0=function(e){for(var t=[],r=10240,n=0;n<e[0].length;++n)if(e[0][n])for(var a=0,s=e[0][n].length;a<s;a+=r)t.push.apply(t,e[0][n].slice(a,a+r));return t},g0=We?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(t){return Buffer.isBuffer(t)?t:Xt(t)})):m0(e)}:m0,p0=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(vt(e,a)));return n.join("").replace($r,"")},As=We?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace($r,""):p0(e,t,r)}:p0,v0=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},Ri=We?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):v0(e,t,r)}:v0,y0=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(na(e,a)));return n.join("")},Wa=We?function(t,r,n){return Buffer.isBuffer(t)?t.toString("utf8",r,n):y0(t,r,n)}:y0,Ii=function(e,t){var r=Vr(e,t);return r>0?Wa(e,t+4,t+4+r-1):""},ji=Ii,Pi=function(e,t){var r=Vr(e,t);return r>0?Wa(e,t+4,t+4+r-1):""},Li=Pi,Bi=function(e,t){var r=2*Vr(e,t);return r>0?Wa(e,t+4,t+4+r-1):""},Mi=Bi,$i=function(t,r){var n=Vr(t,r);return n>0?As(t,r+4,r+4+n):""},Ui=$i,Hi=function(e,t){var r=Vr(e,t);return r>0?Wa(e,t+4,t+4+r):""},Gi=Hi,Vi=function(e,t){return mo(e,t)},pn=Vi,Wi=function(t){return Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array};We&&(ji=function(t,r){if(!Buffer.isBuffer(t))return Ii(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Li=function(t,r){if(!Buffer.isBuffer(t))return Pi(t,r);var n=t.readUInt32LE(r);return n>0?t.toString("utf8",r+4,r+4+n-1):""},Mi=function(t,r){if(!Buffer.isBuffer(t))return Bi(t,r);var n=2*t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n-1)},Ui=function(t,r){if(!Buffer.isBuffer(t))return $i(t,r);var n=t.readUInt32LE(r);return t.toString("utf16le",r+4,r+4+n)},Gi=function(t,r){if(!Buffer.isBuffer(t))return Hi(t,r);var n=t.readUInt32LE(r);return t.toString("utf8",r+4,r+4+n)},pn=function(t,r){return Buffer.isBuffer(t)?t.readDoubleLE(r):Vi(t,r)},Wi=function(t){return Buffer.isBuffer(t)||Array.isArray(t)||typeof Uint8Array<"u"&&t instanceof Uint8Array});var na=function(e,t){return e[t]},vt=function(e,t){return e[t+1]*256+e[t]},po=function(e,t){var r=e[t+1]*256+e[t];return r<32768?r:(65535-r+1)*-1},Vr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Mt=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},vo=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Fa(e,t){var r="",n,a,s=[],i,c,o,u;switch(t){case"dbcs":if(u=this.l,We&&Buffer.isBuffer(this))r=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)r+=String.fromCharCode(vt(this,u)),u+=2;e*=2;break;case"utf8":r=Wa(this,this.l,this.l+e);break;case"utf16le":e*=2,r=As(this,this.l,this.l+e);break;case"wstr":return Fa.call(this,e,"dbcs");case"lpstr-ansi":r=ji(this,this.l),e=4+Vr(this,this.l);break;case"lpstr-cp":r=Li(this,this.l),e=4+Vr(this,this.l);break;case"lpwstr":r=Mi(this,this.l),e=4+2*Vr(this,this.l);break;case"lpp4":e=4+Vr(this,this.l),r=Ui(this,this.l),e&2&&(e+=2);break;case"8lpp4":e=4+Vr(this,this.l),r=Gi(this,this.l),e&3&&(e+=4-(e&3));break;case"cstr":for(e=0,r="";(i=na(this,this.l+e++))!==0;)s.push(en(i));r=s.join("");break;case"_wstr":for(e=0,r="";(i=vt(this,this.l+e))!==0;)s.push(en(i)),e+=2;e+=2,r=s.join("");break;case"dbcs-cont":for(r="",u=this.l,o=0;o<e;++o){if(this.lens&&this.lens.indexOf(u)!==-1)return i=na(this,u),this.l=u+1,c=Fa.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),s.join("")+c;s.push(en(vt(this,u))),u+=2}r=s.join(""),e*=2;break;case"cpstr":case"sbcs-cont":for(r="",u=this.l,o=0;o!=e;++o){if(this.lens&&this.lens.indexOf(u)!==-1)return i=na(this,u),this.l=u+1,c=Fa.call(this,e-o,i?"dbcs-cont":"sbcs-cont"),s.join("")+c;s.push(en(na(this,u))),u+=1}r=s.join("");break;default:switch(e){case 1:return n=na(this,this.l),this.l++,n;case 2:return n=(t==="i"?po:vt)(this,this.l),this.l+=2,n;case 4:case-4:return t==="i"||(this[this.l+3]&128)===0?(n=(e>0?Mt:vo)(this,this.l),this.l+=4,n):(a=Vr(this,this.l),this.l+=4,a);case 8:case-8:if(t==="f")return e==8?a=pn(this,this.l):a=pn([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:r=Ri(this,this.l,e);break}}return this.l+=e,r}var yo=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},wo=function(e,t,r){e[r]=t&255,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},_o=function(e,t,r){e[r]=t&255,e[r+1]=t>>>8&255};function Eo(e,t,r){var n=0,a=0;if(r==="dbcs"){for(a=0;a!=t.length;++a)_o(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if(r==="sbcs"){for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=t.charCodeAt(a)&255;n=t.length}else if(r==="hex"){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}else if(r==="utf16le"){var s=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var i=t.charCodeAt(a);this[this.l++]=i&255,this[this.l++]=i>>8}for(;this.l<s;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=t&255;break;case 2:n=2,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255;break;case 3:n=3,this[this.l]=t&255,t>>>=8,this[this.l+1]=t&255,t>>>=8,this[this.l+2]=t&255;break;case 4:n=4,yo(this,t,this.l);break;case 8:if(n=8,r==="f"){go(this,t,this.l);break}case 16:break;case-4:n=4,wo(this,t,this.l);break}return this.l+=n,this}function Xi(e,t){var r=Ri(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function br(e,t){e.l=t,e.read_shift=Fa,e.chk=Xi,e.write_shift=Eo}function Ir(e,t){e.l+=t}function vr(e){var t=Ct(e);return br(t,0),t}function Et(e,t,r){if(e){var n,a,s;br(e,e.l||0);for(var i=e.length,c=0,o=0;e.l<i;){c=e.read_shift(1),c&128&&(c=(c&127)+((e.read_shift(1)&127)<<7));var u=Tn[c]||Tn[65535];for(n=e.read_shift(1),s=n&127,a=1;a<4&&n&128;++a)s+=((n=e.read_shift(1))&127)<<7*a;o=e.l+s;var f=u.f&&u.f(e,s,r);if(e.l=o,t(f,u,c))return}}}function as(){var e=[],t=We?256:2048,r=function(u){var f=vr(u);return br(f,0),f},n=r(t),a=function(){n&&(n.length>n.l&&(n=n.slice(0,n.l),n.l=n.length),n.length>0&&e.push(n),n=null)},s=function(u){return n&&u<n.length-n.l?n:(a(),n=r(Math.max(u+1,t)))},i=function(){return a(),St(e)},c=function(u){a(),n=u,n.l==null&&(n.l=n.length),s(t)};return{next:s,push:c,end:i,_bufs:e}}function Na(e,t,r){var n=kr(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function w0(e,t,r){var n=kr(e);return n.s=Na(n.s,t.s,r),n.e=Na(n.e,t.s,r),n}function Ca(e,t){if(e.cRel&&e.c<0)for(e=kr(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=kr(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=Le(e);return!e.cRel&&e.cRel!=null&&(r=ko(r)),!e.rRel&&e.rRel!=null&&(r=To(r)),r}function Vn(e,t){return e.s.r==0&&!e.s.rRel&&e.e.r==(t.biff>=12?1048575:t.biff>=8?65536:16384)&&!e.e.rRel?(e.s.cRel?"":"$")+yr(e.s.c)+":"+(e.e.cRel?"":"$")+yr(e.e.c):e.s.c==0&&!e.s.cRel&&e.e.c==(t.biff>=12?16383:255)&&!e.e.cRel?(e.s.rRel?"":"$")+Sr(e.s.r)+":"+(e.e.rRel?"":"$")+Sr(e.e.r):Ca(e.s,t.biff)+":"+Ca(e.e,t.biff)}function Fs(e){return parseInt(bo(e),10)-1}function Sr(e){return""+(e+1)}function To(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function bo(e){return e.replace(/\$(\d+)$/,"$1")}function Ns(e){for(var t=So(e),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function yr(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function ko(e){return e.replace(/^([A-Z])/,"$$$1")}function So(e){return e.replace(/^\$([A-Z])/,"$1")}function Ao(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Mr(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function Le(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function xa(e){var t=e.indexOf(":");return t==-1?{s:Mr(e),e:Mr(e)}:{s:Mr(e.slice(0,t)),e:Mr(e.slice(t+1))}}function Ke(e,t){return typeof t>"u"||typeof t=="number"?Ke(e.s,e.e):(typeof e!="string"&&(e=Le(e)),typeof t!="string"&&(t=Le(t)),e==t?e:e+":"+t)}function or(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,s=e.length;for(r=0;n<s&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<s&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;if(t.s.r=--r,n===s||a!=10)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=s&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=s&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;return t.e.r=--r,t}function _0(e,t){var r=e.t=="d"&&t instanceof Date;if(e.z!=null)try{return e.w=tt(e.z,r?Ur(t):t)}catch{}try{return e.w=tt((e.XF||{}).numFmtId||(r?14:0),r?Ur(t):t)}catch{return""+t}}function _t(e,t,r){return e==null||e.t==null||e.t=="z"?"":e.w!==void 0?e.w:(e.t=="d"&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),e.t=="e"?Jt[e.v]||e.v:t==null?_0(e,e.v):_0(e,t))}function Dt(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function Ki(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense,s=e||(a?[]:{}),i=0,c=0;if(s&&n.origin!=null){if(typeof n.origin=="number")i=n.origin;else{var o=typeof n.origin=="string"?Mr(n.origin):n.origin;i=o.r,c=o.c}s["!ref"]||(s["!ref"]="A1:A1")}var u={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=or(s["!ref"]);u.s.c=f.s.c,u.s.r=f.s.r,u.e.c=Math.max(u.e.c,f.e.c),u.e.r=Math.max(u.e.r,f.e.r),i==-1&&(u.e.r=i=f.e.r+1)}for(var x=0;x!=t.length;++x)if(t[x]){if(!Array.isArray(t[x]))throw new Error("aoa_to_sheet expects an array of arrays");for(var d=0;d!=t[x].length;++d)if(!(typeof t[x][d]>"u")){var p={v:t[x][d]},g=i+x,m=c+d;if(u.s.r>g&&(u.s.r=g),u.s.c>m&&(u.s.c=m),u.e.r<g&&(u.e.r=g),u.e.c<m&&(u.e.c=m),t[x][d]&&typeof t[x][d]=="object"&&!Array.isArray(t[x][d])&&!(t[x][d]instanceof Date))p=t[x][d];else if(Array.isArray(p.v)&&(p.f=t[x][d][1],p.v=p.v[0]),p.v===null)if(p.f)p.t="n";else if(n.nullError)p.t="e",p.v=0;else if(n.sheetStubs)p.t="z";else continue;else typeof p.v=="number"?p.t="n":typeof p.v=="boolean"?p.t="b":p.v instanceof Date?(p.z=n.dateNF||Me[14],n.cellDates?(p.t="d",p.w=tt(p.z,Ur(p.v))):(p.t="n",p.v=Ur(p.v),p.w=tt(p.z,p.v))):p.t="s";if(a)s[g]||(s[g]=[]),s[g][m]&&s[g][m].z&&(p.z=s[g][m].z),s[g][m]=p;else{var h=Le({c:m,r:g});s[h]&&s[h].z&&(p.z=s[h].z),s[h]=p}}}return u.s.c<1e7&&(s["!ref"]=Ke(u)),s}function da(e,t){return Ki(null,e,t)}function Fo(e){return e.read_shift(4,"i")}function Rr(e){var t=e.read_shift(4);return t===0?"":e.read_shift(t,"dbcs")}function No(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Cs(e,t){var r=e.l,n=e.read_shift(1),a=Rr(e),s=[],i={t:a,h:a};if((n&1)!==0){for(var c=e.read_shift(4),o=0;o!=c;++o)s.push(No(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}var Co=Cs;function at(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function zt(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}var Oo=Rr;function Os(e){var t=e.read_shift(4);return t===0||t===4294967295?"":e.read_shift(t,"dbcs")}var Do=Rr,ns=Os;function Ds(e){var t=e.slice(e.l,e.l+4),r=t[0]&1,n=t[0]&2;e.l+=4;var a=n===0?pn([0,0,0,0,t[0]&252,t[1],t[2],t[3]],0):Mt(t,0)>>2;return r?a/100:a}function zi(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var Yt=zi;function Or(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Ro(e){var t={},r=e.read_shift(1),n=r>>>1,a=e.read_shift(1),s=e.read_shift(2,"i"),i=e.read_shift(1),c=e.read_shift(1),o=e.read_shift(1);switch(e.l++,n){case 0:t.auto=1;break;case 1:t.index=a;var u=Ht[a];u&&(t.rgb=Ua(u));break;case 2:t.rgb=Ua([i,c,o]);break;case 3:t.theme=a;break}return s!=0&&(t.tint=s>0?s/32767:s/32768),t}function Io(e){var t=e.read_shift(1);e.l++;var r={fBold:t&1,fItalic:t&2,fUnderline:t&4,fStrikeout:t&8,fOutline:t&16,fShadow:t&32,fCondense:t&64,fExtend:t&128};return r}function Yi(e,t){var r={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"},n=e.read_shift(4);switch(n){case 0:return"";case 4294967295:case 4294967294:return r[e.read_shift(4)]||""}if(n>400)throw new Error("Unsupported Clipboard: "+n.toString(16));return e.l-=4,e.read_shift(0,t==1?"lpstr":"lpwstr")}function jo(e){return Yi(e,1)}function Po(e){return Yi(e,2)}var Rs=2,Hr=3,tn=11,E0=12,vn=19,an=64,Lo=65,Bo=71,Mo=4108,$o=4126,Er=80,Ji=81,Uo=[Er,Ji],Ho={1:{n:"CodePage",t:Rs},2:{n:"Category",t:Er},3:{n:"PresentationFormat",t:Er},4:{n:"ByteCount",t:Hr},5:{n:"LineCount",t:Hr},6:{n:"ParagraphCount",t:Hr},7:{n:"SlideCount",t:Hr},8:{n:"NoteCount",t:Hr},9:{n:"HiddenCount",t:Hr},10:{n:"MultimediaClipCount",t:Hr},11:{n:"ScaleCrop",t:tn},12:{n:"HeadingPairs",t:Mo},13:{n:"TitlesOfParts",t:$o},14:{n:"Manager",t:Er},15:{n:"Company",t:Er},16:{n:"LinksUpToDate",t:tn},17:{n:"CharacterCount",t:Hr},19:{n:"SharedDoc",t:tn},22:{n:"HyperlinksChanged",t:tn},23:{n:"AppVersion",t:Hr,p:"version"},24:{n:"DigSig",t:Lo},26:{n:"ContentType",t:Er},27:{n:"ContentStatus",t:Er},28:{n:"Language",t:Er},29:{n:"Version",t:Er},255:{},2147483648:{n:"Locale",t:vn},2147483651:{n:"Behavior",t:vn},1919054434:{}},Go={1:{n:"CodePage",t:Rs},2:{n:"Title",t:Er},3:{n:"Subject",t:Er},4:{n:"Author",t:Er},5:{n:"Keywords",t:Er},6:{n:"Comments",t:Er},7:{n:"Template",t:Er},8:{n:"LastAuthor",t:Er},9:{n:"RevNumber",t:Er},10:{n:"EditTime",t:an},11:{n:"LastPrinted",t:an},12:{n:"CreatedDate",t:an},13:{n:"ModifiedDate",t:an},14:{n:"PageCount",t:Hr},15:{n:"WordCount",t:Hr},16:{n:"CharCount",t:Hr},17:{n:"Thumbnail",t:Bo},18:{n:"Application",t:Er},19:{n:"DocSecurity",t:Hr},255:{},2147483648:{n:"Locale",t:vn},2147483651:{n:"Behavior",t:vn},1919054434:{}},T0={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},Vo=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function Wo(e){return e.map(function(t){return[t>>16&255,t>>8&255,t&255]})}var Xo=Wo([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Ht=kr(Xo),Jt={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},qi={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},b0={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"};function Ko(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function zo(e){var t=Ko();if(!e||!e.match)return t;var r={};if((e.match(jr)||[]).forEach(function(n){var a=Re(n);switch(a[0].replace(ro,"<")){case"<?xml":break;case"<Types":t.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[a.Extension]=a.ContentType;break;case"<Override":t[b0[a.ContentType]]!==void 0&&t[b0[a.ContentType]].push(a.PartName);break}}),t.xmlns!==ho.CT)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}var sa={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function ss(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function Oa(e,t){var r={"!id":{}};if(!e)return r;t.charAt(0)!=="/"&&(t="/"+t);var n={};return(e.match(jr)||[]).forEach(function(a){var s=Re(a);if(s[0]==="<Relationship"){var i={};i.Type=s.Type,i.Target=s.Target,i.Id=s.Id,s.TargetMode&&(i.TargetMode=s.TargetMode);var c=s.TargetMode==="External"?s.Target:ka(s.Target,t);r[c]=i,n[s.Id]=i}}),r["!id"]=n,r}var Yo="application/vnd.oasis.opendocument.spreadsheet";function Jo(e,t){for(var r=Ss(e),n,a;n=Ma.exec(r);)switch(n[3]){case"manifest":break;case"file-entry":if(a=Re(n[0],!1),a.path=="/"&&a.type!==Yo)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw n}}var Da=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],qo=function(){for(var e=new Array(Da.length),t=0;t<Da.length;++t){var r=Da[t],n="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+n+"[^>]*>([\\s\\S]*?)</"+n+">")}return e}();function Zi(e){var t={};e=ar(e);for(var r=0;r<Da.length;++r){var n=Da[r],a=e.match(qo[r]);a!=null&&a.length>0&&(t[n[1]]=ze(a[1])),n[2]==="date"&&t[n[1]]&&(t[n[1]]=Tr(t[n[1]]))}return t}var Zo=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];function Qi(e,t,r,n){var a=[];if(typeof e=="string")a=d0(e,n);else for(var s=0;s<e.length;++s)a=a.concat(e[s].map(function(f){return{v:f}}));var i=typeof t=="string"?d0(t,n).map(function(f){return f.v}):t,c=0,o=0;if(i.length>0)for(var u=0;u!==a.length;u+=2){switch(o=+a[u+1].v,a[u].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=o,r.SheetNames=i.slice(c,c+o);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=o,r.DefinedNames=i.slice(c,c+o);break;case"Charts":case"Diagramme":r.Chartsheets=o,r.ChartNames=i.slice(c,c+o);break}c+=o}}function Qo(e,t,r){var n={};return t||(t={}),e=ar(e),Zo.forEach(function(a){var s=(e.match(Ba(a[0]))||[])[1];switch(a[2]){case"string":s&&(t[a[1]]=ze(s));break;case"bool":t[a[1]]=s==="true";break;case"raw":var i=e.match(new RegExp("<"+a[0]+"[^>]*>([\\s\\S]*?)</"+a[0]+">"));i&&i.length>0&&(n[a[1]]=i[1]);break}}),n.HeadingPairs&&n.TitlesOfParts&&Qi(n.HeadingPairs,n.TitlesOfParts,t,r),t}var ef=/<[^>]+>[^<]*/g;function rf(e,t){var r={},n="",a=e.match(ef);if(a)for(var s=0;s!=a.length;++s){var i=a[s],c=Re(i);switch(c[0]){case"<?xml":break;case"<Properties":break;case"<property":n=ze(c.name);break;case"</property>":n=null;break;default:if(i.indexOf("<vt:")===0){var o=i.split(">"),u=o[0].slice(4),f=o[1];switch(u){case"lpstr":case"bstr":case"lpwstr":r[n]=ze(f);break;case"bool":r[n]=cr(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[n]=parseInt(f,10);break;case"r4":case"r8":case"decimal":r[n]=parseFloat(f);break;case"filetime":case"date":r[n]=Tr(f);break;case"cy":case"error":r[n]=ze(f);break;default:if(u.slice(-1)=="/")break;t.WTF&&typeof console<"u"&&console.warn("Unexpected",i,u,o)}}else if(i.slice(0,2)!=="</"){if(t.WTF)throw new Error(i)}}}return r}var tf={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"},Wn;function af(e,t,r){Wn||(Wn=Ts(tf)),t=Wn[t]||t,e[t]=r}function Is(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date((r/1e7*Math.pow(2,32)+t/1e7-11644473600)*1e3).toISOString().replace(/\.000/,"")}function ec(e,t,r){var n=e.l,a=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-n&3;)++e.l;return a}function rc(e,t,r){var n=e.read_shift(0,"lpwstr");return n}function tc(e,t,r){return t===31?rc(e):ec(e,t,r)}function is(e,t,r){return tc(e,t,r===!1?0:4)}function nf(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return tc(e,t,0)}function sf(e){for(var t=e.read_shift(4),r=[],n=0;n!=t;++n){var a=e.l;r[n]=e.read_shift(0,"lpwstr").replace($r,""),e.l-a&2&&(e.l+=2)}return r}function cf(e){for(var t=e.read_shift(4),r=[],n=0;n!=t;++n)r[n]=e.read_shift(0,"lpstr-cp").replace($r,"");return r}function lf(e){var t=e.l,r=yn(e,Ji);e[e.l]==0&&e[e.l+1]==0&&e.l-t&2&&(e.l+=2);var n=yn(e,Hr);return[r,n]}function of(e){for(var t=e.read_shift(4),r=[],n=0;n<t/2;++n)r.push(lf(e));return r}function k0(e,t){for(var r=e.read_shift(4),n={},a=0;a!=r;++a){var s=e.read_shift(4),i=e.read_shift(4);n[s]=e.read_shift(i,t===1200?"utf16le":"utf8").replace($r,"").replace(ba,"!"),t===1200&&i%2&&(e.l+=2)}return e.l&3&&(e.l=e.l>>3<<2),n}function ac(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(t&3)>0&&(e.l+=4-(t&3)&3),r}function ff(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}function yn(e,t,r){var n=e.read_shift(2),a,s=r||{};if(e.l+=2,t!==E0&&n!==t&&Uo.indexOf(t)===-1&&!((t&65534)==4126&&(n&65534)==4126))throw new Error("Expected type "+t+" saw "+n);switch(t===E0?n:t){case 2:return a=e.read_shift(2,"i"),s.raw||(e.l+=2),a;case 3:return a=e.read_shift(4,"i"),a;case 11:return e.read_shift(4)!==0;case 19:return a=e.read_shift(4),a;case 30:return ec(e,n,4).replace($r,"");case 31:return rc(e);case 64:return Is(e);case 65:return ac(e);case 71:return ff(e);case 80:return is(e,n,!s.raw).replace($r,"");case 81:return nf(e,n).replace($r,"");case 4108:return of(e);case 4126:case 4127:return n==4127?sf(e):cf(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+n)}}function S0(e,t){var r=e.l,n=e.read_shift(4),a=e.read_shift(4),s=[],i=0,c=0,o=-1,u={};for(i=0;i!=a;++i){var f=e.read_shift(4),x=e.read_shift(4);s[i]=[f,x+r]}s.sort(function(k,v){return k[1]-v[1]});var d={};for(i=0;i!=a;++i){if(e.l!==s[i][1]){var p=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,p=!1);break;case 80:e.l<=s[i][1]&&(e.l=s[i][1],p=!1);break;case 4108:e.l<=s[i][1]&&(e.l=s[i][1],p=!1);break}if((!t||i==0)&&e.l<=s[i][1]&&(p=!1,e.l=s[i][1]),p)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var g=t[s[i][0]];if(d[g.n]=yn(e,g.t,{raw:!0}),g.p==="version"&&(d[g.n]=String(d[g.n]>>16)+"."+("0000"+String(d[g.n]&65535)).slice(-4)),g.n=="CodePage")switch(d[g.n]){case 0:d[g.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:ct(c=d[g.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+d[g.n])}}else if(s[i][0]===1){if(c=d.CodePage=yn(e,Rs),ct(c),o!==-1){var m=e.l;e.l=s[o][1],u=k0(e,c),e.l=m}}else if(s[i][0]===0){if(c===0){o=i,e.l=s[i+1][1];continue}u=k0(e,c)}else{var h=u[s[i][0]],_;switch(e[e.l]){case 65:e.l+=4,_=ac(e);break;case 30:e.l+=4,_=is(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4,_=is(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,_=e.read_shift(4,"i");break;case 19:e.l+=4,_=e.read_shift(4);break;case 5:e.l+=4,_=e.read_shift(8,"f");break;case 11:e.l+=4,_=ur(e,4);break;case 64:e.l+=4,_=Tr(Is(e));break;default:throw new Error("unparsed value: "+e[e.l])}d[h]=_}}return e.l=r+n,d}function A0(e,t,r){var n=e.content;if(!n)return{};br(n,0);var a,s,i,c,o=0;n.chk("feff","Byte Order: "),n.read_shift(2);var u=n.read_shift(4),f=n.read_shift(16);if(f!==Xe.utils.consts.HEADER_CLSID&&f!==r)throw new Error("Bad PropertySet CLSID "+f);if(a=n.read_shift(4),a!==1&&a!==2)throw new Error("Unrecognized #Sets: "+a);if(s=n.read_shift(16),c=n.read_shift(4),a===1&&c!==n.l)throw new Error("Length mismatch: "+c+" !== "+n.l);a===2&&(i=n.read_shift(16),o=n.read_shift(4));var x=S0(n,t),d={SystemIdentifier:u};for(var p in x)d[p]=x[p];if(d.FMTID=s,a===1)return d;if(o-n.l==2&&(n.l+=2),n.l!==o)throw new Error("Length mismatch 2: "+n.l+" !== "+o);var g;try{g=S0(n,null)}catch{}for(p in g)d[p]=g[p];return d.FMTID=[s,i],d}function kt(e,t){return e.read_shift(t),null}function uf(e,t,r){for(var n=[],a=e.l+t;e.l<a;)n.push(r(e,a-e.l));if(a!==e.l)throw new Error("Slurp error");return n}function ur(e,t){return e.read_shift(t)===1}function mr(e){return e.read_shift(2,"u")}function nc(e,t){return uf(e,t,mr)}function hf(e){var t=e.read_shift(1),r=e.read_shift(1);return r===1?t:t===1}function Xa(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont";if(r&&r.biff>=8,!r||r.biff==8){var s=e.read_shift(1);s&&(a="dbcs-cont")}else r.biff==12&&(a="wstr");r.biff>=2&&r.biff<=5&&(a="cpstr");var i=n?e.read_shift(n,a):"";return i}function xf(e){var t=e.read_shift(2),r=e.read_shift(1),n=r&4,a=r&8,s=1+(r&1),i=0,c,o={};a&&(i=e.read_shift(2)),n&&(c=e.read_shift(4));var u=s==2?"dbcs-cont":"sbcs-cont",f=t===0?"":e.read_shift(t,u);return a&&(e.l+=4*i),n&&(e.l+=c),o.t=f,a||(o.raw="<t>"+o.t+"</t>",o.r=o.t),o}function Wt(e,t,r){var n;if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}var a=e.read_shift(1);return a===0?n=e.read_shift(t,"sbcs-cont"):n=e.read_shift(t,"dbcs-cont"),n}function Ka(e,t,r){var n=e.read_shift(r&&r.biff==2?1:2);return n===0?(e.l++,""):Wt(e,n,r)}function qt(e,t,r){if(r.biff>5)return Ka(e,t,r);var n=e.read_shift(1);return n===0?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function df(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);return e.l+=2,[t,r]}function mf(e){var t=e.read_shift(4),r=e.l,n=!1;t>24&&(e.l+=t-24,e.read_shift(16)==="795881f43b1d7f48af2c825dc4852763"&&(n=!0),e.l=r);var a=e.read_shift((n?t-24:t)>>1,"utf16le").replace($r,"");return n&&(e.l+=24),a}function gf(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var n=e.read_shift(0,"lpstr-ansi");if(e.l+=2,e.read_shift(2)!=57005)throw new Error("Bad FileMoniker");var a=e.read_shift(4);if(a===0)return r+n.replace(/\\/g,"/");var s=e.read_shift(4);if(e.read_shift(2)!=3)throw new Error("Bad FileMoniker");var i=e.read_shift(s>>1,"utf16le").replace($r,"");return r+i}function pf(e,t){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return mf(e);case"0303000000000000c000000000000046":return gf(e);default:throw new Error("Unsupported Moniker "+r)}}function nn(e){var t=e.read_shift(4),r=t>0?e.read_shift(t,"utf16le").replace($r,""):"";return r}function vf(e,t){var r=e.l+t,n=e.read_shift(4);if(n!==2)throw new Error("Unrecognized streamVersion: "+n);var a=e.read_shift(2);e.l+=2;var s,i,c,o,u="",f,x;a&16&&(s=nn(e,r-e.l)),a&128&&(i=nn(e,r-e.l)),(a&257)===257&&(c=nn(e,r-e.l)),(a&257)===1&&(o=pf(e,r-e.l)),a&8&&(u=nn(e,r-e.l)),a&32&&(f=e.read_shift(16)),a&64&&(x=Is(e)),e.l=r;var d=i||c||o||"";d&&u&&(d+="#"+u),d||(d="#"+u),a&2&&d.charAt(0)=="/"&&d.charAt(1)!="/"&&(d="file://"+d);var p={Target:d};return f&&(p.guid=f),x&&(p.time=x),s&&(p.Tooltip=s),p}function sc(e){var t=e.read_shift(1),r=e.read_shift(1),n=e.read_shift(1),a=e.read_shift(1);return[t,r,n,a]}function ic(e,t){var r=sc(e);return r[3]=0,r}function dt(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2);return{r:t,c:r,ixfe:n}}function yf(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}function wf(e,t,r){return t===0?"":qt(e,t,r)}function _f(e,t,r){var n=r.biff>8?4:2,a=e.read_shift(n),s=e.read_shift(n,"i"),i=e.read_shift(n,"i");return[a,s,i]}function cc(e){var t=e.read_shift(2),r=Ds(e);return[t,r]}function Ef(e,t,r){e.l+=4,t-=4;var n=e.l+t,a=Xa(e,t,r),s=e.read_shift(2);if(n-=e.l,s!==n)throw new Error("Malformed AddinUdf: padding = "+n+" != "+s);return e.l+=s,a}function An(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2);return{s:{c:n,r:t},e:{c:a,r}}}function lc(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(1),a=e.read_shift(1);return{s:{c:n,r:t},e:{c:a,r}}}var Tf=lc;function oc(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2);return e.l+=12,[r,t,n]}function bf(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t}function kf(e){var t={};return e.l+=4,e.cf=e.read_shift(2),t}function Nr(e){e.l+=2,e.l+=e.read_shift(2)}var Sf={0:Nr,4:Nr,5:Nr,6:Nr,7:kf,8:Nr,9:Nr,10:Nr,11:Nr,12:Nr,13:bf,14:Nr,15:Nr,16:Nr,17:Nr,18:Nr,19:Nr,20:Nr,21:oc};function Af(e,t){for(var r=e.l+t,n=[];e.l<r;){var a=e.read_shift(2);e.l-=2;try{n.push(Sf[a](e,r-e.l))}catch{return e.l=r,n}}return e.l!=r&&(e.l=r),n}function sn(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),t-=2,t>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function Ff(e,t){return t===0||e.read_shift(2),1200}function Nf(e,t,r){if(r.enc)return e.l+=t,"";var n=e.l,a=qt(e,0,r);return e.read_shift(t+n-e.l),a}function Cf(e,t,r){var n=r&&r.biff==8||t==2?e.read_shift(2):(e.l+=t,0);return{fDialog:n&16,fBelow:n&64,fRight:n&128}}function Of(e,t,r){var n=e.read_shift(4),a=e.read_shift(1)&3,s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule";break}var i=Xa(e,0,r);return i.length===0&&(i="Sheet1"),{pos:n,hs:a,dt:s,name:i}}function Df(e,t){for(var r=e.l+t,n=e.read_shift(4),a=e.read_shift(4),s=[],i=0;i!=a&&e.l<r;++i)s.push(xf(e));return s.Count=n,s.Unique=a,s}function Rf(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}function If(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var n=e.read_shift(1);return e.l+=3,n&7&&(t.level=n&7),n&32&&(t.hidden=!0),n&64&&(t.hpt=r/20),t}function jf(e){var t=yf(e);if(t.type!=2211)throw new Error("Invalid Future Record "+t.type);var r=e.read_shift(4);return r!==0}function Pf(e){return e.read_shift(2),e.read_shift(4)}function F0(e,t,r){var n=0;r&&r.biff==2||(n=e.read_shift(2));var a=e.read_shift(2);r&&r.biff==2&&(n=1-(a>>15),a&=32767);var s={Unsynced:n&1,DyZero:(n&2)>>1,ExAsc:(n&4)>>2,ExDsc:(n&8)>>3};return[s,a]}function Lf(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=e.read_shift(2),o=e.read_shift(2),u=e.read_shift(2);return{Pos:[t,r],Dim:[n,a],Flags:s,CurTab:i,FirstTab:c,Selected:o,TabRatio:u}}function Bf(e,t,r){if(r&&r.biff>=2&&r.biff<5)return{};var n=e.read_shift(2);return{RTL:n&64}}function Mf(){}function $f(e,t,r){var n={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10;break}return n.name=Xa(e,0,r),n}function Uf(e){var t=dt(e);return t.isst=e.read_shift(4),t}function Hf(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var n=e.l+t,a=dt(e);r.biff==2&&e.l++;var s=Ka(e,n-e.l,r);return a.val=s,a}function Gf(e,t,r){var n=e.read_shift(2),a=qt(e,0,r);return[n,a]}var Vf=qt;function N0(e,t,r){var n=e.l+t,a=r.biff==8||!r.biff?4:2,s=e.read_shift(a),i=e.read_shift(a),c=e.read_shift(2),o=e.read_shift(2);return e.l=n,{s:{r:s,c},e:{r:i,c:o}}}function Wf(e){var t=e.read_shift(2),r=e.read_shift(2),n=cc(e);return{r:t,c:r,ixfe:n[0],rknum:n[1]}}function Xf(e,t){for(var r=e.l+t-2,n=e.read_shift(2),a=e.read_shift(2),s=[];e.l<r;)s.push(cc(e));if(e.l!==r)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-a+1)throw new Error("MulRK length mismatch");return{r:n,c:a,C:i,rkrec:s}}function Kf(e,t){for(var r=e.l+t-2,n=e.read_shift(2),a=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-a+1)throw new Error("MulBlank length mismatch");return{r:n,c:a,C:i,ixfe:s}}function zf(e,t,r,n){var a={},s=e.read_shift(4),i=e.read_shift(4),c=e.read_shift(4),o=e.read_shift(2);return a.patternType=Vo[c>>26],n.cellStyles&&(a.alc=s&7,a.fWrap=s>>3&1,a.alcV=s>>4&7,a.fJustLast=s>>7&1,a.trot=s>>8&255,a.cIndent=s>>16&15,a.fShrinkToFit=s>>20&1,a.iReadOrder=s>>22&2,a.fAtrNum=s>>26&1,a.fAtrFnt=s>>27&1,a.fAtrAlc=s>>28&1,a.fAtrBdr=s>>29&1,a.fAtrPat=s>>30&1,a.fAtrProt=s>>31&1,a.dgLeft=i&15,a.dgRight=i>>4&15,a.dgTop=i>>8&15,a.dgBottom=i>>12&15,a.icvLeft=i>>16&127,a.icvRight=i>>23&127,a.grbitDiag=i>>30&3,a.icvTop=c&127,a.icvBottom=c>>7&127,a.icvDiag=c>>14&127,a.dgDiag=c>>21&15,a.icvFore=o&127,a.icvBack=o>>7&127,a.fsxButton=o>>14&1),a}function Yf(e,t,r){var n={};return n.ifnt=e.read_shift(2),n.numFmtId=e.read_shift(2),n.flags=e.read_shift(2),n.fStyle=n.flags>>2&1,t-=6,n.data=zf(e,t,n.fStyle,r),n}function Jf(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(t[0]!==0&&t[0]--,t[1]!==0&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}function C0(e,t,r){var n=dt(e);(r.biff==2||t==9)&&++e.l;var a=hf(e);return n.val=a,n.t=a===!0||a===!1?"b":"e",n}function qf(e,t,r){r.biffguess&&r.biff==2&&(r.biff=5);var n=dt(e),a=Or(e);return n.val=a,n}var O0=wf;function Zf(e,t,r){var n=e.l+t,a=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,s==1025||s==14849)return[s,a];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=Wt(e,s),c=[];n>e.l;)c.push(Ka(e));return[s,a,i,c]}function D0(e,t,r){var n=e.read_shift(2),a,s={fBuiltIn:n&1,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return r.sbcch===14849&&(a=Ef(e,t-2,r)),s.body=a||e.read_shift(t-2),typeof a=="string"&&(s.Name=a),s}var Qf=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function R0(e,t,r){var n=e.l+t,a=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(r&&r.biff==2?1:2),o=0;(!r||r.biff>=5)&&(r.biff!=5&&(e.l+=2),o=e.read_shift(2),r.biff==5&&(e.l+=2),e.l+=4);var u=Wt(e,i,r);a&32&&(u=Qf[u.charCodeAt(0)]);var f=n-e.l;r&&r.biff==2&&--f;var x=n==e.l||c===0||!(f>0)?[]:Id(e,f,r,c);return{chKey:s,Name:u,itab:o,rgce:x}}function fc(e,t,r){if(r.biff<8)return eu(e,t,r);for(var n=[],a=e.l+t,s=e.read_shift(r.biff>8?4:2);s--!==0;)n.push(_f(e,r.biff>8?12:6,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function eu(e,t,r){e[e.l+1]==3&&e[e.l]++;var n=Xa(e,t,r);return n.charCodeAt(0)==3?n.slice(1):n}function ru(e,t,r){if(r.biff<8){e.l+=t;return}var n=e.read_shift(2),a=e.read_shift(2),s=Wt(e,n,r),i=Wt(e,a,r);return[s,i]}function tu(e,t,r){var n=lc(e);e.l++;var a=e.read_shift(1);return t-=8,[jd(e,t,r),a,n]}function I0(e,t,r){var n=Tf(e);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[n,Dd(e,t,r)]}function au(e){var t=e.read_shift(4)!==0,r=e.read_shift(4)!==0,n=e.read_shift(4);return[t,r,n]}function nu(e,t,r){if(!(r.biff<8)){var n=e.read_shift(2),a=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=qt(e,0,r);return r.biff<8&&e.read_shift(1),[{r:n,c:a},c,i,s]}}function su(e,t,r){return nu(e,t,r)}function iu(e,t){for(var r=[],n=e.read_shift(2);n--;)r.push(An(e));return r}function cu(e,t,r){if(r&&r.biff<8)return ou(e,t,r);var n=oc(e),a=Af(e,t-22,n[1]);return{cmo:n,ft:a}}var lu={8:function(e,t){var r=e.l+t;e.l+=10;var n=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var a=e.read_shift(1);return e.l+=a,e.l=r,{fmt:n}}};function ou(e,t,r){e.l+=4;var n=e.read_shift(2),a=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var i=[];return i.push((lu[n]||Ir)(e,t,r)),{cmo:[a,n,s],ft:i}}function fu(e,t,r){var n=e.l,a="";try{e.l+=4;var s=(r.lastobj||{cmo:[0,0]}).cmo[1],i;[0,5,7,11,12,14].indexOf(s)==-1?e.l+=6:i=df(e,6,r);var c=e.read_shift(2);e.read_shift(2),mr(e,2);var o=e.read_shift(2);e.l+=o;for(var u=1;u<e.lens.length-1;++u){if(e.l-n!=e.lens[u])throw new Error("TxO: bad continue record");var f=e[e.l],x=Wt(e,e.lens[u+1]-e.lens[u]-1);if(a+=x,a.length>=(f?c:2*c))break}if(a.length!==c&&a.length!==c*2)throw new Error("cchText: "+c+" != "+a.length);return e.l=n+t,{t:a}}catch{return e.l=n+t,{t:a}}}function uu(e,t){var r=An(e);e.l+=16;var n=vf(e,t-24);return[r,n]}function hu(e,t){e.read_shift(2);var r=An(e),n=e.read_shift((t-10)/2,"dbcs-cont");return n=n.replace($r,""),[r,n]}function xu(e){var t=[0,0],r;return r=e.read_shift(2),t[0]=T0[r]||r,r=e.read_shift(2),t[1]=T0[r]||r,t}function du(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(ic(e));return r}function mu(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(ic(e));return r}function gu(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t}function uc(e,t,r){if(!r.cellStyles)return Ir(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),s=e.read_shift(n),i=e.read_shift(n),c=e.read_shift(n),o=e.read_shift(2);n==2&&(e.l+=2);var u={s:a,e:s,w:i,ixfe:c,flags:o};return(r.biff>=5||!r.biff)&&(u.level=o>>8&7),u}function pu(e,t){var r={};return t<32||(e.l+=16,r.header=Or(e),r.footer=Or(e),e.l+=2),r}function vu(e,t,r){var n={area:!1};if(r.biff!=5)return e.l+=t,n;var a=e.read_shift(1);return e.l+=3,a&16&&(n.area=!0),n}var yu=dt,wu=nc,_u=Ka;function Eu(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a={fmt:t,env:r,len:n,data:e.slice(e.l,e.l+n)};return e.l+=n,a}function Tu(e,t,r){r.biffguess&&r.biff==5&&(r.biff=2);var n=dt(e);++e.l;var a=qt(e,t-7,r);return n.t="str",n.val=a,n}function bu(e){var t=dt(e);++e.l;var r=Or(e);return t.t="n",t.val=r,t}function ku(e){var t=dt(e);++e.l;var r=e.read_shift(2);return t.t="n",t.val=r,t}function Su(e){var t=e.read_shift(1);return t===0?(e.l++,""):e.read_shift(t,"sbcs-cont")}function Au(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}function Fu(e,t,r){var n=e.l+t,a=dt(e),s=e.read_shift(2),i=Wt(e,s,r);return e.l=n,a.t="str",a.val=i,a}var Nu=[2,3,48,49,131,139,140,245],j0=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Ts({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(c,o){var u=[],f=Ct(1);switch(o.type){case"base64":f=it(Xr(c));break;case"binary":f=it(c);break;case"buffer":case"array":f=c;break}br(f,0);var x=f.read_shift(1),d=!!(x&136),p=!1,g=!1;switch(x){case 2:break;case 3:break;case 48:p=!0,d=!0;break;case 49:p=!0,d=!0;break;case 131:break;case 139:break;case 140:g=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+x.toString(16))}var m=0,h=521;x==2&&(m=f.read_shift(2)),f.l+=3,x!=2&&(m=f.read_shift(4)),m>1048576&&(m=1e6),x!=2&&(h=f.read_shift(2));var _=f.read_shift(2),k=o.codepage||1252;x!=2&&(f.l+=16,f.read_shift(1),f[f.l]!==0&&(k=e[f[f.l]]),f.l+=1,f.l+=2),g&&(f.l+=36);for(var v=[],R={},M=Math.min(f.length,x==2?521:h-10-(p?264:0)),S=g?32:11;f.l<M&&f[f.l]!=13;)switch(R={},R.name=ts.utils.decode(k,f.slice(f.l,f.l+S)).replace(/[\u0000\r\n].*$/g,""),f.l+=S,R.type=String.fromCharCode(f.read_shift(1)),x!=2&&!g&&(R.offset=f.read_shift(4)),R.len=f.read_shift(1),x==2&&(R.offset=f.read_shift(2)),R.dec=f.read_shift(1),R.name.length&&v.push(R),x!=2&&(f.l+=g?13:14),R.type){case"B":(!p||R.len!=8)&&o.WTF&&console.log("Skipping "+R.name+":"+R.type);break;case"G":case"P":o.WTF&&console.log("Skipping "+R.name+":"+R.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+R.type)}if(f[f.l]!==13&&(f.l=h-1),f.read_shift(1)!==13)throw new Error("DBF Terminator not found "+f.l+" "+f[f.l]);f.l=h;var w=0,j=0;for(u[0]=[],j=0;j!=v.length;++j)u[0][j]=v[j].name;for(;m-- >0;){if(f[f.l]===42){f.l+=_;continue}for(++f.l,u[++w]=[],j=0,j=0;j!=v.length;++j){var E=f.slice(f.l,f.l+v[j].len);f.l+=v[j].len,br(E,0);var L=ts.utils.decode(k,E);switch(v[j].type){case"C":L.trim().length&&(u[w][j]=L.replace(/\s+$/,""));break;case"D":L.length===8?u[w][j]=new Date(+L.slice(0,4),+L.slice(4,6)-1,+L.slice(6,8)):u[w][j]=L;break;case"F":u[w][j]=parseFloat(L.trim());break;case"+":case"I":u[w][j]=g?E.read_shift(-4,"i")^2147483648:E.read_shift(4,"i");break;case"L":switch(L.trim().toUpperCase()){case"Y":case"T":u[w][j]=!0;break;case"N":case"F":u[w][j]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+L+"|")}break;case"M":if(!d)throw new Error("DBF Unexpected MEMO for type "+x.toString(16));u[w][j]="##MEMO##"+(g?parseInt(L.trim(),10):E.read_shift(4));break;case"N":L=L.replace(/\u0000/g,"").trim(),L&&L!="."&&(u[w][j]=+L||0);break;case"@":u[w][j]=new Date(E.read_shift(-8,"f")-621356832e5);break;case"T":u[w][j]=new Date((E.read_shift(4)-2440588)*864e5+E.read_shift(4));break;case"Y":u[w][j]=E.read_shift(4,"i")/1e4+E.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":u[w][j]=-E.read_shift(-8,"f");break;case"B":if(p&&v[j].len==8){u[w][j]=E.read_shift(8,"f");break}case"G":case"P":E.l+=v[j].len;break;case"0":if(v[j].name==="_NullFlags")break;default:throw new Error("DBF Unsupported data type "+v[j].type)}}}if(x!=2&&f.l<f.length&&f[f.l++]!=26)throw new Error("DBF EOF Marker missing "+(f.l-1)+" of "+f.length+" "+f[f.l-1].toString(16));return o&&o.sheetRows&&(u=u.slice(0,o.sheetRows)),o.DBF=v,u}function n(c,o){var u=o||{};u.dateNF||(u.dateNF="yyyymmdd");var f=da(r(c,u),u);return f["!cols"]=u.DBF.map(function(x){return{wch:x.len,DBF:x}}),delete u.DBF,f}function a(c,o){try{return Dt(n(c,o),o)}catch(u){if(o&&o.WTF)throw u}return{SheetNames:[],Sheets:{}}}var s={B:8,C:250,L:1,D:8,"?":0,"":0};function i(c,o){var u=o||{};if(+u.codepage>=0&&ct(+u.codepage),u.type=="string")throw new Error("Cannot write DBF to JS string");var f=as(),x=hs(c,{header:1,raw:!0,cellDates:!0}),d=x[0],p=x.slice(1),g=c["!cols"]||[],m=0,h=0,_=0,k=1;for(m=0;m<d.length;++m){if(((g[m]||{}).DBF||{}).name){d[m]=g[m].DBF.name,++_;continue}if(d[m]!=null){if(++_,typeof d[m]=="number"&&(d[m]=d[m].toString(10)),typeof d[m]!="string")throw new Error("DBF Invalid column name "+d[m]+" |"+typeof d[m]+"|");if(d.indexOf(d[m])!==m){for(h=0;h<1024;++h)if(d.indexOf(d[m]+"_"+h)==-1){d[m]+="_"+h;break}}}}var v=or(c["!ref"]),R=[],M=[],S=[];for(m=0;m<=v.e.c-v.s.c;++m){var w="",j="",E=0,L=[];for(h=0;h<p.length;++h)p[h][m]!=null&&L.push(p[h][m]);if(L.length==0||d[m]==null){R[m]="?";continue}for(h=0;h<L.length;++h){switch(typeof L[h]){case"number":j="B";break;case"string":j="C";break;case"boolean":j="L";break;case"object":j=L[h]instanceof Date?"D":"C";break;default:j="C"}E=Math.max(E,String(L[h]).length),w=w&&w!=j?"C":j}E>250&&(E=250),j=((g[m]||{}).DBF||{}).type,j=="C"&&g[m].DBF.len>E&&(E=g[m].DBF.len),w=="B"&&j=="N"&&(w="N",S[m]=g[m].DBF.dec,E=g[m].DBF.len),M[m]=w=="C"||j=="N"?E:s[w]||0,k+=M[m],R[m]=w}var G=f.next(32);for(G.write_shift(4,318902576),G.write_shift(4,p.length),G.write_shift(2,296+32*_),G.write_shift(2,k),m=0;m<4;++m)G.write_shift(4,0);for(G.write_shift(4,0|(+t[fi]||3)<<8),m=0,h=0;m<d.length;++m)if(d[m]!=null){var C=f.next(32),I=(d[m].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);C.write_shift(1,I,"sbcs"),C.write_shift(1,R[m]=="?"?"C":R[m],"sbcs"),C.write_shift(4,h),C.write_shift(1,M[m]||s[R[m]]||0),C.write_shift(1,S[m]||0),C.write_shift(1,2),C.write_shift(4,0),C.write_shift(1,0),C.write_shift(4,0),C.write_shift(4,0),h+=M[m]||s[R[m]]||0}var q=f.next(264);for(q.write_shift(4,13),m=0;m<65;++m)q.write_shift(4,0);for(m=0;m<p.length;++m){var ae=f.next(k);for(ae.write_shift(1,0),h=0;h<d.length;++h)if(d[h]!=null)switch(R[h]){case"L":ae.write_shift(1,p[m][h]==null?63:p[m][h]?84:70);break;case"B":ae.write_shift(8,p[m][h]||0,"f");break;case"N":var ce="0";for(typeof p[m][h]=="number"&&(ce=p[m][h].toFixed(S[h]||0)),_=0;_<M[h]-ce.length;++_)ae.write_shift(1,32);ae.write_shift(1,ce,"sbcs");break;case"D":p[m][h]?(ae.write_shift(4,("0000"+p[m][h].getFullYear()).slice(-4),"sbcs"),ae.write_shift(2,("00"+(p[m][h].getMonth()+1)).slice(-2),"sbcs"),ae.write_shift(2,("00"+p[m][h].getDate()).slice(-2),"sbcs")):ae.write_shift(8,"00000000","sbcs");break;case"C":var de=String(p[m][h]!=null?p[m][h]:"").slice(0,M[h]);for(ae.write_shift(1,de,"sbcs"),_=0;_<M[h]-de.length;++_)ae.write_shift(1,32);break}}return f.next(1).write_shift(1,26),f.end()}return{to_workbook:a,to_sheet:n,from_sheet:i}}(),Cu=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1BN("+ht(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(d,p){var g=e[p];return typeof g=="number"?qs(g):g},n=function(d,p,g){var m=p.charCodeAt(0)-32<<4|g.charCodeAt(0)-48;return m==59?d:qs(m)};e["|"]=254;function a(d,p){switch(p.type){case"base64":return s(Xr(d),p);case"binary":return s(d,p);case"buffer":return s(We&&Buffer.isBuffer(d)?d.toString("binary"):Kt(d),p);case"array":return s(Vt(d),p)}throw new Error("Unrecognized type "+p.type)}function s(d,p){var g=d.split(/[\n\r]+/),m=-1,h=-1,_=0,k=0,v=[],R=[],M=null,S={},w=[],j=[],E=[],L=0,G;for(+p.codepage>=0&&ct(+p.codepage);_!==g.length;++_){L=0;var C=g[_].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),I=C.replace(/;;/g,"\0").split(";").map(function(P){return P.replace(/\u0000/g,";")}),q=I[0],ae;if(C.length>0)switch(q){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":I[1].charAt(0)=="P"&&R.push(C.slice(3).replace(/;;/g,";"));break;case"C":var ce=!1,de=!1,ie=!1,B=!1,re=-1,he=-1;for(k=1;k<I.length;++k)switch(I[k].charAt(0)){case"A":break;case"X":h=parseInt(I[k].slice(1))-1,de=!0;break;case"Y":for(m=parseInt(I[k].slice(1))-1,de||(h=0),G=v.length;G<=m;++G)v[G]=[];break;case"K":ae=I[k].slice(1),ae.charAt(0)==='"'?ae=ae.slice(1,ae.length-1):ae==="TRUE"?ae=!0:ae==="FALSE"?ae=!1:isNaN(ot(ae))?isNaN(fa(ae).getDate())||(ae=Tr(ae)):(ae=ot(ae),M!==null&&ha(M)&&(ae=Sn(ae))),ce=!0;break;case"E":B=!0;var F=la(I[k].slice(1),{r:m,c:h});v[m][h]=[v[m][h],F];break;case"S":ie=!0,v[m][h]=[v[m][h],"S5S"];break;case"G":break;case"R":re=parseInt(I[k].slice(1))-1;break;case"C":he=parseInt(I[k].slice(1))-1;break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+C)}if(ce&&(v[m][h]&&v[m][h].length==2?v[m][h][0]=ae:v[m][h]=ae,M=null),ie){if(B)throw new Error("SYLK shared formula cannot have own formula");var U=re>-1&&v[re][he];if(!U||!U[1])throw new Error("SYLK shared formula cannot find base");v[m][h][1]=_c(U[1],{r:m-re,c:h-he})}break;case"F":var H=0;for(k=1;k<I.length;++k)switch(I[k].charAt(0)){case"X":h=parseInt(I[k].slice(1))-1,++H;break;case"Y":for(m=parseInt(I[k].slice(1))-1,G=v.length;G<=m;++G)v[G]=[];break;case"M":L=parseInt(I[k].slice(1))/20;break;case"F":break;case"G":break;case"P":M=R[parseInt(I[k].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(E=I[k].slice(1).split(" "),G=parseInt(E[0],10);G<=parseInt(E[1],10);++G)L=parseInt(E[2],10),j[G-1]=L===0?{hidden:!0}:{wch:L},ua(j[G-1]);break;case"C":h=parseInt(I[k].slice(1))-1,j[h]||(j[h]={});break;case"R":m=parseInt(I[k].slice(1))-1,w[m]||(w[m]={}),L>0?(w[m].hpt=L,w[m].hpx=Ha(L)):L===0&&(w[m].hidden=!0);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+C)}H<1&&(M=null);break;default:if(p&&p.WTF)throw new Error("SYLK bad record "+C)}}return w.length>0&&(S["!rows"]=w),j.length>0&&(S["!cols"]=j),p&&p.sheetRows&&(v=v.slice(0,p.sheetRows)),[v,S]}function i(d,p){var g=a(d,p),m=g[0],h=g[1],_=da(m,p);return ht(h).forEach(function(k){_[k]=h[k]}),_}function c(d,p){return Dt(i(d,p),p)}function o(d,p,g,m){var h="C;Y"+(g+1)+";X"+(m+1)+";K";switch(d.t){case"n":h+=d.v||0,d.f&&!d.F&&(h+=";E"+gx(d.f,{r:g,c:m}));break;case"b":h+=d.v?"TRUE":"FALSE";break;case"e":h+=d.w||d.v;break;case"d":h+='"'+(d.w||d.v)+'"';break;case"s":h+='"'+d.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return h}function u(d,p){p.forEach(function(g,m){var h="F;W"+(m+1)+" "+(m+1)+" ";g.hidden?h+="0":(typeof g.width=="number"&&!g.wpx&&(g.wpx=_n(g.width)),typeof g.wpx=="number"&&!g.wch&&(g.wch=En(g.wpx)),typeof g.wch=="number"&&(h+=Math.round(g.wch))),h.charAt(h.length-1)!=" "&&d.push(h)})}function f(d,p){p.forEach(function(g,m){var h="F;";g.hidden?h+="M0;":g.hpt?h+="M"+20*g.hpt+";":g.hpx&&(h+="M"+20*vc(g.hpx)+";"),h.length>2&&d.push(h+"R"+(m+1))})}function x(d,p){var g=["ID;PWXL;N;E"],m=[],h=or(d["!ref"]),_,k=Array.isArray(d),v=`\r
`;g.push("P;PGeneral"),g.push("F;P0;DG0G8;M255"),d["!cols"]&&u(g,d["!cols"]),d["!rows"]&&f(g,d["!rows"]),g.push("B;Y"+(h.e.r-h.s.r+1)+";X"+(h.e.c-h.s.c+1)+";D"+[h.s.c,h.s.r,h.e.c,h.e.r].join(" "));for(var R=h.s.r;R<=h.e.r;++R)for(var M=h.s.c;M<=h.e.c;++M){var S=Le({r:R,c:M});_=k?(d[R]||[])[M]:d[S],!(!_||_.v==null&&(!_.f||_.F))&&m.push(o(_,d,R,M))}return g.join(v)+v+m.join(v)+v+"E"+v}return{to_workbook:c,to_sheet:i,from_sheet:x}}(),Ou=function(){function e(s,i){switch(i.type){case"base64":return t(Xr(s),i);case"binary":return t(s,i);case"buffer":return t(We&&Buffer.isBuffer(s)?s.toString("binary"):Kt(s),i);case"array":return t(Vt(s),i)}throw new Error("Unrecognized type "+i.type)}function t(s,i){for(var c=s.split(`
`),o=-1,u=-1,f=0,x=[];f!==c.length;++f){if(c[f].trim()==="BOT"){x[++o]=[],u=0;continue}if(!(o<0)){var d=c[f].trim().split(","),p=d[0],g=d[1];++f;for(var m=c[f]||"";(m.match(/["]/g)||[]).length&1&&f<c.length-1;)m+=`
`+c[++f];switch(m=m.trim(),+p){case-1:if(m==="BOT"){x[++o]=[],u=0;continue}else if(m!=="EOD")throw new Error("Unrecognized DIF special command "+m);break;case 0:m==="TRUE"?x[o][u]=!0:m==="FALSE"?x[o][u]=!1:isNaN(ot(g))?isNaN(fa(g).getDate())?x[o][u]=g:x[o][u]=Tr(g):x[o][u]=ot(g),++u;break;case 1:m=m.slice(1,m.length-1),m=m.replace(/""/g,'"'),m&&m.match(/^=".*"$/)&&(m=m.slice(2,-1)),x[o][u++]=m!==""?m:null;break}if(m==="EOD")break}}return i&&i.sheetRows&&(x=x.slice(0,i.sheetRows)),x}function r(s,i){return da(e(s,i),i)}function n(s,i){return Dt(r(s,i),i)}var a=function(){var s=function(o,u,f,x,d){o.push(u),o.push(f+","+x),o.push('"'+d.replace(/"/g,'""')+'"')},i=function(o,u,f,x){o.push(u+","+f),o.push(u==1?'"'+x.replace(/"/g,'""')+'"':x)};return function(o){var u=[],f=or(o["!ref"]),x,d=Array.isArray(o);s(u,"TABLE",0,1,"sheetjs"),s(u,"VECTORS",0,f.e.r-f.s.r+1,""),s(u,"TUPLES",0,f.e.c-f.s.c+1,""),s(u,"DATA",0,0,"");for(var p=f.s.r;p<=f.e.r;++p){i(u,-1,0,"BOT");for(var g=f.s.c;g<=f.e.c;++g){var m=Le({r:p,c:g});if(x=d?(o[p]||[])[g]:o[m],!x){i(u,1,0,"");continue}switch(x.t){case"n":var h=x.w;!h&&x.v!=null&&(h=x.v),h==null?x.f&&!x.F?i(u,1,0,"="+x.f):i(u,1,0,""):i(u,0,h,"V");break;case"b":i(u,0,x.v?1:0,x.v?"TRUE":"FALSE");break;case"s":i(u,1,0,isNaN(x.v)?x.v:'="'+x.v+'"');break;case"d":x.w||(x.w=tt(x.z||Me[14],Ur(Tr(x.v)))),i(u,0,x.w,"V");break;default:i(u,1,0,"")}}}i(u,-1,0,"EOD");var _=`\r
`,k=u.join(_);return k}}();return{to_workbook:n,to_sheet:r,from_sheet:a}}(),Du=function(){function e(x){return x.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,`
`)}function t(x){return x.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(x,d){for(var p=x.split(`
`),g=-1,m=-1,h=0,_=[];h!==p.length;++h){var k=p[h].trim().split(":");if(k[0]==="cell"){var v=Mr(k[1]);if(_.length<=v.r)for(g=_.length;g<=v.r;++g)_[g]||(_[g]=[]);switch(g=v.r,m=v.c,k[2]){case"t":_[g][m]=e(k[3]);break;case"v":_[g][m]=+k[3];break;case"vtf":var R=k[k.length-1];case"vtc":switch(k[3]){case"nl":_[g][m]=!!+k[4];break;default:_[g][m]=+k[4];break}k[2]=="vtf"&&(_[g][m]=[_[g][m],R])}}}return d&&d.sheetRows&&(_=_.slice(0,d.sheetRows)),_}function n(x,d){return da(r(x,d),d)}function a(x,d){return Dt(n(x,d),d)}var s=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join(`
`),i=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join(`
`)+`
`,c=["# SocialCalc Spreadsheet Control Save","part:sheet"].join(`
`),o="--SocialCalcSpreadsheetControlSave--";function u(x){if(!x||!x["!ref"])return"";for(var d=[],p=[],g,m="",h=xa(x["!ref"]),_=Array.isArray(x),k=h.s.r;k<=h.e.r;++k)for(var v=h.s.c;v<=h.e.c;++v)if(m=Le({r:k,c:v}),g=_?(x[k]||[])[v]:x[m],!(!g||g.v==null||g.t==="z")){switch(p=["cell",m,"t"],g.t){case"s":case"str":p.push(t(g.v));break;case"n":g.f?(p[2]="vtf",p[3]="n",p[4]=g.v,p[5]=t(g.f)):(p[2]="v",p[3]=g.v);break;case"b":p[2]="vt"+(g.f?"f":"c"),p[3]="nl",p[4]=g.v?"1":"0",p[5]=t(g.f||(g.v?"TRUE":"FALSE"));break;case"d":var R=Ur(Tr(g.v));p[2]="vtc",p[3]="nd",p[4]=""+R,p[5]=g.w||tt(g.z||Me[14],R);break;case"e":continue}d.push(p.join(":"))}return d.push("sheet:c:"+(h.e.c-h.s.c+1)+":r:"+(h.e.r-h.s.r+1)+":tvf:1"),d.push("valueformat:1:text-wiki"),d.join(`
`)}function f(x){return[s,i,c,i,u(x),o].join(`
`)}return{to_workbook:a,to_sheet:n,from_sheet:f}}(),$a=function(){function e(f,x,d,p,g){g.raw?x[d][p]=f:f===""||(f==="TRUE"?x[d][p]=!0:f==="FALSE"?x[d][p]=!1:isNaN(ot(f))?isNaN(fa(f).getDate())?x[d][p]=f:x[d][p]=Tr(f):x[d][p]=ot(f))}function t(f,x){var d=x||{},p=[];if(!f||f.length===0)return p;for(var g=f.split(/[\r\n]/),m=g.length-1;m>=0&&g[m].length===0;)--m;for(var h=10,_=0,k=0;k<=m;++k)_=g[k].indexOf(" "),_==-1?_=g[k].length:_++,h=Math.max(h,_);for(k=0;k<=m;++k){p[k]=[];var v=0;for(e(g[k].slice(0,h).trim(),p,k,v,d),v=1;v<=(g[k].length-h)/10+1;++v)e(g[k].slice(h+(v-1)*10,h+v*10).trim(),p,k,v,d)}return d.sheetRows&&(p=p.slice(0,d.sheetRows)),p}var r={44:",",9:"	",59:";",124:"|"},n={44:3,9:2,59:1,124:0};function a(f){for(var x={},d=!1,p=0,g=0;p<f.length;++p)(g=f.charCodeAt(p))==34?d=!d:!d&&g in r&&(x[g]=(x[g]||0)+1);g=[];for(p in x)Object.prototype.hasOwnProperty.call(x,p)&&g.push([x[p],p]);if(!g.length){x=n;for(p in x)Object.prototype.hasOwnProperty.call(x,p)&&g.push([x[p],p])}return g.sort(function(m,h){return m[0]-h[0]||n[m[1]]-n[h[1]]}),r[g.pop()[1]]||44}function s(f,x){var d=x||{},p="",g=d.dense?[]:{},m={s:{c:0,r:0},e:{c:0,r:0}};f.slice(0,4)=="sep="?f.charCodeAt(5)==13&&f.charCodeAt(6)==10?(p=f.charAt(4),f=f.slice(7)):f.charCodeAt(5)==13||f.charCodeAt(5)==10?(p=f.charAt(4),f=f.slice(6)):p=a(f.slice(0,1024)):d&&d.FS?p=d.FS:p=a(f.slice(0,1024));var h=0,_=0,k=0,v=0,R=0,M=p.charCodeAt(0),S=!1,w=0,j=f.charCodeAt(0);f=f.replace(/\r\n/mg,`
`);var E=d.dateNF!=null?Ul(d.dateNF):null;function L(){var G=f.slice(v,R),C={};if(G.charAt(0)=='"'&&G.charAt(G.length-1)=='"'&&(G=G.slice(1,-1).replace(/""/g,'"')),G.length===0)C.t="z";else if(d.raw)C.t="s",C.v=G;else if(G.trim().length===0)C.t="s",C.v=G;else if(G.charCodeAt(0)==61)G.charCodeAt(1)==34&&G.charCodeAt(G.length-1)==34?(C.t="s",C.v=G.slice(2,-1).replace(/""/g,'"')):vx(G)?(C.t="n",C.f=G.slice(1)):(C.t="s",C.v=G);else if(G=="TRUE")C.t="b",C.v=!0;else if(G=="FALSE")C.t="b",C.v=!1;else if(!isNaN(k=ot(G)))C.t="n",d.cellText!==!1&&(C.w=G),C.v=k;else if(!isNaN(fa(G).getDate())||E&&G.match(E)){C.z=d.dateNF||Me[14];var I=0;E&&G.match(E)&&(G=Hl(G,d.dateNF,G.match(E)||[]),I=1),d.cellDates?(C.t="d",C.v=Tr(G,I)):(C.t="n",C.v=Ur(Tr(G,I))),d.cellText!==!1&&(C.w=tt(C.z,C.v instanceof Date?Ur(C.v):C.v)),d.cellNF||delete C.z}else C.t="s",C.v=G;if(C.t=="z"||(d.dense?(g[h]||(g[h]=[]),g[h][_]=C):g[Le({c:_,r:h})]=C),v=R+1,j=f.charCodeAt(v),m.e.c<_&&(m.e.c=_),m.e.r<h&&(m.e.r=h),w==M)++_;else if(_=0,++h,d.sheetRows&&d.sheetRows<=h)return!0}e:for(;R<f.length;++R)switch(w=f.charCodeAt(R)){case 34:j===34&&(S=!S);break;case M:case 10:case 13:if(!S&&L())break e;break}return R-v>0&&L(),g["!ref"]=Ke(m),g}function i(f,x){return!(x&&x.PRN)||x.FS||f.slice(0,4)=="sep="||f.indexOf("	")>=0||f.indexOf(",")>=0||f.indexOf(";")>=0?s(f,x):da(t(f,x),x)}function c(f,x){var d="",p=x.type=="string"?[0,0,0,0]:Hs(f,x);switch(x.type){case"base64":d=Xr(f);break;case"binary":d=f;break;case"buffer":x.codepage==65001?d=f.toString("utf8"):(x.codepage,d=We&&Buffer.isBuffer(f)?f.toString("binary"):Kt(f));break;case"array":d=Vt(f);break;case"string":d=f;break;default:throw new Error("Unrecognized type "+x.type)}return p[0]==239&&p[1]==187&&p[2]==191?d=ar(d.slice(3)):x.type!="string"&&x.type!="buffer"&&x.codepage==65001?d=ar(d):x.type=="binary",d.slice(0,19)=="socialcalc:version:"?Du.to_sheet(x.type=="string"?d:ar(d),x):i(d,x)}function o(f,x){return Dt(c(f,x),x)}function u(f){for(var x=[],d=or(f["!ref"]),p,g=Array.isArray(f),m=d.s.r;m<=d.e.r;++m){for(var h=[],_=d.s.c;_<=d.e.c;++_){var k=Le({r:m,c:_});if(p=g?(f[m]||[])[_]:f[k],!p||p.v==null){h.push("          ");continue}for(var v=(p.w||(_t(p),p.w)||"").slice(0,10);v.length<10;)v+=" ";h.push(v+(_===0?" ":""))}x.push(h.join(""))}return x.join(`
`)}return{to_workbook:o,to_sheet:c,from_sheet:u}}();function Ru(e,t){var r=t||{},n=!!r.WTF;r.WTF=!0;try{var a=Cu.to_workbook(e,r);return r.WTF=n,a}catch(s){if(r.WTF=n,!s.message.match(/SYLK bad record ID/)&&n)throw s;return $a.to_workbook(e,t)}}var Ra=function(){function e(F,U,H){if(F){br(F,F.l||0);for(var P=H.Enum||re;F.l<F.length;){var Q=F.read_shift(2),me=P[Q]||P[65535],ye=F.read_shift(2),oe=F.l+ye,le=me.f&&me.f(F,ye,H);if(F.l=oe,U(le,me,Q))return}}}function t(F,U){switch(U.type){case"base64":return r(it(Xr(F)),U);case"binary":return r(it(F),U);case"buffer":case"array":return r(F,U)}throw"Unsupported type "+U.type}function r(F,U){if(!F)return F;var H=U||{},P=H.dense?[]:{},Q="Sheet1",me="",ye=0,oe={},le=[],Ee=[],D={s:{r:0,c:0},e:{r:0,c:0}},ke=H.sheetRows||0;if(F[2]==0&&(F[3]==8||F[3]==9)&&F.length>=16&&F[14]==5&&F[15]===108)throw new Error("Unsupported Works 3 for Mac file");if(F[2]==2)H.Enum=re,e(F,function(xe,Ve,er){switch(er){case 0:H.vers=xe,xe>=4096&&(H.qpro=!0);break;case 6:D=xe;break;case 204:xe&&(me=xe);break;case 222:me=xe;break;case 15:case 51:H.qpro||(xe[1].v=xe[1].v.slice(1));case 13:case 14:case 16:er==14&&(xe[2]&112)==112&&(xe[2]&15)>1&&(xe[2]&15)<15&&(xe[1].z=H.dateNF||Me[14],H.cellDates&&(xe[1].t="d",xe[1].v=Sn(xe[1].v))),H.qpro&&xe[3]>ye&&(P["!ref"]=Ke(D),oe[Q]=P,le.push(Q),P=H.dense?[]:{},D={s:{r:0,c:0},e:{r:0,c:0}},ye=xe[3],Q=me||"Sheet"+(ye+1),me="");var qe=H.dense?(P[xe[0].r]||[])[xe[0].c]:P[Le(xe[0])];if(qe){qe.t=xe[1].t,qe.v=xe[1].v,xe[1].z!=null&&(qe.z=xe[1].z),xe[1].f!=null&&(qe.f=xe[1].f);break}H.dense?(P[xe[0].r]||(P[xe[0].r]=[]),P[xe[0].r][xe[0].c]=xe[1]):P[Le(xe[0])]=xe[1];break}},H);else if(F[2]==26||F[2]==14)H.Enum=he,F[2]==14&&(H.qpro=!0,F.l=0),e(F,function(xe,Ve,er){switch(er){case 204:Q=xe;break;case 22:xe[1].v=xe[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(xe[3]>ye&&(P["!ref"]=Ke(D),oe[Q]=P,le.push(Q),P=H.dense?[]:{},D={s:{r:0,c:0},e:{r:0,c:0}},ye=xe[3],Q="Sheet"+(ye+1)),ke>0&&xe[0].r>=ke)break;H.dense?(P[xe[0].r]||(P[xe[0].r]=[]),P[xe[0].r][xe[0].c]=xe[1]):P[Le(xe[0])]=xe[1],D.e.c<xe[0].c&&(D.e.c=xe[0].c),D.e.r<xe[0].r&&(D.e.r=xe[0].r);break;case 27:xe[14e3]&&(Ee[xe[14e3][0]]=xe[14e3][1]);break;case 1537:Ee[xe[0]]=xe[1],xe[0]==ye&&(Q=xe[1]);break}},H);else throw new Error("Unrecognized LOTUS BOF "+F[2]);if(P["!ref"]=Ke(D),oe[me||Q]=P,le.push(me||Q),!Ee.length)return{SheetNames:le,Sheets:oe};for(var Oe={},Ie=[],je=0;je<Ee.length;++je)oe[le[je]]?(Ie.push(Ee[je]||le[je]),Oe[Ee[je]]=oe[Ee[je]]||oe[le[je]]):(Ie.push(Ee[je]),Oe[Ee[je]]={"!ref":"A1"});return{SheetNames:Ie,Sheets:Oe}}function n(F,U){var H=U||{};if(+H.codepage>=0&&ct(+H.codepage),H.type=="string")throw new Error("Cannot write WK1 to JS string");var P=as(),Q=or(F["!ref"]),me=Array.isArray(F),ye=[];st(P,0,s(1030)),st(P,6,o(Q));for(var oe=Math.min(Q.e.r,8191),le=Q.s.r;le<=oe;++le)for(var Ee=Sr(le),D=Q.s.c;D<=Q.e.c;++D){le===Q.s.r&&(ye[D]=yr(D));var ke=ye[D]+Ee,Oe=me?(F[le]||[])[D]:F[ke];if(!(!Oe||Oe.t=="z"))if(Oe.t=="n")(Oe.v|0)==Oe.v&&Oe.v>=-32768&&Oe.v<=32767?st(P,13,p(le,D,Oe.v)):st(P,14,m(le,D,Oe.v));else{var Ie=_t(Oe);st(P,15,x(le,D,Ie.slice(0,239)))}}return st(P,1),P.end()}function a(F,U){var H=U||{};if(+H.codepage>=0&&ct(+H.codepage),H.type=="string")throw new Error("Cannot write WK3 to JS string");var P=as();st(P,0,i(F));for(var Q=0,me=0;Q<F.SheetNames.length;++Q)(F.Sheets[F.SheetNames[Q]]||{})["!ref"]&&st(P,27,B(F.SheetNames[Q],me++));var ye=0;for(Q=0;Q<F.SheetNames.length;++Q){var oe=F.Sheets[F.SheetNames[Q]];if(!(!oe||!oe["!ref"])){for(var le=or(oe["!ref"]),Ee=Array.isArray(oe),D=[],ke=Math.min(le.e.r,8191),Oe=le.s.r;Oe<=ke;++Oe)for(var Ie=Sr(Oe),je=le.s.c;je<=le.e.c;++je){Oe===le.s.r&&(D[je]=yr(je));var xe=D[je]+Ie,Ve=Ee?(oe[Oe]||[])[je]:oe[xe];if(!(!Ve||Ve.t=="z"))if(Ve.t=="n")st(P,23,L(Oe,je,ye,Ve.v));else{var er=_t(Ve);st(P,22,w(Oe,je,ye,er.slice(0,239)))}}++ye}}return st(P,1),P.end()}function s(F){var U=vr(2);return U.write_shift(2,F),U}function i(F){var U=vr(26);U.write_shift(2,4096),U.write_shift(2,4),U.write_shift(4,0);for(var H=0,P=0,Q=0,me=0;me<F.SheetNames.length;++me){var ye=F.SheetNames[me],oe=F.Sheets[ye];if(!(!oe||!oe["!ref"])){++Q;var le=xa(oe["!ref"]);H<le.e.r&&(H=le.e.r),P<le.e.c&&(P=le.e.c)}}return H>8191&&(H=8191),U.write_shift(2,H),U.write_shift(1,Q),U.write_shift(1,P),U.write_shift(2,0),U.write_shift(2,0),U.write_shift(1,1),U.write_shift(1,2),U.write_shift(4,0),U.write_shift(4,0),U}function c(F,U,H){var P={s:{c:0,r:0},e:{c:0,r:0}};return U==8&&H.qpro?(P.s.c=F.read_shift(1),F.l++,P.s.r=F.read_shift(2),P.e.c=F.read_shift(1),F.l++,P.e.r=F.read_shift(2),P):(P.s.c=F.read_shift(2),P.s.r=F.read_shift(2),U==12&&H.qpro&&(F.l+=2),P.e.c=F.read_shift(2),P.e.r=F.read_shift(2),U==12&&H.qpro&&(F.l+=2),P.s.c==65535&&(P.s.c=P.e.c=P.s.r=P.e.r=0),P)}function o(F){var U=vr(8);return U.write_shift(2,F.s.c),U.write_shift(2,F.s.r),U.write_shift(2,F.e.c),U.write_shift(2,F.e.r),U}function u(F,U,H){var P=[{c:0,r:0},{t:"n",v:0},0,0];return H.qpro&&H.vers!=20768?(P[0].c=F.read_shift(1),P[3]=F.read_shift(1),P[0].r=F.read_shift(2),F.l+=2):(P[2]=F.read_shift(1),P[0].c=F.read_shift(2),P[0].r=F.read_shift(2)),P}function f(F,U,H){var P=F.l+U,Q=u(F,U,H);if(Q[1].t="s",H.vers==20768){F.l++;var me=F.read_shift(1);return Q[1].v=F.read_shift(me,"utf8"),Q}return H.qpro&&F.l++,Q[1].v=F.read_shift(P-F.l,"cstr"),Q}function x(F,U,H){var P=vr(7+H.length);P.write_shift(1,255),P.write_shift(2,U),P.write_shift(2,F),P.write_shift(1,39);for(var Q=0;Q<P.length;++Q){var me=H.charCodeAt(Q);P.write_shift(1,me>=128?95:me)}return P.write_shift(1,0),P}function d(F,U,H){var P=u(F,U,H);return P[1].v=F.read_shift(2,"i"),P}function p(F,U,H){var P=vr(7);return P.write_shift(1,255),P.write_shift(2,U),P.write_shift(2,F),P.write_shift(2,H,"i"),P}function g(F,U,H){var P=u(F,U,H);return P[1].v=F.read_shift(8,"f"),P}function m(F,U,H){var P=vr(13);return P.write_shift(1,255),P.write_shift(2,U),P.write_shift(2,F),P.write_shift(8,H,"f"),P}function h(F,U,H){var P=F.l+U,Q=u(F,U,H);if(Q[1].v=F.read_shift(8,"f"),H.qpro)F.l=P;else{var me=F.read_shift(2);R(F.slice(F.l,F.l+me),Q),F.l+=me}return Q}function _(F,U,H){var P=U&32768;return U&=-32769,U=(P?F:0)+(U>=8192?U-16384:U),(P?"":"$")+(H?yr(U):Sr(U))}var k={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},v=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function R(F,U){br(F,0);for(var H=[],P=0,Q="",me="",ye="",oe="";F.l<F.length;){var le=F[F.l++];switch(le){case 0:H.push(F.read_shift(8,"f"));break;case 1:me=_(U[0].c,F.read_shift(2),!0),Q=_(U[0].r,F.read_shift(2),!1),H.push(me+Q);break;case 2:{var Ee=_(U[0].c,F.read_shift(2),!0),D=_(U[0].r,F.read_shift(2),!1);me=_(U[0].c,F.read_shift(2),!0),Q=_(U[0].r,F.read_shift(2),!1),H.push(Ee+D+":"+me+Q)}break;case 3:if(F.l<F.length){console.error("WK1 premature formula end");return}break;case 4:H.push("("+H.pop()+")");break;case 5:H.push(F.read_shift(2));break;case 6:{for(var ke="";le=F[F.l++];)ke+=String.fromCharCode(le);H.push('"'+ke.replace(/"/g,'""')+'"')}break;case 8:H.push("-"+H.pop());break;case 23:H.push("+"+H.pop());break;case 22:H.push("NOT("+H.pop()+")");break;case 20:case 21:oe=H.pop(),ye=H.pop(),H.push(["AND","OR"][le-20]+"("+ye+","+oe+")");break;default:if(le<32&&v[le])oe=H.pop(),ye=H.pop(),H.push(ye+v[le]+oe);else if(k[le]){if(P=k[le][1],P==69&&(P=F[F.l++]),P>H.length){console.error("WK1 bad formula parse 0x"+le.toString(16)+":|"+H.join("|")+"|");return}var Oe=H.slice(-P);H.length-=P,H.push(k[le][0]+"("+Oe.join(",")+")")}else return le<=7?console.error("WK1 invalid opcode "+le.toString(16)):le<=24?console.error("WK1 unsupported op "+le.toString(16)):le<=30?console.error("WK1 invalid opcode "+le.toString(16)):le<=115?console.error("WK1 unsupported function opcode "+le.toString(16)):console.error("WK1 unrecognized opcode "+le.toString(16))}}H.length==1?U[1].f=""+H[0]:console.error("WK1 bad formula parse |"+H.join("|")+"|")}function M(F){var U=[{c:0,r:0},{t:"n",v:0},0];return U[0].r=F.read_shift(2),U[3]=F[F.l++],U[0].c=F[F.l++],U}function S(F,U){var H=M(F);return H[1].t="s",H[1].v=F.read_shift(U-4,"cstr"),H}function w(F,U,H,P){var Q=vr(6+P.length);Q.write_shift(2,F),Q.write_shift(1,H),Q.write_shift(1,U),Q.write_shift(1,39);for(var me=0;me<P.length;++me){var ye=P.charCodeAt(me);Q.write_shift(1,ye>=128?95:ye)}return Q.write_shift(1,0),Q}function j(F,U){var H=M(F);H[1].v=F.read_shift(2);var P=H[1].v>>1;if(H[1].v&1)switch(P&7){case 0:P=(P>>3)*5e3;break;case 1:P=(P>>3)*500;break;case 2:P=(P>>3)/20;break;case 3:P=(P>>3)/200;break;case 4:P=(P>>3)/2e3;break;case 5:P=(P>>3)/2e4;break;case 6:P=(P>>3)/16;break;case 7:P=(P>>3)/64;break}return H[1].v=P,H}function E(F,U){var H=M(F),P=F.read_shift(4),Q=F.read_shift(4),me=F.read_shift(2);if(me==65535)return P===0&&Q===3221225472?(H[1].t="e",H[1].v=15):P===0&&Q===3489660928?(H[1].t="e",H[1].v=42):H[1].v=0,H;var ye=me&32768;return me=(me&32767)-16446,H[1].v=(1-ye*2)*(Q*Math.pow(2,me+32)+P*Math.pow(2,me)),H}function L(F,U,H,P){var Q=vr(14);if(Q.write_shift(2,F),Q.write_shift(1,H),Q.write_shift(1,U),P==0)return Q.write_shift(4,0),Q.write_shift(4,0),Q.write_shift(2,65535),Q;var me=0,ye=0,oe=0,le=0;return P<0&&(me=1,P=-P),ye=Math.log2(P)|0,P/=Math.pow(2,ye-31),le=P>>>0,(le&2147483648)==0&&(P/=2,++ye,le=P>>>0),P-=le,le|=2147483648,le>>>=0,P*=Math.pow(2,32),oe=P>>>0,Q.write_shift(4,oe),Q.write_shift(4,le),ye+=16383+(me?32768:0),Q.write_shift(2,ye),Q}function G(F,U){var H=E(F);return F.l+=U-14,H}function C(F,U){var H=M(F),P=F.read_shift(4);return H[1].v=P>>6,H}function I(F,U){var H=M(F),P=F.read_shift(8,"f");return H[1].v=P,H}function q(F,U){var H=I(F);return F.l+=U-10,H}function ae(F,U){return F[F.l+U-1]==0?F.read_shift(U,"cstr"):""}function ce(F,U){var H=F[F.l++];H>U-1&&(H=U-1);for(var P="";P.length<H;)P+=String.fromCharCode(F[F.l++]);return P}function de(F,U,H){if(!(!H.qpro||U<21)){var P=F.read_shift(1);F.l+=17,F.l+=1,F.l+=2;var Q=F.read_shift(U-21,"cstr");return[P,Q]}}function ie(F,U){for(var H={},P=F.l+U;F.l<P;){var Q=F.read_shift(2);if(Q==14e3){for(H[Q]=[0,""],H[Q][0]=F.read_shift(2);F[F.l];)H[Q][1]+=String.fromCharCode(F[F.l]),F.l++;F.l++}}return H}function B(F,U){var H=vr(5+F.length);H.write_shift(2,14e3),H.write_shift(2,U);for(var P=0;P<F.length;++P){var Q=F.charCodeAt(P);H[H.l++]=Q>127?95:Q}return H[H.l++]=0,H}var re={0:{n:"BOF",f:mr},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:c},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:d},14:{n:"NUMBER",f:g},15:{n:"LABEL",f},16:{n:"FORMULA",f:h},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:ae},222:{n:"SHEETNAMELP",f:ce},65535:{n:""}},he={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:S},23:{n:"NUMBER17",f:E},24:{n:"NUMBER18",f:j},25:{n:"FORMULA19",f:G},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:ie},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:C},38:{n:"??"},39:{n:"NUMBER27",f:I},40:{n:"FORMULA28",f:q},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:ae},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:de},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:n,book_to_wk3:a,to_workbook:t}}();function Iu(e){var t={},r=e.match(jr),n=0,a=!1;if(r)for(;n!=r.length;++n){var s=Re(r[n]);switch(s[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if(s.val=="1")break;t.cp=vs[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=s.val;break;case"<sz":t.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting";break}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if(s.val=="0")break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if(s.val=="0")break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":s.rgb&&(t.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":break;case"<scheme":break;case"<scheme>":case"<scheme/>":case"</scheme>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(s[0].charCodeAt(1)!==47&&!a)throw new Error("Unrecognized rich format "+s[0])}}return t}var ju=function(){var e=Ba("t"),t=Ba("rPr");function r(s){var i=s.match(e);if(!i)return{t:"s",v:""};var c={t:"s",v:ze(i[1])},o=s.match(t);return o&&(c.s=Iu(o[1])),c}var n=/<(?:\w+:)?r>/g,a=/<\/(?:\w+:)?r>/;return function(i){return i.replace(n,"").split(a).map(r).filter(function(c){return c.v})}}(),Pu=function(){var t=/(\r\n|\n)/g;function r(a,s,i){var c=[];a.u&&c.push("text-decoration: underline;"),a.uval&&c.push("text-underline-style:"+a.uval+";"),a.sz&&c.push("font-size:"+a.sz+"pt;"),a.outline&&c.push("text-effect: outline;"),a.shadow&&c.push("text-shadow: auto;"),s.push('<span style="'+c.join("")+'">'),a.b&&(s.push("<b>"),i.push("</b>")),a.i&&(s.push("<i>"),i.push("</i>")),a.strike&&(s.push("<s>"),i.push("</s>"));var o=a.valign||"";return o=="superscript"||o=="super"?o="sup":o=="subscript"&&(o="sub"),o!=""&&(s.push("<"+o+">"),i.push("</"+o+">")),i.push("</span>"),a}function n(a){var s=[[],a.v,[]];return a.v?(a.s&&r(a.s,s[0],s[2]),s[0].join("")+s[1].replace(t,"<br/>")+s[2].join("")):""}return function(s){return s.map(n).join("")}}(),Lu=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Bu=/<(?:\w+:)?r>/,Mu=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function js(e,t){var r=t?t.cellHTML:!0,n={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(n.t=ze(ar(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),n.r=ar(e),r&&(n.h=ks(n.t))):e.match(Bu)&&(n.r=ar(e),n.t=ze(ar((e.replace(Mu,"").match(Lu)||[]).join("").replace(jr,""))),r&&(n.h=Pu(ju(n.r)))),n):{t:""}}var $u=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Uu=/<(?:\w+:)?(?:si|sstItem)>/g,Hu=/<\/(?:\w+:)?(?:si|sstItem)>/;function Gu(e,t){var r=[],n="";if(!e)return r;var a=e.match($u);if(a){n=a[2].replace(Uu,"").split(Hu);for(var s=0;s!=n.length;++s){var i=js(n[s].trim(),t);i!=null&&(r[r.length]=i)}a=Re(a[1]),r.Count=a.count,r.Unique=a.uniqueCount}return r}function Vu(e){return[e.read_shift(4),e.read_shift(4)]}function Wu(e,t){var r=[],n=!1;return Et(e,function(s,i,c){switch(c){case 159:r.Count=s[0],r.Unique=s[1];break;case 19:r.push(s);break;case 160:return!0;case 35:n=!0;break;case 36:n=!1;break;default:if(i.T,!n||t.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}),r}function hc(e){for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function wt(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function Xu(e){var t={};return t.id=e.read_shift(0,"lpp4"),t.R=wt(e,4),t.U=wt(e,4),t.W=wt(e,4),t}function Ku(e){for(var t=e.read_shift(4),r=e.l+t-4,n={},a=e.read_shift(4),s=[];a-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(n.name=e.read_shift(0,"lpp4"),n.comps=s,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return n}function zu(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(Ku(e));return t}function Yu(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}function Ju(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=wt(e,4),t.U=wt(e,4),t.W=wt(e,4),t}function qu(e){var t=Ju(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),e.read_shift(4)!=4)throw new Error("Bad !Primary record");return t}function xc(e,t){var r=e.l+t,n={};n.Flags=e.read_shift(4)&63,e.l+=4,n.AlgID=e.read_shift(4);var a=!1;switch(n.AlgID){case 26126:case 26127:case 26128:a=n.Flags==36;break;case 26625:a=n.Flags==4;break;case 0:a=n.Flags==16||n.Flags==4||n.Flags==36;break;default:throw"Unrecognized encryption algorithm: "+n.AlgID}if(!a)throw new Error("Encryption Flags/AlgID mismatch");return n.AlgIDHash=e.read_shift(4),n.KeySize=e.read_shift(4),n.ProviderType=e.read_shift(4),e.l+=8,n.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,n}function dc(e,t){var r={},n=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,n),e.l=n,r}function Zu(e){var t=wt(e);switch(t.Minor){case 2:return[t.Minor,Qu(e)];case 3:return[t.Minor,eh()];case 4:return[t.Minor,rh(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}function Qu(e){var t=e.read_shift(4);if((t&63)!=36)throw new Error("EncryptionInfo mismatch");var r=e.read_shift(4),n=xc(e,r),a=dc(e,e.length-e.l);return{t:"Std",h:n,v:a}}function eh(){throw new Error("File is password-protected: ECMA-376 Extensible")}function rh(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),n={};return r.replace(jr,function(s){var i=Re(s);switch(xt(i[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":t.forEach(function(c){n[c]=i[c]});break;case"<dataIntegrity":n.encryptedHmacKey=i.encryptedHmacKey,n.encryptedHmacValue=i.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":n.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":n.uri=i.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":n.encs.push(i);break;default:throw i[0]}}),n}function th(e,t){var r={},n=r.EncryptionVersionInfo=wt(e,4);if(t-=4,n.Minor!=2)throw new Error("unrecognized minor version code: "+n.Minor);if(n.Major>4||n.Major<2)throw new Error("unrecognized major version code: "+n.Major);r.Flags=e.read_shift(4),t-=4;var a=e.read_shift(4);return t-=4,r.EncryptionHeader=xc(e,a),t-=a,r.EncryptionVerifier=dc(e,t),r}function ah(e){var t={},r=t.EncryptionVersionInfo=wt(e,4);if(r.Major!=1||r.Minor!=1)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}function nh(e){var t=0,r,n=hc(e),a=n.length+1,s,i,c,o,u;for(r=Ct(a),r[0]=n.length,s=1;s!=a;++s)r[s]=n[s-1];for(s=a-1;s>=0;--s)i=r[s],c=(t&16384)===0?0:1,o=t<<1&32767,u=c|o,t=u^i;return t^52811}var mc=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],n=function(i){return(i/2|i*128)&255},a=function(i,c){return n(i^c)},s=function(i){for(var c=t[i.length-1],o=104,u=i.length-1;u>=0;--u)for(var f=i[u],x=0;x!=7;++x)f&64&&(c^=r[o]),f*=2,--o;return c};return function(i){for(var c=hc(i),o=s(c),u=c.length,f=Ct(16),x=0;x!=16;++x)f[x]=0;var d,p,g;for((u&1)===1&&(d=o>>8,f[u]=a(e[0],d),--u,d=o&255,p=c[c.length-1],f[u]=a(p,d));u>0;)--u,d=o>>8,f[u]=a(c[u],d),--u,d=o&255,f[u]=a(c[u],d);for(u=15,g=15-c.length;g>0;)d=o>>8,f[u]=a(e[g],d),--u,--g,d=o&255,f[u]=a(c[u],d),--u,--g;return f}}(),sh=function(e,t,r,n,a){a||(a=t),n||(n=mc(e));var s,i;for(s=0;s!=t.length;++s)i=t[s],i^=n[r],i=(i>>5|i<<3)&255,a[s]=i,++r;return[a,r,n]},ih=function(e){var t=0,r=mc(e);return function(n){var a=sh("",n,t,r);return t=a[1],a[0]}};function ch(e,t,r,n){var a={key:mr(e),verificationBytes:mr(e)};return r.password&&(a.verifier=nh(r.password)),n.valid=a.verificationBytes===a.verifier,n.valid&&(n.insitu=ih(r.password)),a}function lh(e,t,r){var n=r||{};return n.Info=e.read_shift(2),e.l-=2,n.Info===1?n.Data=ah(e):n.Data=th(e,t),n}function oh(e,t,r){var n={Type:r.biff>=8?e.read_shift(2):0};return n.Type?lh(e,t-2,n):ch(e,r.biff>=8?t:t-2,r,n),n}var fh=function(){function e(a,s){switch(s.type){case"base64":return t(Xr(a),s);case"binary":return t(a,s);case"buffer":return t(We&&Buffer.isBuffer(a)?a.toString("binary"):Kt(a),s);case"array":return t(Vt(a),s)}throw new Error("Unrecognized type "+s.type)}function t(a,s){var i=s||{},c=i.dense?[]:{},o=a.match(/\\trowd.*?\\row\b/g);if(!o.length)throw new Error("RTF missing table");var u={s:{c:0,r:0},e:{c:0,r:o.length-1}};return o.forEach(function(f,x){Array.isArray(c)&&(c[x]=[]);for(var d=/\\\w+\b/g,p=0,g,m=-1;g=d.exec(f);){switch(g[0]){case"\\cell":var h=f.slice(p,d.lastIndex-g[0].length);if(h[0]==" "&&(h=h.slice(1)),++m,h.length){var _={v:h,t:"s"};Array.isArray(c)?c[x][m]=_:c[Le({r:x,c:m})]=_}break}p=d.lastIndex}m>u.e.c&&(u.e.c=m)}),c["!ref"]=Ke(u),c}function r(a,s){return Dt(e(a,s),s)}function n(a){for(var s=["{\\rtf1\\ansi"],i=or(a["!ref"]),c,o=Array.isArray(a),u=i.s.r;u<=i.e.r;++u){s.push("\\trowd\\trautofit1");for(var f=i.s.c;f<=i.e.c;++f)s.push("\\cellx"+(f+1));for(s.push("\\pard\\intbl"),f=i.s.c;f<=i.e.c;++f){var x=Le({r:u,c:f});c=o?(a[u]||[])[f]:a[x],!(!c||c.v==null&&(!c.f||c.F))&&(s.push(" "+(c.w||(_t(c),c.w))),s.push("\\cell"))}s.push("\\pard\\intbl\\row")}return s.join("")+"}"}return{to_workbook:r,to_sheet:e,from_sheet:n}}();function uh(e){var t=e.slice(e[0]==="#"?1:0).slice(0,6);return[parseInt(t.slice(0,2),16),parseInt(t.slice(2,4),16),parseInt(t.slice(4,6),16)]}function Ua(e){for(var t=0,r=1;t!=3;++t)r=r*256+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function hh(e){var t=e[0]/255,r=e[1]/255,n=e[2]/255,a=Math.max(t,r,n),s=Math.min(t,r,n),i=a-s;if(i===0)return[0,0,t];var c=0,o=0,u=a+s;switch(o=i/(u>1?2-u:u),a){case t:c=((r-n)/i+6)%6;break;case r:c=(n-t)/i+2;break;case n:c=(t-r)/i+4;break}return[c/6,o,u/2]}function xh(e){var t=e[0],r=e[1],n=e[2],a=r*2*(n<.5?n:1-n),s=n-a/2,i=[s,s,s],c=6*t,o;if(r!==0)switch(c|0){case 0:case 6:o=a*c,i[0]+=a,i[1]+=o;break;case 1:o=a*(2-c),i[0]+=o,i[1]+=a;break;case 2:o=a*(c-2),i[1]+=a,i[2]+=o;break;case 3:o=a*(4-c),i[1]+=o,i[2]+=a;break;case 4:o=a*(c-4),i[2]+=a,i[0]+=o;break;case 5:o=a*(6-c),i[2]+=o,i[0]+=a;break}for(var u=0;u!=3;++u)i[u]=Math.round(i[u]*255);return i}function wn(e,t){if(t===0)return e;var r=hh(uh(e));return t<0?r[2]=r[2]*(1+t):r[2]=1-(1-r[2])*(1-t),Ua(xh(r))}var gc=6,dh=15,mh=1,Br=gc;function _n(e){return Math.floor((e+Math.round(128/Br)/256)*Br)}function En(e){return Math.floor((e-5)/Br*100+.5)/100}function cs(e){return Math.round((e*Br+5)/Br*256)/256}function Xn(e){return cs(En(_n(e)))}function Ps(e){var t=Math.abs(e-Xn(e)),r=Br;if(t>.005)for(Br=mh;Br<dh;++Br)Math.abs(e-Xn(e))<=t&&(t=Math.abs(e-Xn(e)),r=Br);Br=r}function ua(e){e.width?(e.wpx=_n(e.width),e.wch=En(e.wpx),e.MDW=Br):e.wpx?(e.wch=En(e.wpx),e.width=cs(e.wch),e.MDW=Br):typeof e.wch=="number"&&(e.width=cs(e.wch),e.wpx=_n(e.width),e.MDW=Br),e.customWidth&&delete e.customWidth}var gh=96,pc=gh;function vc(e){return e*96/pc}function Ha(e){return e*pc/96}var ph={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function vh(e,t,r,n){t.Borders=[];var a={},s=!1;(e[0].match(jr)||[]).forEach(function(i){var c=Re(i);switch(xt(c[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":a={},c.diagonalUp&&(a.diagonalUp=cr(c.diagonalUp)),c.diagonalDown&&(a.diagonalDown=cr(c.diagonalDown)),t.Borders.push(a);break;case"</border>":break;case"<left/>":break;case"<left":case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":case"<bottom>":break;case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":break;case"</vertical>":break;case"<start":case"<start>":case"<start/>":break;case"</start>":break;case"<end":case"<end>":case"<end/>":break;case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+c[0]+" in borders")}})}function yh(e,t,r,n){t.Fills=[];var a={},s=!1;(e[0].match(jr)||[]).forEach(function(i){var c=Re(i);switch(xt(c[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":a={},t.Fills.push(a);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":t.Fills.push(a),a={};break;case"<patternFill":case"<patternFill>":c.patternType&&(a.patternType=c.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":a.bgColor||(a.bgColor={}),c.indexed&&(a.bgColor.indexed=parseInt(c.indexed,10)),c.theme&&(a.bgColor.theme=parseInt(c.theme,10)),c.tint&&(a.bgColor.tint=parseFloat(c.tint)),c.rgb&&(a.bgColor.rgb=c.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":a.fgColor||(a.fgColor={}),c.theme&&(a.fgColor.theme=parseInt(c.theme,10)),c.tint&&(a.fgColor.tint=parseFloat(c.tint)),c.rgb!=null&&(a.fgColor.rgb=c.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":break;case"</stop>":break;case"<color":case"<color/>":break;case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+c[0]+" in fills")}})}function wh(e,t,r,n){t.Fonts=[];var a={},s=!1;(e[0].match(jr)||[]).forEach(function(i){var c=Re(i);switch(xt(c[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":t.Fonts.push(a),a={};break;case"<name":c.val&&(a.name=ar(c.val));break;case"<name/>":case"</name>":break;case"<b":a.bold=c.val?cr(c.val):1;break;case"<b/>":a.bold=1;break;case"<i":a.italic=c.val?cr(c.val):1;break;case"<i/>":a.italic=1;break;case"<u":switch(c.val){case"none":a.underline=0;break;case"single":a.underline=1;break;case"double":a.underline=2;break;case"singleAccounting":a.underline=33;break;case"doubleAccounting":a.underline=34;break}break;case"<u/>":a.underline=1;break;case"<strike":a.strike=c.val?cr(c.val):1;break;case"<strike/>":a.strike=1;break;case"<outline":a.outline=c.val?cr(c.val):1;break;case"<outline/>":a.outline=1;break;case"<shadow":a.shadow=c.val?cr(c.val):1;break;case"<shadow/>":a.shadow=1;break;case"<condense":a.condense=c.val?cr(c.val):1;break;case"<condense/>":a.condense=1;break;case"<extend":a.extend=c.val?cr(c.val):1;break;case"<extend/>":a.extend=1;break;case"<sz":c.val&&(a.sz=+c.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":c.val&&(a.vertAlign=c.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":c.val&&(a.family=parseInt(c.val,10));break;case"<family/>":case"</family>":break;case"<scheme":c.val&&(a.scheme=c.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if(c.val=="1")break;c.codepage=vs[parseInt(c.val,10)];break;case"<color":if(a.color||(a.color={}),c.auto&&(a.color.auto=cr(c.auto)),c.rgb)a.color.rgb=c.rgb.slice(-6);else if(c.indexed){a.color.index=parseInt(c.indexed,10);var o=Ht[a.color.index];a.color.index==81&&(o=Ht[1]),o||(o=Ht[1]),a.color.rgb=o[0].toString(16)+o[1].toString(16)+o[2].toString(16)}else c.theme&&(a.color.theme=parseInt(c.theme,10),c.tint&&(a.color.tint=parseFloat(c.tint)),c.theme&&r.themeElements&&r.themeElements.clrScheme&&(a.color.rgb=wn(r.themeElements.clrScheme[a.color.theme].rgb,a.color.tint||0)));break;case"<color/>":case"</color>":break;case"<AlternateContent":s=!0;break;case"</AlternateContent>":s=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+c[0]+" in fonts")}})}function _h(e,t,r){t.NumberFmt=[];for(var n=ht(Me),a=0;a<n.length;++a)t.NumberFmt[n[a]]=Me[n[a]];var s=e[0].match(jr);if(s)for(a=0;a<s.length;++a){var i=Re(s[a]);switch(xt(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":{var c=ze(ar(i.formatCode)),o=parseInt(i.numFmtId,10);if(t.NumberFmt[o]=c,o>0){if(o>392){for(o=392;o>60&&t.NumberFmt[o]!=null;--o);t.NumberFmt[o]=c}Ut(c,o)}}break;case"</numFmt>":break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}var cn=["numFmtId","fillId","fontId","borderId","xfId"],ln=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function Eh(e,t,r){t.CellXf=[];var n,a=!1;(e[0].match(jr)||[]).forEach(function(s){var i=Re(s),c=0;switch(xt(i[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(n=i,delete n[0],c=0;c<cn.length;++c)n[cn[c]]&&(n[cn[c]]=parseInt(n[cn[c]],10));for(c=0;c<ln.length;++c)n[ln[c]]&&(n[ln[c]]=cr(n[ln[c]]));if(t.NumberFmt&&n.numFmtId>392){for(c=392;c>60;--c)if(t.NumberFmt[n.numFmtId]==t.NumberFmt[c]){n.numFmtId=c;break}}t.CellXf.push(n);break;case"</xf>":break;case"<alignment":case"<alignment/>":var o={};i.vertical&&(o.vertical=i.vertical),i.horizontal&&(o.horizontal=i.horizontal),i.textRotation!=null&&(o.textRotation=i.textRotation),i.indent&&(o.indent=i.indent),i.wrapText&&(o.wrapText=cr(i.wrapText)),n.alignment=o;break;case"</alignment>":break;case"<protection":break;case"</protection>":case"<protection/>":break;case"<AlternateContent":a=!0;break;case"</AlternateContent>":a=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(r&&r.WTF&&!a)throw new Error("unrecognized "+i[0]+" in cellXfs")}})}var Th=function(){var t=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,n=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,s=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(c,o,u){var f={};if(!c)return f;c=c.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");var x;return(x=c.match(t))&&_h(x,f,u),(x=c.match(a))&&wh(x,f,o,u),(x=c.match(n))&&yh(x,f,o,u),(x=c.match(s))&&vh(x,f,o,u),(x=c.match(r))&&Eh(x,f,u),f}}();function bh(e,t){var r=e.read_shift(2),n=Rr(e);return[r,n]}function kh(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=Io(e);a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1);var s=e.read_shift(2);switch(s===700&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript";break}var i=e.read_shift(1);i!=0&&(n.underline=i);var c=e.read_shift(1);c>0&&(n.family=c);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=Ro(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor";break}return n.name=Rr(e),n}var Sh=Ir;function Ah(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}var Fh=Ir;function Nh(e,t,r){var n={};n.NumberFmt=[];for(var a in Me)n.NumberFmt[a]=Me[a];n.CellXf=[],n.Fonts=[];var s=[],i=!1;return Et(e,function(o,u,f){switch(f){case 44:n.NumberFmt[o[0]]=o[1],Ut(o[1],o[0]);break;case 43:n.Fonts.push(o),o.color.theme!=null&&t&&t.themeElements&&t.themeElements.clrScheme&&(o.color.rgb=wn(t.themeElements.clrScheme[o.color.theme].rgb,o.color.tint||0));break;case 1025:break;case 45:break;case 46:break;case 47:s[s.length-1]==617&&n.CellXf.push(o);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(f),i=!0;break;case 38:s.pop(),i=!1;break;default:if(u.T>0)s.push(f);else if(u.T<0)s.pop();else if(!i||r.WTF&&s[s.length-1]!=37)throw new Error("Unexpected record 0x"+f.toString(16))}}),n}var Ch=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Oh(e,t,r){t.themeElements.clrScheme=[];var n={};(e[0].match(jr)||[]).forEach(function(a){var s=Re(a);switch(s[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":n.rgb=s.val;break;case"<a:sysClr":n.rgb=s.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":s[0].charAt(1)==="/"?(t.themeElements.clrScheme[Ch.indexOf(s[0])]=n,n={}):n.name=s[0].slice(3,s[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+s[0]+" in clrScheme")}})}function Dh(){}function Rh(){}var Ih=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,jh=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,Ph=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function Lh(e,t,r){t.themeElements={};var n;[["clrScheme",Ih,Oh],["fontScheme",jh,Dh],["fmtScheme",Ph,Rh]].forEach(function(a){if(!(n=e.match(a[1])))throw new Error(a[0]+" not found in themeElements");a[2](n,t,r)})}var Bh=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function yc(e,t){(!e||e.length===0)&&(e=Mh());var r,n={};if(!(r=e.match(Bh)))throw new Error("themeElements not found in theme");return Lh(r[0],n,t),n.raw=e,n}function Mh(e,t){var r=[Ci];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function $h(e,t,r){var n=e.l+t,a=e.read_shift(4);if(a!==124226){if(!r.cellStyles){e.l=n;return}var s=e.slice(e.l);e.l=n;var i;try{i=Ni(s,{type:"array"})}catch{return}var c=Wr(i,"theme/theme/theme1.xml",!0);if(c)return yc(c,r)}}function Uh(e){return e.read_shift(4)}function Hh(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:e.l+=4;break;case 1:t.xclrValue=Gh(e,4);break;case 2:t.xclrValue=sc(e);break;case 3:t.xclrValue=Uh(e);break;case 4:e.l+=4;break}return e.l+=8,t}function Gh(e,t){return Ir(e,t)}function Vh(e,t){return Ir(e,t)}function Wh(e){var t=e.read_shift(2),r=e.read_shift(2)-4,n=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:n[1]=Hh(e);break;case 6:n[1]=Vh(e,r);break;case 14:case 15:n[1]=e.read_shift(r===1?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return n}function Xh(e,t){var r=e.l+t;e.l+=2;var n=e.read_shift(2);e.l+=2;for(var a=e.read_shift(2),s=[];a-- >0;)s.push(Wh(e,r-e.l));return{ixfe:n,ext:s}}function Kh(e,t){t.forEach(function(r){r[0]})}function zh(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:Rr(e)}}function Yh(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}function Jh(e){return e.l+=4,e.read_shift(4)!=0}function qh(e,t,r){var n={Types:[],Cell:[],Value:[]},a=r||{},s=[],i=!1,c=2;return Et(e,function(o,u,f){switch(f){case 335:n.Types.push({name:o.name});break;case 51:o.forEach(function(x){c==1?n.Cell.push({type:n.Types[x[0]-1].name,index:x[1]}):c==0&&n.Value.push({type:n.Types[x[0]-1].name,index:x[1]})});break;case 337:c=o?1:0;break;case 338:c=2;break;case 35:s.push(f),i=!0;break;case 36:s.pop(),i=!1;break;default:if(!u.T){if(!i||a.WTF&&s[s.length-1]!=35)throw new Error("Unexpected record 0x"+f.toString(16))}}}),n}function Zh(e,t,r){var n={Types:[],Cell:[],Value:[]};if(!e)return n;var a=!1,s=2,i;return e.replace(jr,function(c){var o=Re(c);switch(xt(o[0])){case"<?xml":break;case"<metadata":case"</metadata>":break;case"<metadataTypes":case"</metadataTypes>":break;case"<metadataType":n.Types.push({name:o.name});break;case"</metadataType>":break;case"<futureMetadata":for(var u=0;u<n.Types.length;++u)n.Types[u].name==o.name&&(i=n.Types[u]);break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":s==1?n.Cell.push({type:n.Types[o.t-1].name,index:+o.v}):s==0&&n.Value.push({type:n.Types[o.t-1].name,index:+o.v});break;case"</rc>":break;case"<cellMetadata":s=1;break;case"</cellMetadata>":s=2;break;case"<valueMetadata":s=0;break;case"</valueMetadata>":s=2;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;case"<rvb":if(!i)break;i.offsets||(i.offsets=[]),i.offsets.push(+o.i);break;default:if(!a&&r.WTF)throw new Error("unrecognized "+o[0]+" in metadata")}return c}),n}function Qh(e){var t=[];if(!e)return t;var r=1;return(e.match(jr)||[]).forEach(function(n){var a=Re(n);switch(a[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete a[0],a.i?r=a.i:a.i=r,t.push(a);break}}),t}function ex(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=Le(r);var n=e.read_shift(1);return n&2&&(t.l="1"),n&8&&(t.a="1"),t}function rx(e,t,r){var n=[];return Et(e,function(s,i,c){switch(c){case 63:n.push(s);break;default:if(!i.T)throw new Error("Unexpected record 0x"+c.toString(16))}}),n}function tx(e,t,r,n){if(!e)return e;var a=n||{},s=!1;Et(e,function(c,o,u){switch(u){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(!o.T){if(!s||a.WTF)throw new Error("Unexpected record 0x"+u.toString(16))}}},a)}function ax(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return t["!id"][r].Target}function P0(e,t,r,n){var a=Array.isArray(e),s;t.forEach(function(i){var c=Mr(i.ref);if(a?(e[c.r]||(e[c.r]=[]),s=e[c.r][c.c]):s=e[i.ref],!s){s={t:"z"},a?e[c.r][c.c]=s:e[i.ref]=s;var o=or(e["!ref"]||"BDWGO1000001:A1");o.s.r>c.r&&(o.s.r=c.r),o.e.r<c.r&&(o.e.r=c.r),o.s.c>c.c&&(o.s.c=c.c),o.e.c<c.c&&(o.e.c=c.c);var u=Ke(o);u!==e["!ref"]&&(e["!ref"]=u)}s.c||(s.c=[]);var f={a:i.author,t:i.t,r:i.r,T:r};i.h&&(f.h=i.h);for(var x=s.c.length-1;x>=0;--x){if(!r&&s.c[x].T)return;r&&!s.c[x].T&&s.c.splice(x,1)}if(r&&n){for(x=0;x<n.length;++x)if(f.a==n[x].id){f.a=n[x].name||f.a;break}}s.c.push(f)})}function nx(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],n=[],a=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);a&&a[1]&&a[1].split(/<\/\w*:?author>/).forEach(function(i){if(!(i===""||i.trim()==="")){var c=i.match(/<(?:\w+:)?author[^>]*>(.*)/);c&&r.push(c[1])}});var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach(function(i){if(!(i===""||i.trim()==="")){var c=i.match(/<(?:\w+:)?comment[^>]*>/);if(c){var o=Re(c[0]),u={author:o.authorId&&r[o.authorId]||"sheetjsghost",ref:o.ref,guid:o.guid},f=Mr(o.ref);if(!(t.sheetRows&&t.sheetRows<=f.r)){var x=i.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),d=!!x&&!!x[1]&&js(x[1])||{r:"",t:"",h:""};u.r=d.r,d.r=="<t></t>"&&(d.t=d.h=""),u.t=(d.t||"").replace(/\r\n/g,`
`).replace(/\r/g,`
`),t.cellHTML&&(u.h=d.h),n.push(u)}}}}),n}function sx(e,t){var r=[],n=!1,a={},s=0;return e.replace(jr,function(c,o){var u=Re(c);switch(xt(u[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":a={author:u.personId,guid:u.id,ref:u.ref,T:1};break;case"</threadedComment>":a.t!=null&&r.push(a);break;case"<text>":case"<text":s=o+c.length;break;case"</text>":a.t=e.slice(s,o).replace(/\r\n/g,`
`).replace(/\r/g,`
`);break;case"<mentions":case"<mentions>":n=!0;break;case"</mentions>":n=!1;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(!n&&t.WTF)throw new Error("unrecognized "+u[0]+" in threaded comments")}return c}),r}function ix(e,t){var r=[],n=!1;return e.replace(jr,function(s){var i=Re(s);switch(xt(i[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":r.push({name:i.displayname,id:i.id});break;case"</person>":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(!n&&t.WTF)throw new Error("unrecognized "+i[0]+" in threaded comments")}return s}),r}function cx(e){var t={};t.iauthor=e.read_shift(4);var r=Yt(e);return t.rfx=r.s,t.ref=Le(r.s),e.l+=16,t}var lx=Rr;function ox(e,t){var r=[],n=[],a={},s=!1;return Et(e,function(c,o,u){switch(u){case 632:n.push(c);break;case 635:a=c;break;case 637:a.t=c.t,a.h=c.h,a.r=c.r;break;case 636:if(a.author=n[a.iauthor],delete a.iauthor,t.sheetRows&&a.rfx&&t.sheetRows<=a.rfx.r)break;a.t||(a.t=""),delete a.rfx,r.push(a);break;case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:break;case 38:break;default:if(!o.T){if(!s||t.WTF)throw new Error("Unexpected record 0x"+u.toString(16))}}}),r}var fx="application/vnd.ms-office.vbaProject";function ux(e){var t=Xe.utils.cfb_new({root:"R"});return e.FullPaths.forEach(function(r,n){if(!(r.slice(-1)==="/"||!r.match(/_VBA_PROJECT_CUR/))){var a=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Xe.utils.cfb_add(t,a,e.FileIndex[n].content)}}),Xe.write(t)}function hx(){return{"!type":"dialog"}}function xx(){return{"!type":"dialog"}}function dx(){return{"!type":"macro"}}function mx(){return{"!type":"macro"}}var la=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(n,a,s,i){var c=!1,o=!1;s.length==0?o=!0:s.charAt(0)=="["&&(o=!0,s=s.slice(1,-1)),i.length==0?c=!0:i.charAt(0)=="["&&(c=!0,i=i.slice(1,-1));var u=s.length>0?parseInt(s,10)|0:0,f=i.length>0?parseInt(i,10)|0:0;return c?f+=t.c:--f,o?u+=t.r:--u,a+(c?"":"$")+yr(f)+(o?"":"$")+Sr(u)}return function(a,s){return t=s,a.replace(e,r)}}(),wc=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,gx=function(){return function(t,r){return t.replace(wc,function(n,a,s,i,c,o){var u=Ns(i)-(s?0:r.c),f=Fs(o)-(c?0:r.r),x=f==0?"":c?f+1:"["+f+"]",d=u==0?"":s?u+1:"["+u+"]";return a+"R"+x+"C"+d})}}();function _c(e,t){return e.replace(wc,function(r,n,a,s,i,c){return n+(a=="$"?a+s:yr(Ns(s)+t.c))+(i=="$"?i+c:Sr(Fs(c)+t.r))})}function px(e,t,r){var n=xa(t),a=n.s,s=Mr(r),i={r:s.r-a.r,c:s.c-a.c};return _c(e,i)}function vx(e){return e.length!=1}function L0(e){return e.replace(/_xlfn\./g,"")}function xr(e){e.l+=1}function Ot(e,t){var r=e.read_shift(2);return[r&16383,r>>14&1,r>>15&1]}function Ec(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return Tc(e);r.biff==12&&(n=4)}var a=e.read_shift(n),s=e.read_shift(n),i=Ot(e),c=Ot(e);return{s:{r:a,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:c[0],cRel:c[1],rRel:c[2]}}}function Tc(e){var t=Ot(e),r=Ot(e),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function yx(e,t,r){if(r.biff<8)return Tc(e);var n=e.read_shift(r.biff==12?4:2),a=e.read_shift(r.biff==12?4:2),s=Ot(e),i=Ot(e);return{s:{r:n,c:s[0],cRel:s[1],rRel:s[2]},e:{r:a,c:i[0],cRel:i[1],rRel:i[2]}}}function bc(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return wx(e);var n=e.read_shift(r&&r.biff==12?4:2),a=Ot(e);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function wx(e){var t=Ot(e),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}function _x(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:r&255,fQuoted:!!(r&16384),cRel:r>>15,rRel:r>>15}}function Ex(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return Tx(e);var a=e.read_shift(n>=12?4:2),s=e.read_shift(2),i=(s&16384)>>14,c=(s&32768)>>15;if(s&=16383,c==1)for(;a>524287;)a-=1048576;if(i==1)for(;s>8191;)s=s-16384;return{r:a,c:s,cRel:i,rRel:c}}function Tx(e){var t=e.read_shift(2),r=e.read_shift(1),n=(t&32768)>>15,a=(t&16384)>>14;return t&=16383,n==1&&t>=8192&&(t=t-16384),a==1&&r>=128&&(r=r-256),{r:t,c:r,cRel:a,rRel:n}}function bx(e,t,r){var n=(e[e.l++]&96)>>5,a=Ec(e,r.biff>=2&&r.biff<=5?6:8,r);return[n,a]}function kx(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2,"i"),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}var i=Ec(e,s,r);return[n,a,i]}function Sx(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}function Ax(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}return e.l+=s,[n,a]}function Fx(e,t,r){var n=(e[e.l++]&96)>>5,a=yx(e,t-1,r);return[n,a]}function Nx(e,t,r){var n=(e[e.l++]&96)>>5;return e.l+=r.biff==2?6:r.biff==12?14:7,[n]}function B0(e){var t=e[e.l+1]&1,r=1;return e.l+=4,[t,r]}function Cx(e,t,r){e.l+=2;for(var n=e.read_shift(r&&r.biff==2?1:2),a=[],s=0;s<=n;++s)a.push(e.read_shift(r&&r.biff==2?1:2));return a}function Ox(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function Dx(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=2,[n,e.read_shift(r&&r.biff==2?1:2)]}function Rx(e){var t=e[e.l+1]&255?1:0;return e.l+=2,[t,e.read_shift(2)]}function Ix(e,t,r){var n=e[e.l+1]&255?1:0;return e.l+=r&&r.biff==2?3:4,[n]}function kc(e){var t=e.read_shift(1),r=e.read_shift(1);return[t,r]}function jx(e){return e.read_shift(2),kc(e)}function Px(e){return e.read_shift(2),kc(e)}function Lx(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=bc(e,0,r);return[n,a]}function Bx(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=Ex(e,0,r);return[n,a]}function Mx(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(2);r&&r.biff==5&&(e.l+=12);var s=bc(e,0,r);return[n,a,s]}function $x(e,t,r){var n=(e[e.l]&96)>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[Ud[a],Fc[a],n]}function Ux(e,t,r){var n=e[e.l++],a=e.read_shift(1),s=r&&r.biff<=3?[n==88?-1:0,e.read_shift(1)]:Hx(e);return[a,(s[0]===0?Fc:$d)[s[1]]]}function Hx(e){return[e[e.l+1]>>7,e.read_shift(2)&32767]}function Gx(e,t,r){e.l+=r&&r.biff==2?3:4}function Vx(e,t,r){if(e.l++,r&&r.biff==12)return[e.read_shift(4,"i"),0];var n=e.read_shift(2),a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function Wx(e){return e.l++,Jt[e.read_shift(1)]}function Xx(e){return e.l++,e.read_shift(2)}function Kx(e){return e.l++,e.read_shift(1)!==0}function zx(e){return e.l++,Or(e)}function Yx(e,t,r){return e.l++,Xa(e,t-1,r)}function Jx(e,t){var r=[e.read_shift(1)];if(t==12)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2;break}switch(r[0]){case 4:r[1]=ur(e,1)?"TRUE":"FALSE",t!=12&&(e.l+=7);break;case 37:case 16:r[1]=Jt[e[e.l]],e.l+=t==12?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Or(e);break;case 2:r[1]=qt(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function qx(e,t,r){for(var n=e.read_shift(r.biff==12?4:2),a=[],s=0;s!=n;++s)a.push((r.biff==12?Yt:An)(e));return a}function Zx(e,t,r){var n=0,a=0;r.biff==12?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,--a==0&&(a=256));for(var s=0,i=[];s!=n&&(i[s]=[]);++s)for(var c=0;c!=a;++c)i[s][c]=Jx(e,r.biff);return i}function Qx(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,s=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[n,0,s]}function ed(e,t,r){if(r.biff==5)return rd(e);var n=e.read_shift(1)>>>5&3,a=e.read_shift(2),s=e.read_shift(4);return[n,a,s]}function rd(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}function td(e,t,r){var n=e.read_shift(1)>>>5&3;e.l+=r&&r.biff==2?3:4;var a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function ad(e,t,r){var n=e.read_shift(1)>>>5&3,a=e.read_shift(r&&r.biff==2?1:2);return[n,a]}function nd(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,r.biff==12&&(e.l+=2),[n]}function sd(e,t,r){var n=(e[e.l++]&96)>>5,a=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6;break}return e.l+=s,[n,a]}var id=Ir,cd=Ir,ld=Ir;function za(e,t,r){return e.l+=2,[_x(e)]}function Ls(e){return e.l+=6,[]}var od=za,fd=Ls,ud=Ls,hd=za;function Sc(e){return e.l+=2,[mr(e),e.read_shift(2)&1]}var xd=za,dd=Sc,md=Ls,gd=za,pd=za,vd=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function yd(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),s=e.read_shift(2),i=vd[r>>2&31];return{ixti:t,coltype:r&3,rt:i,idx:n,c:a,C:s}}function wd(e){return e.l+=2,[e.read_shift(4)]}function _d(e,t,r){return e.l+=5,e.l+=2,e.l+=r.biff==2?1:4,["PTGSHEET"]}function Ed(e,t,r){return e.l+=r.biff==2?4:5,["PTGENDSHEET"]}function Td(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function bd(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2);return[t,r]}function kd(e){return e.l+=4,[0,0]}var M0={1:{n:"PtgExp",f:Vx},2:{n:"PtgTbl",f:ld},3:{n:"PtgAdd",f:xr},4:{n:"PtgSub",f:xr},5:{n:"PtgMul",f:xr},6:{n:"PtgDiv",f:xr},7:{n:"PtgPower",f:xr},8:{n:"PtgConcat",f:xr},9:{n:"PtgLt",f:xr},10:{n:"PtgLe",f:xr},11:{n:"PtgEq",f:xr},12:{n:"PtgGe",f:xr},13:{n:"PtgGt",f:xr},14:{n:"PtgNe",f:xr},15:{n:"PtgIsect",f:xr},16:{n:"PtgUnion",f:xr},17:{n:"PtgRange",f:xr},18:{n:"PtgUplus",f:xr},19:{n:"PtgUminus",f:xr},20:{n:"PtgPercent",f:xr},21:{n:"PtgParen",f:xr},22:{n:"PtgMissArg",f:xr},23:{n:"PtgStr",f:Yx},26:{n:"PtgSheet",f:_d},27:{n:"PtgEndSheet",f:Ed},28:{n:"PtgErr",f:Wx},29:{n:"PtgBool",f:Kx},30:{n:"PtgInt",f:Xx},31:{n:"PtgNum",f:zx},32:{n:"PtgArray",f:Nx},33:{n:"PtgFunc",f:$x},34:{n:"PtgFuncVar",f:Ux},35:{n:"PtgName",f:Qx},36:{n:"PtgRef",f:Lx},37:{n:"PtgArea",f:bx},38:{n:"PtgMemArea",f:td},39:{n:"PtgMemErr",f:id},40:{n:"PtgMemNoMem",f:cd},41:{n:"PtgMemFunc",f:ad},42:{n:"PtgRefErr",f:nd},43:{n:"PtgAreaErr",f:Sx},44:{n:"PtgRefN",f:Bx},45:{n:"PtgAreaN",f:Fx},46:{n:"PtgMemAreaN",f:Td},47:{n:"PtgMemNoMemN",f:bd},57:{n:"PtgNameX",f:ed},58:{n:"PtgRef3d",f:Mx},59:{n:"PtgArea3d",f:kx},60:{n:"PtgRefErr3d",f:sd},61:{n:"PtgAreaErr3d",f:Ax},255:{}},Sd={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Ad={1:{n:"PtgElfLel",f:Sc},2:{n:"PtgElfRw",f:gd},3:{n:"PtgElfCol",f:od},6:{n:"PtgElfRwV",f:pd},7:{n:"PtgElfColV",f:hd},10:{n:"PtgElfRadical",f:xd},11:{n:"PtgElfRadicalS",f:md},13:{n:"PtgElfColS",f:fd},15:{n:"PtgElfColSV",f:ud},16:{n:"PtgElfRadicalLel",f:dd},25:{n:"PtgList",f:yd},29:{n:"PtgSxName",f:wd},255:{}},Fd={0:{n:"PtgAttrNoop",f:kd},1:{n:"PtgAttrSemi",f:Ix},2:{n:"PtgAttrIf",f:Dx},4:{n:"PtgAttrChoose",f:Cx},8:{n:"PtgAttrGoto",f:Ox},16:{n:"PtgAttrSum",f:Gx},32:{n:"PtgAttrBaxcel",f:B0},33:{n:"PtgAttrBaxcel",f:B0},64:{n:"PtgAttrSpace",f:jx},65:{n:"PtgAttrSpaceSemi",f:Px},128:{n:"PtgAttrIfError",f:Rx},255:{}};function Ya(e,t,r,n){if(n.biff<8)return Ir(e,t);for(var a=e.l+t,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=Zx(e,0,n),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=qx(e,r[i][1],n),s.push(r[i][2]);break;case"PtgExp":n&&n.biff==12&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return t=a-e.l,t!==0&&s.push(Ir(e,t)),s}function Ja(e,t,r){for(var n=e.l+t,a,s,i=[];n!=e.l;)t=n-e.l,s=e[e.l],a=M0[s]||M0[Sd[s]],(s===24||s===25)&&(a=(s===24?Ad:Fd)[e[e.l+1]]),!a||!a.f?Ir(e,t):i.push([a.n,a.f(e,t,r)]);return i}function Nd(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],s=0;s<n.length;++s){var i=n[s];if(i)switch(i[0]){case 2:a.push('"'+i[1].replace(/"/g,'""')+'"');break;default:a.push(i[1])}else a.push("")}t.push(a.join(","))}return t.join(";")}var Cd={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Od(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function Ac(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),t==0?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=n[1]==-1?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return r.SID!=null?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];case 355:default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=n[1]==-1?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map(function(s){return s.Name}).join(";;");default:return e[n[0]][0][3]?(a=n[1]==-1?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function $0(e,t,r){var n=Ac(e,t,r);return n=="#REF"?n:Od(n,r)}function Cr(e,t,r,n,a){var s=a&&a.biff||8,i={s:{c:0,r:0}},c=[],o,u,f,x=0,d=0,p,g="";if(!e[0]||!e[0][0])return"";for(var m=-1,h="",_=0,k=e[0].length;_<k;++_){var v=e[0][_];switch(v[0]){case"PtgUminus":c.push("-"+c.pop());break;case"PtgUplus":c.push("+"+c.pop());break;case"PtgPercent":c.push(c.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(o=c.pop(),u=c.pop(),m>=0){switch(e[0][m][1][0]){case 0:h=lr(" ",e[0][m][1][1]);break;case 1:h=lr("\r",e[0][m][1][1]);break;default:if(h="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}u=u+h,m=-1}c.push(u+Cd[v[0]]+o);break;case"PtgIsect":o=c.pop(),u=c.pop(),c.push(u+" "+o);break;case"PtgUnion":o=c.pop(),u=c.pop(),c.push(u+","+o);break;case"PtgRange":o=c.pop(),u=c.pop(),c.push(u+":"+o);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":f=Na(v[1][1],i,a),c.push(Ca(f,s));break;case"PtgRefN":f=r?Na(v[1][1],r,a):v[1][1],c.push(Ca(f,s));break;case"PtgRef3d":x=v[1][1],f=Na(v[1][2],i,a),g=$0(n,x,a),c.push(g+"!"+Ca(f,s));break;case"PtgFunc":case"PtgFuncVar":var R=v[1][0],M=v[1][1];R||(R=0),R&=127;var S=R==0?[]:c.slice(-R);c.length-=R,M==="User"&&(M=S.shift()),c.push(M+"("+S.join(",")+")");break;case"PtgBool":c.push(v[1]?"TRUE":"FALSE");break;case"PtgInt":c.push(v[1]);break;case"PtgNum":c.push(String(v[1]));break;case"PtgStr":c.push('"'+v[1].replace(/"/g,'""')+'"');break;case"PtgErr":c.push(v[1]);break;case"PtgAreaN":p=w0(v[1][1],r?{s:r}:i,a),c.push(Vn(p,a));break;case"PtgArea":p=w0(v[1][1],i,a),c.push(Vn(p,a));break;case"PtgArea3d":x=v[1][1],p=v[1][2],g=$0(n,x,a),c.push(g+"!"+Vn(p,a));break;case"PtgAttrSum":c.push("SUM("+c.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":d=v[1][2];var w=(n.names||[])[d-1]||(n[0]||[])[d],j=w?w.Name:"SH33TJSNAME"+String(d);j&&j.slice(0,6)=="_xlfn."&&!a.xlfn&&(j=j.slice(6)),c.push(j);break;case"PtgNameX":var E=v[1][1];d=v[1][2];var L;if(a.biff<=5)E<0&&(E=-E),n[E]&&(L=n[E][d]);else{var G="";if(((n[E]||[])[0]||[])[0]==14849||(((n[E]||[])[0]||[])[0]==1025?n[E][d]&&n[E][d].itab>0&&(G=n.SheetNames[n[E][d].itab-1]+"!"):G=n.SheetNames[d-1]+"!"),n[E]&&n[E][d])G+=n[E][d].Name;else if(n[0]&&n[0][d])G+=n[0][d].Name;else{var C=(Ac(n,E,a)||"").split(";;");C[d-1]?G=C[d-1]:G+="SH33TJSERRX"}c.push(G);break}L||(L={Name:"SH33TJSERRY"}),c.push(L.Name);break;case"PtgParen":var I="(",q=")";if(m>=0){switch(h="",e[0][m][1][0]){case 2:I=lr(" ",e[0][m][1][1])+I;break;case 3:I=lr("\r",e[0][m][1][1])+I;break;case 4:q=lr(" ",e[0][m][1][1])+q;break;case 5:q=lr("\r",e[0][m][1][1])+q;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}c.push(I+c.pop()+q);break;case"PtgRefErr":c.push("#REF!");break;case"PtgRefErr3d":c.push("#REF!");break;case"PtgExp":f={c:v[1][1],r:v[1][0]};var ae={c:r.c,r:r.r};if(n.sharedf[Le(f)]){var ce=n.sharedf[Le(f)];c.push(Cr(ce,i,ae,n,a))}else{var de=!1;for(o=0;o!=n.arrayf.length;++o)if(u=n.arrayf[o],!(f.c<u[0].s.c||f.c>u[0].e.c)&&!(f.r<u[0].s.r||f.r>u[0].e.r)){c.push(Cr(u[1],i,ae,n,a)),de=!0;break}de||c.push(v[1])}break;case"PtgArray":c.push("{"+Nd(v[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=_;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":c.push("");break;case"PtgAreaErr":c.push("#REF!");break;case"PtgAreaErr3d":c.push("#REF!");break;case"PtgList":c.push("Table"+v[1].idx+"[#"+v[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(v));default:throw new Error("Unrecognized Formula Token: "+String(v))}var ie=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(a.biff!=3&&m>=0&&ie.indexOf(e[0][_][0])==-1){v=e[0][m];var B=!0;switch(v[1][0]){case 4:B=!1;case 0:h=lr(" ",v[1][1]);break;case 5:B=!1;case 1:h=lr("\r",v[1][1]);break;default:if(h="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+v[1][0])}c.push((B?h:"")+c.pop()+(B?"":h)),m=-1}}if(c.length>1&&a.WTF)throw new Error("bad formula stack");return c[0]}function Dd(e,t,r){var n=e.l+t,a=r.biff==2?1:2,s,i=e.read_shift(a);if(i==65535)return[[],Ir(e,t-2)];var c=Ja(e,i,r);return t!==i+a&&(s=Ya(e,t-i-a,c,r)),e.l=n,[c,s]}function Rd(e,t,r){var n=e.l+t,a=r.biff==2?1:2,s,i=e.read_shift(a);if(i==65535)return[[],Ir(e,t-2)];var c=Ja(e,i,r);return t!==i+a&&(s=Ya(e,t-i-a,c,r)),e.l=n,[c,s]}function Id(e,t,r,n){var a=e.l+t,s=Ja(e,n,r),i;return a!==e.l&&(i=Ya(e,a-e.l,s,r)),[s,i]}function jd(e,t,r){var n=e.l+t,a,s=e.read_shift(2),i=Ja(e,s,r);return s==65535?[[],Ir(e,t-2)]:(t!==s+2&&(a=Ya(e,n-s-2,i,r)),[i,a])}function Pd(e){var t;if(vt(e,e.l+6)!==65535)return[Or(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=e[e.l+2]===1,e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}function Kn(e,t,r){var n=e.l+t,a=dt(e);r.biff==2&&++e.l;var s=Pd(e),i=e.read_shift(1);r.biff!=2&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var c=Rd(e,n-e.l,r);return{cell:a,val:s[0],formula:c,shared:i>>3&1,tt:s[1]}}function Fn(e,t,r){var n=e.read_shift(4),a=Ja(e,n,r),s=e.read_shift(4),i=s>0?Ya(e,s,a,r):null;return[a,i]}var Ld=Fn,Nn=Fn,Bd=Fn,Md=Fn,$d={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Fc={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Ud={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function U0(e){return e.slice(0,3)=="of:"&&(e=e.slice(3)),e.charCodeAt(0)==61&&(e=e.slice(1),e.charCodeAt(0)==61&&(e=e.slice(1))),e=e.replace(/COM\.MICROSOFT\./g,""),e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(t,r){return r.replace(/\./g,"")}),e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1"),e.replace(/[;~]/g,",").replace(/\|/g,";")}function zn(e){var t=e.split(":"),r=t[0].split(".")[0];return[r,t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}var Ia={},oa={};function ja(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];t=="xlml"&&(r=[1,1,1,1,.5,.5]),e.left==null&&(e.left=r[0]),e.right==null&&(e.right=r[1]),e.top==null&&(e.top=r[2]),e.bottom==null&&(e.bottom=r[3]),e.header==null&&(e.header=r[4]),e.footer==null&&(e.footer=r[5])}}function Nc(e,t,r,n,a,s){try{n.cellNF&&(e.z=Me[t])}catch(c){if(n.WTF)throw c}if(!(e.t==="z"&&!n.cellStyles)){if(e.t==="d"&&typeof e.v=="string"&&(e.v=Tr(e.v)),(!n||n.cellText!==!1)&&e.t!=="z")try{if(Me[t]==null&&Ut($l[t]||"General",t),e.t==="e")e.w=e.w||Jt[e.v];else if(t===0)if(e.t==="n")(e.v|0)===e.v?e.w=e.v.toString(10):e.w=La(e.v);else if(e.t==="d"){var i=Ur(e.v);(i|0)===i?e.w=i.toString(10):e.w=La(i)}else{if(e.v===void 0)return"";e.w=Gt(e.v,oa)}else e.t==="d"?e.w=tt(t,Ur(e.v),oa):e.w=tt(t,e.v,oa)}catch(c){if(n.WTF)throw c}if(n.cellStyles&&r!=null)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=wn(a.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),n.WTF&&(e.s.fgColor.raw_rgb=a.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=wn(a.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),n.WTF&&(e.s.bgColor.raw_rgb=a.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(c){if(n.WTF&&s.Fills)throw c}}}function Hd(e,t){var r=or(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=Ke(r))}var Gd=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,Vd=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,Wd=/<(?:\w:)?hyperlink [^>]*>/mg,Xd=/"(\w*:\w*)"/,Kd=/<(?:\w:)?col\b[^>]*[\/]?>/g,zd=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,Yd=/<(?:\w:)?pageMargins[^>]*\/>/g,Cc=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,Jd=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,qd=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function Zd(e,t,r,n,a,s,i){if(!e)return e;n||(n={"!id":{}});var c=t.dense?[]:{},o={s:{r:2e6,c:2e6},e:{r:0,c:0}},u="",f="",x=e.match(Vd);x?(u=e.slice(0,x.index),f=e.slice(x.index+x[0].length)):u=f=e;var d=u.match(Cc);d?Bs(d[0],c,a,r):(d=u.match(Jd))&&Qd(d[0],d[1]||"",c,a,r);var p=(u.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(p>0){var g=u.slice(p,p+50).match(Xd);g&&Hd(c,g[1])}var m=u.match(qd);m&&m[1]&&s1(m[1],a);var h=[];if(t.cellStyles){var _=u.match(Kd);_&&t1(h,_)}x&&i1(x[1],c,t,o,s,i);var k=f.match(zd);k&&(c["!autofilter"]=a1(k[0]));var v=[],R=f.match(Gd);if(R)for(p=0;p!=R.length;++p)v[p]=or(R[p].slice(R[p].indexOf('"')+1));var M=f.match(Wd);M&&e1(c,M,n);var S=f.match(Yd);if(S&&(c["!margins"]=r1(Re(S[0]))),!c["!ref"]&&o.e.c>=o.s.c&&o.e.r>=o.s.r&&(c["!ref"]=Ke(o)),t.sheetRows>0&&c["!ref"]){var w=or(c["!ref"]);t.sheetRows<=+w.e.r&&(w.e.r=t.sheetRows-1,w.e.r>o.e.r&&(w.e.r=o.e.r),w.e.r<w.s.r&&(w.s.r=w.e.r),w.e.c>o.e.c&&(w.e.c=o.e.c),w.e.c<w.s.c&&(w.s.c=w.e.c),c["!fullref"]=c["!ref"],c["!ref"]=Ke(w))}return h.length>0&&(c["!cols"]=h),v.length>0&&(c["!merges"]=v),c}function Bs(e,t,r,n){var a=Re(e);r.Sheets[n]||(r.Sheets[n]={}),a.codeName&&(r.Sheets[n].CodeName=ze(ar(a.codeName)))}function Qd(e,t,r,n,a){Bs(e.slice(0,e.indexOf(">")),r,n,a)}function e1(e,t,r){for(var n=Array.isArray(e),a=0;a!=t.length;++a){var s=Re(ar(t[a]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+ze(s.location))):(s.Target="#"+ze(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var c=or(s.ref),o=c.s.r;o<=c.e.r;++o)for(var u=c.s.c;u<=c.e.c;++u){var f=Le({c:u,r:o});n?(e[o]||(e[o]=[]),e[o][u]||(e[o][u]={t:"z",v:void 0}),e[o][u].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}function r1(e){var t={};return["left","right","top","bottom","header","footer"].forEach(function(r){e[r]&&(t[r]=parseFloat(e[r]))}),t}function t1(e,t){for(var r=!1,n=0;n!=t.length;++n){var a=Re(t[n],!0);a.hidden&&(a.hidden=cr(a.hidden));var s=parseInt(a.min,10)-1,i=parseInt(a.max,10)-1;for(a.outlineLevel&&(a.level=+a.outlineLevel||0),delete a.min,delete a.max,a.width=+a.width,!r&&a.width&&(r=!0,Ps(a.width)),ua(a);s<=i;)e[s++]=kr(a)}}function a1(e){var t={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return t}var n1=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function s1(e,t){t.Views||(t.Views=[{}]),(e.match(n1)||[]).forEach(function(r,n){var a=Re(r);t.Views[n]||(t.Views[n]={}),+a.zoomScale&&(t.Views[n].zoom=+a.zoomScale),cr(a.rightToLeft)&&(t.Views[n].RTL=!0)})}var i1=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,n=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,a=/ref=["']([^"']*)["']/,s=Ba("v"),i=Ba("f");return function(o,u,f,x,d,p){for(var g=0,m="",h=[],_=[],k=0,v=0,R=0,M="",S,w,j=0,E=0,L,G,C=0,I=0,q=Array.isArray(p.CellXf),ae,ce=[],de=[],ie=Array.isArray(u),B=[],re={},he=!1,F=!!f.sheetStubs,U=o.split(t),H=0,P=U.length;H!=P;++H){m=U[H].trim();var Q=m.length;if(Q!==0){var me=0;e:for(g=0;g<Q;++g)switch(m[g]){case">":if(m[g-1]!="/"){++g;break e}if(f&&f.cellStyles){if(w=Re(m.slice(me,g),!0),j=w.r!=null?parseInt(w.r,10):j+1,E=-1,f.sheetRows&&f.sheetRows<j)continue;re={},he=!1,w.ht&&(he=!0,re.hpt=parseFloat(w.ht),re.hpx=Ha(re.hpt)),w.hidden=="1"&&(he=!0,re.hidden=!0),w.outlineLevel!=null&&(he=!0,re.level=+w.outlineLevel),he&&(B[j-1]=re)}break;case"<":me=g;break}if(me>=g)break;if(w=Re(m.slice(me,g),!0),j=w.r!=null?parseInt(w.r,10):j+1,E=-1,!(f.sheetRows&&f.sheetRows<j)){x.s.r>j-1&&(x.s.r=j-1),x.e.r<j-1&&(x.e.r=j-1),f&&f.cellStyles&&(re={},he=!1,w.ht&&(he=!0,re.hpt=parseFloat(w.ht),re.hpx=Ha(re.hpt)),w.hidden=="1"&&(he=!0,re.hidden=!0),w.outlineLevel!=null&&(he=!0,re.level=+w.outlineLevel),he&&(B[j-1]=re)),h=m.slice(g).split(e);for(var ye=0;ye!=h.length&&h[ye].trim().charAt(0)=="<";++ye);for(h=h.slice(ye),g=0;g!=h.length;++g)if(m=h[g].trim(),m.length!==0){if(_=m.match(r),k=g,v=0,R=0,m="<c "+(m.slice(0,1)=="<"?">":"")+m,_!=null&&_.length===2){for(k=0,M=_[1],v=0;v!=M.length&&!((R=M.charCodeAt(v)-64)<1||R>26);++v)k=26*k+R;--k,E=k}else++E;for(v=0;v!=m.length&&m.charCodeAt(v)!==62;++v);if(++v,w=Re(m.slice(0,v),!0),w.r||(w.r=Le({r:j-1,c:E})),M=m.slice(v),S={t:""},(_=M.match(s))!=null&&_[1]!==""&&(S.v=ze(_[1])),f.cellFormula){if((_=M.match(i))!=null&&_[1]!==""){if(S.f=ze(ar(_[1])).replace(/\r\n/g,`
`),f.xlfn||(S.f=L0(S.f)),_[0].indexOf('t="array"')>-1)S.F=(M.match(a)||[])[1],S.F.indexOf(":")>-1&&ce.push([or(S.F),S.F]);else if(_[0].indexOf('t="shared"')>-1){G=Re(_[0]);var oe=ze(ar(_[1]));f.xlfn||(oe=L0(oe)),de[parseInt(G.si,10)]=[G,oe,w.r]}}else(_=M.match(/<f[^>]*\/>/))&&(G=Re(_[0]),de[G.si]&&(S.f=px(de[G.si][1],de[G.si][2],w.r)));var le=Mr(w.r);for(v=0;v<ce.length;++v)le.r>=ce[v][0].s.r&&le.r<=ce[v][0].e.r&&le.c>=ce[v][0].s.c&&le.c<=ce[v][0].e.c&&(S.F=ce[v][1])}if(w.t==null&&S.v===void 0)if(S.f||S.F)S.v=0,S.t="n";else if(F)S.t="z";else continue;else S.t=w.t||"n";switch(x.s.c>E&&(x.s.c=E),x.e.c<E&&(x.e.c=E),S.t){case"n":if(S.v==""||S.v==null){if(!F)continue;S.t="z"}else S.v=parseFloat(S.v);break;case"s":if(typeof S.v>"u"){if(!F)continue;S.t="z"}else L=Ia[parseInt(S.v,10)],S.v=L.t,S.r=L.r,f.cellHTML&&(S.h=L.h);break;case"str":S.t="s",S.v=S.v!=null?ar(S.v):"",f.cellHTML&&(S.h=ks(S.v));break;case"inlineStr":_=M.match(n),S.t="s",_!=null&&(L=js(_[1]))?(S.v=L.t,f.cellHTML&&(S.h=L.h)):S.v="";break;case"b":S.v=cr(S.v);break;case"d":f.cellDates?S.v=Tr(S.v,1):(S.v=Ur(Tr(S.v,1)),S.t="n");break;case"e":(!f||f.cellText!==!1)&&(S.w=S.v),S.v=qi[S.v];break}if(C=I=0,ae=null,q&&w.s!==void 0&&(ae=p.CellXf[w.s],ae!=null&&(ae.numFmtId!=null&&(C=ae.numFmtId),f.cellStyles&&ae.fillId!=null&&(I=ae.fillId))),Nc(S,C,I,f,d,p),f.cellDates&&q&&S.t=="n"&&ha(Me[C])&&(S.t="d",S.v=Sn(S.v)),w.cm&&f.xlmeta){var Ee=(f.xlmeta.Cell||[])[+w.cm-1];Ee&&Ee.type=="XLDAPR"&&(S.D=!0)}if(ie){var D=Mr(w.r);u[D.r]||(u[D.r]=[]),u[D.r][D.c]=S}else u[w.r]=S}}}}B.length>0&&(u["!rows"]=B)}}();function c1(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=n,s&7&&(r.level=s&7),s&16&&(r.hidden=!0),s&32&&(r.hpt=a/20),r}var l1=Yt;function o1(){}function f1(e,t){var r={},n=e[e.l];return++e.l,r.above=!(n&64),r.left=!(n&128),e.l+=18,r.name=Oo(e),r}function u1(e){var t=at(e);return[t]}function h1(e){var t=zt(e);return[t]}function x1(e){var t=at(e),r=e.read_shift(1);return[t,r,"b"]}function d1(e){var t=zt(e),r=e.read_shift(1);return[t,r,"b"]}function m1(e){var t=at(e),r=e.read_shift(1);return[t,r,"e"]}function g1(e){var t=zt(e),r=e.read_shift(1);return[t,r,"e"]}function p1(e){var t=at(e),r=e.read_shift(4);return[t,r,"s"]}function v1(e){var t=zt(e),r=e.read_shift(4);return[t,r,"s"]}function y1(e){var t=at(e),r=Or(e);return[t,r,"n"]}function Oc(e){var t=zt(e),r=Or(e);return[t,r,"n"]}function w1(e){var t=at(e),r=Ds(e);return[t,r,"n"]}function _1(e){var t=zt(e),r=Ds(e);return[t,r,"n"]}function E1(e){var t=at(e),r=Cs(e);return[t,r,"is"]}function T1(e){var t=at(e),r=Rr(e);return[t,r,"str"]}function b1(e){var t=zt(e),r=Rr(e);return[t,r,"str"]}function k1(e,t,r){var n=e.l+t,a=at(e);a.r=r["!row"];var s=e.read_shift(1),i=[a,s,"b"];if(r.cellFormula){e.l+=2;var c=Nn(e,n-e.l,r);i[3]=Cr(c,null,a,r.supbooks,r)}else e.l=n;return i}function S1(e,t,r){var n=e.l+t,a=at(e);a.r=r["!row"];var s=e.read_shift(1),i=[a,s,"e"];if(r.cellFormula){e.l+=2;var c=Nn(e,n-e.l,r);i[3]=Cr(c,null,a,r.supbooks,r)}else e.l=n;return i}function A1(e,t,r){var n=e.l+t,a=at(e);a.r=r["!row"];var s=Or(e),i=[a,s,"n"];if(r.cellFormula){e.l+=2;var c=Nn(e,n-e.l,r);i[3]=Cr(c,null,a,r.supbooks,r)}else e.l=n;return i}function F1(e,t,r){var n=e.l+t,a=at(e);a.r=r["!row"];var s=Rr(e),i=[a,s,"str"];if(r.cellFormula){e.l+=2;var c=Nn(e,n-e.l,r);i[3]=Cr(c,null,a,r.supbooks,r)}else e.l=n;return i}var N1=Yt;function C1(e,t){var r=e.l+t,n=Yt(e),a=Os(e),s=Rr(e),i=Rr(e),c=Rr(e);e.l=r;var o={rfx:n,relId:a,loc:s,display:c};return i&&(o.Tooltip=i),o}function O1(){}function D1(e,t,r){var n=e.l+t,a=zi(e),s=e.read_shift(1),i=[a];if(i[2]=s,r.cellFormula){var c=Ld(e,n-e.l,r);i[1]=c}else e.l=n;return i}function R1(e,t,r){var n=e.l+t,a=Yt(e),s=[a];if(r.cellFormula){var i=Md(e,n-e.l,r);s[1]=i,e.l=n}else e.l=n;return s}var I1=["left","right","top","bottom","header","footer"];function j1(e){var t={};return I1.forEach(function(r){t[r]=Or(e)}),t}function P1(e){var t=e.read_shift(2);return e.l+=28,{RTL:t&32}}function L1(){}function B1(){}function M1(e,t,r,n,a,s,i){if(!e)return e;var c=t||{};n||(n={"!id":{}});var o=c.dense?[]:{},u,f={s:{r:2e6,c:2e6},e:{r:0,c:0}},x=!1,d=!1,p,g,m,h,_,k,v,R,M,S=[];c.biff=12,c["!row"]=0;var w=0,j=!1,E=[],L={},G=c.supbooks||a.supbooks||[[]];if(G.sharedf=L,G.arrayf=E,G.SheetNames=a.SheetNames||a.Sheets.map(function(ie){return ie.name}),!c.supbooks&&(c.supbooks=G,a.Names))for(var C=0;C<a.Names.length;++C)G[0][C+1]=a.Names[C];var I=[],q=[],ae=!1;Tn[16]={n:"BrtShortReal",f:Oc};var ce;if(Et(e,function(B,re,he){if(!d)switch(he){case 148:u=B;break;case 0:p=B,c.sheetRows&&c.sheetRows<=p.r&&(d=!0),R=Sr(h=p.r),c["!row"]=p.r,(B.hidden||B.hpt||B.level!=null)&&(B.hpt&&(B.hpx=Ha(B.hpt)),q[B.r]=B);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(g={t:B[2]},B[2]){case"n":g.v=B[1];break;case"s":v=Ia[B[1]],g.v=v.t,g.r=v.r;break;case"b":g.v=!!B[1];break;case"e":g.v=B[1],c.cellText!==!1&&(g.w=Jt[g.v]);break;case"str":g.t="s",g.v=B[1];break;case"is":g.t="s",g.v=B[1].t;break}if((m=i.CellXf[B[0].iStyleRef])&&Nc(g,m.numFmtId,null,c,s,i),_=B[0].c==-1?_+1:B[0].c,c.dense?(o[h]||(o[h]=[]),o[h][_]=g):o[yr(_)+R]=g,c.cellFormula){for(j=!1,w=0;w<E.length;++w){var F=E[w];p.r>=F[0].s.r&&p.r<=F[0].e.r&&_>=F[0].s.c&&_<=F[0].e.c&&(g.F=Ke(F[0]),j=!0)}!j&&B.length>3&&(g.f=B[3])}if(f.s.r>p.r&&(f.s.r=p.r),f.s.c>_&&(f.s.c=_),f.e.r<p.r&&(f.e.r=p.r),f.e.c<_&&(f.e.c=_),c.cellDates&&m&&g.t=="n"&&ha(Me[m.numFmtId])){var U=$t(g.v);U&&(g.t="d",g.v=new Date(U.y,U.m-1,U.d,U.H,U.M,U.S,U.u))}ce&&(ce.type=="XLDAPR"&&(g.D=!0),ce=void 0);break;case 1:case 12:if(!c.sheetStubs||x)break;g={t:"z",v:void 0},_=B[0].c==-1?_+1:B[0].c,c.dense?(o[h]||(o[h]=[]),o[h][_]=g):o[yr(_)+R]=g,f.s.r>p.r&&(f.s.r=p.r),f.s.c>_&&(f.s.c=_),f.e.r<p.r&&(f.e.r=p.r),f.e.c<_&&(f.e.c=_),ce&&(ce.type=="XLDAPR"&&(g.D=!0),ce=void 0);break;case 176:S.push(B);break;case 49:ce=((c.xlmeta||{}).Cell||[])[B-1];break;case 494:var H=n["!id"][B.relId];for(H?(B.Target=H.Target,B.loc&&(B.Target+="#"+B.loc),B.Rel=H):B.relId==""&&(B.Target="#"+B.loc),h=B.rfx.s.r;h<=B.rfx.e.r;++h)for(_=B.rfx.s.c;_<=B.rfx.e.c;++_)c.dense?(o[h]||(o[h]=[]),o[h][_]||(o[h][_]={t:"z",v:void 0}),o[h][_].l=B):(k=Le({c:_,r:h}),o[k]||(o[k]={t:"z",v:void 0}),o[k].l=B);break;case 426:if(!c.cellFormula)break;E.push(B),M=c.dense?o[h][_]:o[yr(_)+R],M.f=Cr(B[1],f,{r:p.r,c:_},G,c),M.F=Ke(B[0]);break;case 427:if(!c.cellFormula)break;L[Le(B[0].s)]=B[1],M=c.dense?o[h][_]:o[yr(_)+R],M.f=Cr(B[1],f,{r:p.r,c:_},G,c);break;case 60:if(!c.cellStyles)break;for(;B.e>=B.s;)I[B.e--]={width:B.w/256,hidden:!!(B.flags&1),level:B.level},ae||(ae=!0,Ps(B.w/256)),ua(I[B.e+1]);break;case 161:o["!autofilter"]={ref:Ke(B)};break;case 476:o["!margins"]=B;break;case 147:a.Sheets[r]||(a.Sheets[r]={}),B.name&&(a.Sheets[r].CodeName=B.name),(B.above||B.left)&&(o["!outline"]={above:B.above,left:B.left});break;case 137:a.Views||(a.Views=[{}]),a.Views[0]||(a.Views[0]={}),B.RTL&&(a.Views[0].RTL=!0);break;case 485:break;case 64:case 1053:break;case 151:break;case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:x=!0;break;case 36:x=!1;break;case 37:x=!0;break;case 38:x=!1;break;default:if(!re.T){if(!x||c.WTF)throw new Error("Unexpected record 0x"+he.toString(16))}}},c),delete c.supbooks,delete c["!row"],!o["!ref"]&&(f.s.r<2e6||u&&(u.e.r>0||u.e.c>0||u.s.r>0||u.s.c>0))&&(o["!ref"]=Ke(u||f)),c.sheetRows&&o["!ref"]){var de=or(o["!ref"]);c.sheetRows<=+de.e.r&&(de.e.r=c.sheetRows-1,de.e.r>f.e.r&&(de.e.r=f.e.r),de.e.r<de.s.r&&(de.s.r=de.e.r),de.e.c>f.e.c&&(de.e.c=f.e.c),de.e.c<de.s.c&&(de.s.c=de.e.c),o["!fullref"]=o["!ref"],o["!ref"]=Ke(de))}return S.length>0&&(o["!merges"]=S),I.length>0&&(o["!cols"]=I),q.length>0&&(o["!rows"]=q),o}function $1(e){var t=[],r=e.match(/^<c:numCache>/),n;(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(s){var i=s.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);i&&(t[+i[1]]=r?+i[2]:i[2])});var a=ze((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(s){n=s.replace(/<.*?>/g,"")}),[t,a,n]}function U1(e,t,r,n,a,s){var i=s||{"!type":"chart"};if(!e)return s;var c=0,o=0,u="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(x){var d=$1(x);f.s.r=f.s.c=0,f.e.c=c,u=yr(c),d[0].forEach(function(p,g){i[u+Sr(g)]={t:"n",v:p,z:d[1]},o=g}),f.e.r<o&&(f.e.r=o),++c}),c>0&&(i["!ref"]=Ke(f)),i}function H1(e,t,r,n,a){if(!e)return e;n||(n={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i,c=e.match(Cc);return c&&Bs(c[0],s,a,r),(i=e.match(/drawing r:id="(.*?)"/))&&(s["!rel"]=i[1]),n["!id"][s["!rel"]]&&(s["!drawel"]=n["!id"][s["!rel"]]),s}function G1(e,t){e.l+=10;var r=Rr(e);return{name:r}}function V1(e,t,r,n,a){if(!e)return e;n||(n={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=!1;return Et(e,function(o,u,f){switch(f){case 550:s["!rel"]=o;break;case 651:a.Sheets[r]||(a.Sheets[r]={}),o.name&&(a.Sheets[r].CodeName=o.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:break;case 38:break;default:if(!(u.T>0)){if(!(u.T<0)){if(!i||t.WTF)throw new Error("Unexpected record 0x"+f.toString(16))}}}},t),n["!id"][s["!rel"]]&&(s["!drawel"]=n["!id"][s["!rel"]]),s}var Dc=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],W1=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],X1=[],K1=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function H0(e,t){for(var r=0;r!=e.length;++r)for(var n=e[r],a=0;a!=t.length;++a){var s=t[a];if(n[s[0]]==null)n[s[0]]=s[1];else switch(s[2]){case"bool":typeof n[s[0]]=="string"&&(n[s[0]]=cr(n[s[0]]));break;case"int":typeof n[s[0]]=="string"&&(n[s[0]]=parseInt(n[s[0]],10));break}}}function G0(e,t){for(var r=0;r!=t.length;++r){var n=t[r];if(e[n[0]]==null)e[n[0]]=n[1];else switch(n[2]){case"bool":typeof e[n[0]]=="string"&&(e[n[0]]=cr(e[n[0]]));break;case"int":typeof e[n[0]]=="string"&&(e[n[0]]=parseInt(e[n[0]],10));break}}}function Rc(e){G0(e.WBProps,Dc),G0(e.CalcPr,K1),H0(e.WBView,W1),H0(e.Sheets,X1),oa.date1904=cr(e.WBProps.date1904)}var z1="][*?/\\".split("");function Y1(e,t){if(e.length>31)throw new Error("Sheet names cannot exceed 31 chars");var r=!0;return z1.forEach(function(n){if(e.indexOf(n)!=-1)throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}),r}var J1=/<\w+:workbook/;function q1(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},n=!1,a="xmlns",s={},i=0;if(e.replace(jr,function(o,u){var f=Re(o);switch(xt(f[0])){case"<?xml":break;case"<workbook":o.match(J1)&&(a="xmlns"+o.match(/<(\w+):/)[1]),r.xmlns=f[a];break;case"</workbook>":break;case"<fileVersion":delete f[0],r.AppVersion=f;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":Dc.forEach(function(x){if(f[x[0]]!=null)switch(x[2]){case"bool":r.WBProps[x[0]]=cr(f[x[0]]);break;case"int":r.WBProps[x[0]]=parseInt(f[x[0]],10);break;default:r.WBProps[x[0]]=f[x[0]]}}),f.codeName&&(r.WBProps.CodeName=ar(f.codeName));break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete f[0],r.WBView.push(f);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(f.state){case"hidden":f.Hidden=1;break;case"veryHidden":f.Hidden=2;break;default:f.Hidden=0}delete f.state,f.name=ze(ar(f.name)),delete f[0],r.Sheets.push(f);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":case"<definedNames":n=!0;break;case"</definedNames>":n=!1;break;case"<definedName":s={},s.Name=ar(f.name),f.comment&&(s.Comment=f.comment),f.localSheetId&&(s.Sheet=+f.localSheetId),cr(f.hidden||"0")&&(s.Hidden=!0),i=u+o.length;break;case"</definedName>":s.Ref=ze(ar(e.slice(i,u))),r.Names.push(s);break;case"<definedName/>":break;case"<calcPr":delete f[0],r.CalcPr=f;break;case"<calcPr/>":delete f[0],r.CalcPr=f;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":n=!0;break;case"</AlternateContent>":n=!1;break;case"<revisionPtr":break;default:if(!n&&t.WTF)throw new Error("unrecognized "+f[0]+" in workbook")}return o}),xo.indexOf(r.xmlns)===-1)throw new Error("Unknown Namespace: "+r.xmlns);return Rc(r),r}function Z1(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=ns(e),r.name=Rr(e),r}function Q1(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?Rr(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(n&65536),r.backupFile=!!(n&64),r.checkCompatibility=!!(n&4096),r.date1904=!!(n&1),r.filterPrivacy=!!(n&8),r.hidePivotFieldList=!!(n&1024),r.promptedSolutions=!!(n&16),r.publishItems=!!(n&2048),r.refreshAllConnections=!!(n&262144),r.saveExternalLinkValues=!!(n&128),r.showBorderUnselectedTables=!!(n&4),r.showInkAnnotation=!!(n&32),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(n&32768),r.updateLinks=["userSet","never","always"][n>>8&3],r}function em(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function rm(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),s=Do(e),i=Bd(e,0,r),c=Os(e);e.l=n;var o={Name:s,Ptg:i};return a<268435455&&(o.Sheet=a),c&&(o.Comment=c),o}function tm(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},n=[],a=!1;t||(t={}),t.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],Tn[16]={n:"BrtFRTArchID$",f:em},Et(e,function(o,u,f){switch(f){case 156:i.SheetNames.push(o.name),r.Sheets.push(o);break;case 153:r.WBProps=o;break;case 39:o.Sheet!=null&&(t.SID=o.Sheet),o.Ref=Cr(o.Ptg,null,null,i,t),delete t.SID,delete o.Ptg,s.push(o);break;case 1036:break;case 357:case 358:case 355:case 667:i[0].length?i.push([f,o]):i[0]=[f,o],i[i.length-1].XTI=[];break;case 362:i.length===0&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(o),i.XTI=i.XTI.concat(o);break;case 361:break;case 2071:case 158:case 143:case 664:case 353:break;case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:n.push(f),a=!0;break;case 36:n.pop(),a=!1;break;case 37:n.push(f),a=!0;break;case 38:n.pop(),a=!1;break;case 16:break;default:if(!u.T){if(!a||t.WTF&&n[n.length-1]!=37&&n[n.length-1]!=35)throw new Error("Unexpected record 0x"+f.toString(16))}}},t),Rc(r),r.Names=s,r.supbooks=i,r}function am(e,t,r){return t.slice(-4)===".bin"?tm(e,r):q1(e,r)}function nm(e,t,r,n,a,s,i,c){return t.slice(-4)===".bin"?M1(e,n,r,a,s,i,c):Zd(e,n,r,a,s,i,c)}function sm(e,t,r,n,a,s,i,c){return t.slice(-4)===".bin"?V1(e,n,r,a,s):H1(e,n,r,a,s)}function im(e,t,r,n,a,s,i,c){return t.slice(-4)===".bin"?dx():mx()}function cm(e,t,r,n,a,s,i,c){return t.slice(-4)===".bin"?hx():xx()}function lm(e,t,r,n){return t.slice(-4)===".bin"?Nh(e,r,n):Th(e,r,n)}function om(e,t,r){return yc(e,r)}function fm(e,t,r){return t.slice(-4)===".bin"?Wu(e,r):Gu(e,r)}function um(e,t,r){return t.slice(-4)===".bin"?ox(e,r):nx(e,r)}function hm(e,t,r){return t.slice(-4)===".bin"?rx(e):Qh(e)}function xm(e,t,r,n){return r.slice(-4)===".bin"?tx(e,t,r,n):void 0}function dm(e,t,r){return t.slice(-4)===".bin"?qh(e,t,r):Zh(e,t,r)}var Ic=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,jc=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function nt(e,t){var r=e.split(/\s+/),n=[];if(n[0]=r[0],r.length===1)return n;var a=e.match(Ic),s,i,c,o;if(a)for(o=0;o!=a.length;++o)s=a[o].match(jc),(i=s[1].indexOf(":"))===-1?n[s[1]]=s[2].slice(1,s[2].length-1):(s[1].slice(0,6)==="xmlns:"?c="xmlns"+s[1].slice(6):c=s[1].slice(i+1),n[c]=s[2].slice(1,s[2].length-1));return n}function mm(e){var t=e.split(/\s+/),r={};if(t.length===1)return r;var n=e.match(Ic),a,s,i,c;if(n)for(c=0;c!=n.length;++c)a=n[c].match(jc),(s=a[1].indexOf(":"))===-1?r[a[1]]=a[2].slice(1,a[2].length-1):(a[1].slice(0,6)==="xmlns:"?i="xmlns"+a[1].slice(6):i=a[1].slice(s+1),r[i]=a[2].slice(1,a[2].length-1));return r}var Pa;function gm(e,t){var r=Pa[e]||ze(e);return r==="General"?Gt(t):tt(r,t)}function pm(e,t,r,n){var a=n;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":a=cr(n);break;case"i2":case"int":a=parseInt(n,10);break;case"r4":case"float":a=parseFloat(n);break;case"date":case"dateTime.tz":a=Tr(n);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[ze(t)]=a}function vm(e,t,r){if(e.t!=="z"){if(!r||r.cellText!==!1)try{e.t==="e"?e.w=e.w||Jt[e.v]:t==="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=La(e.v):e.w=Gt(e.v):e.w=gm(t||"General",e.v)}catch(s){if(r.WTF)throw s}try{var n=Pa[t]||t||"General";if(r.cellNF&&(e.z=n),r.cellDates&&e.t=="n"&&ha(n)){var a=$t(e.v);a&&(e.t="d",e.v=new Date(a.y,a.m-1,a.d,a.H,a.M,a.S,a.u))}}catch(s){if(r.WTF)throw s}}}function ym(e,t,r){if(r.cellStyles&&t.Interior){var n=t.Interior;n.Pattern&&(n.patternType=ph[n.Pattern]||n.Pattern)}e[t.ID]=t}function wm(e,t,r,n,a,s,i,c,o,u){var f="General",x=n.StyleID,d={};u=u||{};var p=[],g=0;for(x===void 0&&c&&(x=c.StyleID),x===void 0&&i&&(x=i.StyleID);s[x]!==void 0&&(s[x].nf&&(f=s[x].nf),s[x].Interior&&p.push(s[x].Interior),!!s[x].Parent);)x=s[x].Parent;switch(r.Type){case"Boolean":n.t="b",n.v=cr(e);break;case"String":n.t="s",n.r=u0(ze(e)),n.v=e.indexOf("<")>-1?ze(t||e).replace(/<.*?>/g,""):n.r;break;case"DateTime":e.slice(-1)!="Z"&&(e+="Z"),n.v=(Tr(e)-new Date(Date.UTC(1899,11,30)))/(1440*60*1e3),n.v!==n.v?n.v=ze(e):n.v<60&&(n.v=n.v-1),(!f||f=="General")&&(f="yyyy-mm-dd");case"Number":n.v===void 0&&(n.v=+e),n.t||(n.t="n");break;case"Error":n.t="e",n.v=qi[e],u.cellText!==!1&&(n.w=e);break;default:e==""&&t==""?n.t="z":(n.t="s",n.v=u0(t||e));break}if(vm(n,f,u),u.cellFormula!==!1)if(n.Formula){var m=ze(n.Formula);m.charCodeAt(0)==61&&(m=m.slice(1)),n.f=la(m,a),delete n.Formula,n.ArrayRange=="RC"?n.F=la("RC:RC",a):n.ArrayRange&&(n.F=la(n.ArrayRange,a),o.push([or(n.F),n.F]))}else for(g=0;g<o.length;++g)a.r>=o[g][0].s.r&&a.r<=o[g][0].e.r&&a.c>=o[g][0].s.c&&a.c<=o[g][0].e.c&&(n.F=o[g][1]);u.cellStyles&&(p.forEach(function(h){!d.patternType&&h.patternType&&(d.patternType=h.patternType)}),n.s=d),n.StyleID!==void 0&&(n.ixfe=n.StyleID)}function _m(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,`
`).replace(/\r/g,`
`),e.v=e.w=e.ixfe=void 0}function Yn(e,t){var r=t||{};Ti();var n=Ta(Ss(e));(r.type=="binary"||r.type=="array"||r.type=="base64")&&(n=ar(n));var a=n.slice(0,1024).toLowerCase(),s=!1;if(a=a.replace(/".*?"/g,""),(a.indexOf(">")&1023)>Math.min(a.indexOf(",")&1023,a.indexOf(";")&1023)){var i=kr(r);return i.type="string",$a.to_workbook(n,i)}if(a.indexOf("<?xml")==-1&&["html","table","head","meta","script","style","div"].forEach(function(Ve){a.indexOf("<"+Ve)>=0&&(s=!0)}),s)return Cm(n,r);Pa={"General Number":"General","General Date":Me[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":Me[15],"Short Date":Me[14],"Long Time":Me[19],"Medium Time":Me[18],"Short Time":Me[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:Me[2],Standard:Me[4],Percent:Me[10],Scientific:Me[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var c,o=[],u,f={},x=[],d=r.dense?[]:{},p="",g={},m={},h=nt('<Data ss:Type="String">'),_=0,k=0,v=0,R={s:{r:2e6,c:2e6},e:{r:0,c:0}},M={},S={},w="",j=0,E=[],L={},G={},C=0,I=[],q=[],ae={},ce=[],de,ie=!1,B=[],re=[],he={},F=0,U=0,H={Sheets:[],WBProps:{date1904:!1}},P={};Ma.lastIndex=0,n=n.replace(/<!--([\s\S]*?)-->/mg,"");for(var Q="";c=Ma.exec(n);)switch(c[3]=(Q=c[3]).toLowerCase()){case"data":if(Q=="data"){if(c[1]==="/"){if((u=o.pop())[0]!==c[3])throw new Error("Bad state: "+u.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&o.push([c[3],!0]);break}if(o[o.length-1][1])break;c[1]==="/"?wm(n.slice(_,c.index),w,h,o[o.length-1][0]=="comment"?ae:g,{c:k,r:v},M,ce[k],m,B,r):(w="",h=nt(c[0]),_=c.index+c[0].length);break;case"cell":if(c[1]==="/")if(q.length>0&&(g.c=q),(!r.sheetRows||r.sheetRows>v)&&g.v!==void 0&&(r.dense?(d[v]||(d[v]=[]),d[v][k]=g):d[yr(k)+Sr(v)]=g),g.HRef&&(g.l={Target:ze(g.HRef)},g.HRefScreenTip&&(g.l.Tooltip=g.HRefScreenTip),delete g.HRef,delete g.HRefScreenTip),(g.MergeAcross||g.MergeDown)&&(F=k+(parseInt(g.MergeAcross,10)|0),U=v+(parseInt(g.MergeDown,10)|0),E.push({s:{c:k,r:v},e:{c:F,r:U}})),!r.sheetStubs)g.MergeAcross?k=F+1:++k;else if(g.MergeAcross||g.MergeDown){for(var me=k;me<=F;++me)for(var ye=v;ye<=U;++ye)(me>k||ye>v)&&(r.dense?(d[ye]||(d[ye]=[]),d[ye][me]={t:"z"}):d[yr(me)+Sr(ye)]={t:"z"});k=F+1}else++k;else g=mm(c[0]),g.Index&&(k=+g.Index-1),k<R.s.c&&(R.s.c=k),k>R.e.c&&(R.e.c=k),c[0].slice(-2)==="/>"&&++k,q=[];break;case"row":c[1]==="/"||c[0].slice(-2)==="/>"?(v<R.s.r&&(R.s.r=v),v>R.e.r&&(R.e.r=v),c[0].slice(-2)==="/>"&&(m=nt(c[0]),m.Index&&(v=+m.Index-1)),k=0,++v):(m=nt(c[0]),m.Index&&(v=+m.Index-1),he={},(m.AutoFitHeight=="0"||m.Height)&&(he.hpx=parseInt(m.Height,10),he.hpt=vc(he.hpx),re[v]=he),m.Hidden=="1"&&(he.hidden=!0,re[v]=he));break;case"worksheet":if(c[1]==="/"){if((u=o.pop())[0]!==c[3])throw new Error("Bad state: "+u.join("|"));x.push(p),R.s.r<=R.e.r&&R.s.c<=R.e.c&&(d["!ref"]=Ke(R),r.sheetRows&&r.sheetRows<=R.e.r&&(d["!fullref"]=d["!ref"],R.e.r=r.sheetRows-1,d["!ref"]=Ke(R))),E.length&&(d["!merges"]=E),ce.length>0&&(d["!cols"]=ce),re.length>0&&(d["!rows"]=re),f[p]=d}else R={s:{r:2e6,c:2e6},e:{r:0,c:0}},v=k=0,o.push([c[3],!1]),u=nt(c[0]),p=ze(u.Name),d=r.dense?[]:{},E=[],B=[],re=[],P={name:p,Hidden:0},H.Sheets.push(P);break;case"table":if(c[1]==="/"){if((u=o.pop())[0]!==c[3])throw new Error("Bad state: "+u.join("|"))}else{if(c[0].slice(-2)=="/>")break;o.push([c[3],!1]),ce=[],ie=!1}break;case"style":c[1]==="/"?ym(M,S,r):S=nt(c[0]);break;case"numberformat":S.nf=ze(nt(c[0]).Format||"General"),Pa[S.nf]&&(S.nf=Pa[S.nf]);for(var oe=0;oe!=392&&Me[oe]!=S.nf;++oe);if(oe==392){for(oe=57;oe!=392;++oe)if(Me[oe]==null){Ut(S.nf,oe);break}}break;case"column":if(o[o.length-1][0]!=="table")break;if(de=nt(c[0]),de.Hidden&&(de.hidden=!0,delete de.Hidden),de.Width&&(de.wpx=parseInt(de.Width,10)),!ie&&de.wpx>10){ie=!0,Br=gc;for(var le=0;le<ce.length;++le)ce[le]&&ua(ce[le])}ie&&ua(de),ce[de.Index-1||ce.length]=de;for(var Ee=0;Ee<+de.Span;++Ee)ce[ce.length]=kr(de);break;case"namedrange":if(c[1]==="/")break;H.Names||(H.Names=[]);var D=Re(c[0]),ke={Name:D.Name,Ref:la(D.RefersTo.slice(1),{r:0,c:0})};H.Sheets.length>0&&(ke.Sheet=H.Sheets.length-1),H.Names.push(ke);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if(c[0].slice(-2)==="/>")break;c[1]==="/"?w+=n.slice(j,c.index):j=c.index+c[0].length;break;case"interior":if(!r.cellStyles)break;S.Interior=nt(c[0]);break;case"protection":break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if(c[0].slice(-2)==="/>")break;c[1]==="/"?af(L,Q,n.slice(C,c.index)):C=c.index+c[0].length;break;case"paragraphs":break;case"styles":case"workbook":if(c[1]==="/"){if((u=o.pop())[0]!==c[3])throw new Error("Bad state: "+u.join("|"))}else o.push([c[3],!1]);break;case"comment":if(c[1]==="/"){if((u=o.pop())[0]!==c[3])throw new Error("Bad state: "+u.join("|"));_m(ae),q.push(ae)}else o.push([c[3],!1]),u=nt(c[0]),ae={a:u.Author};break;case"autofilter":if(c[1]==="/"){if((u=o.pop())[0]!==c[3])throw new Error("Bad state: "+u.join("|"))}else if(c[0].charAt(c[0].length-2)!=="/"){var Oe=nt(c[0]);d["!autofilter"]={ref:la(Oe.Range).replace(/\$/g,"")},o.push([c[3],!0])}break;case"name":break;case"datavalidation":if(c[1]==="/"){if((u=o.pop())[0]!==c[3])throw new Error("Bad state: "+u.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&o.push([c[3],!0]);break;case"pixelsperinch":break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if(c[1]==="/"){if((u=o.pop())[0]!==c[3])throw new Error("Bad state: "+u.join("|"))}else c[0].charAt(c[0].length-2)!=="/"&&o.push([c[3],!0]);break;case"null":break;default:if(o.length==0&&c[3]=="document"||o.length==0&&c[3]=="uof")return Y0(n,r);var Ie=!0;switch(o[o.length-1][0]){case"officedocumentsettings":switch(c[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:Ie=!1}break;case"componentoptions":switch(c[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:Ie=!1}break;case"excelworkbook":switch(c[3]){case"date1904":H.WBProps.date1904=!0;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:Ie=!1}break;case"workbookoptions":switch(c[3]){case"owcversion":break;case"height":break;case"width":break;default:Ie=!1}break;case"worksheetoptions":switch(c[3]){case"visible":if(c[0].slice(-2)!=="/>")if(c[1]==="/")switch(n.slice(C,c.index)){case"SheetHidden":P.Hidden=1;break;case"SheetVeryHidden":P.Hidden=2;break}else C=c.index+c[0].length;break;case"header":d["!margins"]||ja(d["!margins"]={},"xlml"),isNaN(+Re(c[0]).Margin)||(d["!margins"].header=+Re(c[0]).Margin);break;case"footer":d["!margins"]||ja(d["!margins"]={},"xlml"),isNaN(+Re(c[0]).Margin)||(d["!margins"].footer=+Re(c[0]).Margin);break;case"pagemargins":var je=Re(c[0]);d["!margins"]||ja(d["!margins"]={},"xlml"),isNaN(+je.Top)||(d["!margins"].top=+je.Top),isNaN(+je.Left)||(d["!margins"].left=+je.Left),isNaN(+je.Right)||(d["!margins"].right=+je.Right),isNaN(+je.Bottom)||(d["!margins"].bottom=+je.Bottom);break;case"displayrighttoleft":H.Views||(H.Views=[]),H.Views[0]||(H.Views[0]={}),H.Views[0].RTL=!0;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":d["!outline"]||(d["!outline"]={}),d["!outline"].above=!0;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":d["!outline"]||(d["!outline"]={}),d["!outline"].left=!0;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:Ie=!1}break;case"pivottable":case"pivotcache":switch(c[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:Ie=!1}break;case"pagebreaks":switch(c[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:Ie=!1}break;case"autofilter":switch(c[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:Ie=!1}break;case"querytable":switch(c[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:Ie=!1}break;case"datavalidation":switch(c[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:Ie=!1}break;case"sorting":case"conditionalformatting":switch(c[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:Ie=!1}break;case"mapinfo":case"schema":case"data":switch(c[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":break;case"row":break;default:Ie=!1}break;case"smarttags":break;default:Ie=!1;break}if(Ie||c[3].match(/!\[CDATA/))break;if(!o[o.length-1][1])throw"Unrecognized tag: "+c[3]+"|"+o.join("|");if(o[o.length-1][0]==="customdocumentproperties"){if(c[0].slice(-2)==="/>")break;c[1]==="/"?pm(G,Q,I,n.slice(C,c.index)):(I=c,C=c.index+c[0].length);break}if(r.WTF)throw"Unrecognized tag: "+c[3]+"|"+o.join("|")}var xe={};return!r.bookSheets&&!r.bookProps&&(xe.Sheets=f),xe.SheetNames=x,xe.Workbook=H,xe.SSF=kr(Me),xe.Props=L,xe.Custprops=G,xe}function ls(e,t){switch(Us(t=t||{}),t.type||"base64"){case"base64":return Yn(Xr(e),t);case"binary":case"buffer":case"file":return Yn(e,t);case"array":return Yn(Kt(e),t)}}function Em(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=jo(r),r.length-r.l<=4)return t;var n=r.read_shift(4);if(n==0||n>40||(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4)||(n=r.read_shift(4),n!==1907505652)||(t.UnicodeClipboardFormat=Po(r),n=r.read_shift(4),n==0||n>40))return t;r.l-=4,t.Reserved2=r.read_shift(0,"lpwstr")}var Tm=[60,1084,2066,2165,2175];function bm(e,t,r,n,a){var s=n,i=[],c=r.slice(r.l,r.l+s);if(a&&a.enc&&a.enc.insitu&&c.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:break;case 133:break;default:a.enc.insitu(c)}i.push(c),r.l+=s;for(var o=vt(r,r.l),u=os[o],f=0;u!=null&&Tm.indexOf(o)>-1;)s=vt(r,r.l+2),f=r.l+4,o==2066?f+=4:(o==2165||o==2175)&&(f+=12),c=r.slice(f,r.l+4+s),i.push(c),r.l+=4+s,u=os[o=vt(r,r.l)];var x=St(i);br(x,0);var d=0;x.lens=[];for(var p=0;p<i.length;++p)x.lens.push(d),d+=i[p].length;if(x.length<n)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+x.length+" < "+n;return t.f(x,x.length,a)}function ft(e,t,r){if(e.t!=="z"&&e.XF){var n=0;try{n=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=Me[n])}catch(s){if(t.WTF)throw s}if(!t||t.cellText!==!1)try{e.t==="e"?e.w=e.w||Jt[e.v]:n===0||n=="General"?e.t==="n"?(e.v|0)===e.v?e.w=e.v.toString(10):e.w=La(e.v):e.w=Gt(e.v):e.w=tt(n,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(s){if(t.WTF)throw s}if(t.cellDates&&n&&e.t=="n"&&ha(Me[n]||String(n))){var a=$t(e.v);a&&(e.t="d",e.v=new Date(a.y,a.m-1,a.d,a.H,a.M,a.S,a.u))}}}function on(e,t,r){return{v:e,ixfe:t,t:r}}function km(e,t){var r={opts:{}},n={},a=t.dense?[]:{},s={},i={},c=null,o=[],u="",f={},x,d="",p,g,m,h,_={},k=[],v,R,M=[],S=[],w={Sheets:[],WBProps:{date1904:!1},Views:[{}]},j={},E=function($e){return $e<8?Ht[$e]:$e<64&&S[$e-8]||Ht[$e]},L=function($e,rr,Pr){var Qe=rr.XF.data;if(!(!Qe||!Qe.patternType||!Pr||!Pr.cellStyles)){rr.s={},rr.s.patternType=Qe.patternType;var Kr;(Kr=Ua(E(Qe.icvFore)))&&(rr.s.fgColor={rgb:Kr}),(Kr=Ua(E(Qe.icvBack)))&&(rr.s.bgColor={rgb:Kr})}},G=function($e,rr,Pr){if(!(he>1)&&!(Pr.sheetRows&&$e.r>=Pr.sheetRows)){if(Pr.cellStyles&&rr.XF&&rr.XF.data&&L($e,rr,Pr),delete rr.ixfe,delete rr.XF,x=$e,d=Le($e),(!i||!i.s||!i.e)&&(i={s:{r:0,c:0},e:{r:0,c:0}}),$e.r<i.s.r&&(i.s.r=$e.r),$e.c<i.s.c&&(i.s.c=$e.c),$e.r+1>i.e.r&&(i.e.r=$e.r+1),$e.c+1>i.e.c&&(i.e.c=$e.c+1),Pr.cellFormula&&rr.f){for(var Qe=0;Qe<k.length;++Qe)if(!(k[Qe][0].s.c>$e.c||k[Qe][0].s.r>$e.r)&&!(k[Qe][0].e.c<$e.c||k[Qe][0].e.r<$e.r)){rr.F=Ke(k[Qe][0]),(k[Qe][0].s.c!=$e.c||k[Qe][0].s.r!=$e.r)&&delete rr.f,rr.f&&(rr.f=""+Cr(k[Qe][1],i,$e,B,C));break}}Pr.dense?(a[$e.r]||(a[$e.r]=[]),a[$e.r][$e.c]=rr):a[d]=rr}},C={enc:!1,sbcch:0,snames:[],sharedf:_,arrayf:k,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(C.password=t.password);var I,q=[],ae=[],ce=[],de=[],ie=!1,B=[];B.SheetNames=C.snames,B.sharedf=C.sharedf,B.arrayf=C.arrayf,B.names=[],B.XTI=[];var re=0,he=0,F=0,U=[],H=[],P;C.codepage=1200,ct(1200);for(var Q=!1;e.l<e.length-1;){var me=e.l,ye=e.read_shift(2);if(ye===0&&re===10)break;var oe=e.l===e.length?0:e.read_shift(2),le=os[ye];if(le&&le.f){if(t.bookSheets&&re===133&&ye!==133)break;if(re=ye,le.r===2||le.r==12){var Ee=e.read_shift(2);if(oe-=2,!C.enc&&Ee!==ye&&((Ee&255)<<8|Ee>>8)!==ye)throw new Error("rt mismatch: "+Ee+"!="+ye);le.r==12&&(e.l+=10,oe-=10)}var D={};if(ye===10?D=le.f(e,oe,C):D=bm(ye,le,e,oe,C),he==0&&[9,521,1033,2057].indexOf(re)===-1)continue;switch(ye){case 34:r.opts.Date1904=w.WBProps.date1904=D;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(C.enc||(e.l=0),C.enc=D,!t.password)throw new Error("File is password-protected");if(D.valid==null)throw new Error("Encryption scheme unsupported");if(!D.valid)throw new Error("Password is incorrect");break;case 92:C.lastuser=D;break;case 66:var ke=Number(D);switch(ke){case 21010:ke=1200;break;case 32768:ke=1e4;break;case 32769:ke=1252;break}ct(C.codepage=ke),Q=!0;break;case 317:C.rrtabid=D;break;case 25:C.winlocked=D;break;case 439:r.opts.RefreshAll=D;break;case 12:r.opts.CalcCount=D;break;case 16:r.opts.CalcDelta=D;break;case 17:r.opts.CalcIter=D;break;case 13:r.opts.CalcMode=D;break;case 14:r.opts.CalcPrecision=D;break;case 95:r.opts.CalcSaveRecalc=D;break;case 15:C.CalcRefMode=D;break;case 2211:r.opts.FullCalc=D;break;case 129:D.fDialog&&(a["!type"]="dialog"),D.fBelow||((a["!outline"]||(a["!outline"]={})).above=!0),D.fRight||((a["!outline"]||(a["!outline"]={})).left=!0);break;case 224:M.push(D);break;case 430:B.push([D]),B[B.length-1].XTI=[];break;case 35:case 547:B[B.length-1].push(D);break;case 24:case 536:P={Name:D.Name,Ref:Cr(D.rgce,i,null,B,C)},D.itab>0&&(P.Sheet=D.itab-1),B.names.push(P),B[0]||(B[0]=[],B[0].XTI=[]),B[B.length-1].push(D),D.Name=="_xlnm._FilterDatabase"&&D.itab>0&&D.rgce&&D.rgce[0]&&D.rgce[0][0]&&D.rgce[0][0][0]=="PtgArea3d"&&(H[D.itab-1]={ref:Ke(D.rgce[0][0][1][2])});break;case 22:C.ExternCount=D;break;case 23:B.length==0&&(B[0]=[],B[0].XTI=[]),B[B.length-1].XTI=B[B.length-1].XTI.concat(D),B.XTI=B.XTI.concat(D);break;case 2196:if(C.biff<8)break;P!=null&&(P.Comment=D[1]);break;case 18:a["!protect"]=D;break;case 19:D!==0&&C.WTF&&console.error("Password verifier: "+D);break;case 133:s[D.pos]=D,C.snames.push(D.name);break;case 10:{if(--he)break;if(i.e){if(i.e.r>0&&i.e.c>0){if(i.e.r--,i.e.c--,a["!ref"]=Ke(i),t.sheetRows&&t.sheetRows<=i.e.r){var Oe=i.e.r;i.e.r=t.sheetRows-1,a["!fullref"]=a["!ref"],a["!ref"]=Ke(i),i.e.r=Oe}i.e.r++,i.e.c++}q.length>0&&(a["!merges"]=q),ae.length>0&&(a["!objects"]=ae),ce.length>0&&(a["!cols"]=ce),de.length>0&&(a["!rows"]=de),w.Sheets.push(j)}u===""?f=a:n[u]=a,a=t.dense?[]:{}}break;case 9:case 521:case 1033:case 2057:{if(C.biff===8&&(C.biff={9:2,521:3,1033:4}[ye]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[D.BIFFVer]||8),C.biffguess=D.BIFFVer==0,D.BIFFVer==0&&D.dt==4096&&(C.biff=5,Q=!0,ct(C.codepage=28591)),C.biff==8&&D.BIFFVer==0&&D.dt==16&&(C.biff=2),he++)break;if(a=t.dense?[]:{},C.biff<8&&!Q&&(Q=!0,ct(C.codepage=t.codepage||1252)),C.biff<5||D.BIFFVer==0&&D.dt==4096){u===""&&(u="Sheet1"),i={s:{r:0,c:0},e:{r:0,c:0}};var Ie={pos:e.l-oe,name:u};s[Ie.pos]=Ie,C.snames.push(u)}else u=(s[me]||{name:""}).name;D.dt==32&&(a["!type"]="chart"),D.dt==64&&(a["!type"]="macro"),q=[],ae=[],C.arrayf=k=[],ce=[],de=[],ie=!1,j={Hidden:(s[me]||{hs:0}).hs,name:u}}break;case 515:case 3:case 2:a["!type"]=="chart"&&(t.dense?(a[D.r]||[])[D.c]:a[Le({c:D.c,r:D.r})])&&++D.c,v={ixfe:D.ixfe,XF:M[D.ixfe]||{},v:D.val,t:"n"},F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G({c:D.c,r:D.r},v,t);break;case 5:case 517:v={ixfe:D.ixfe,XF:M[D.ixfe],v:D.val,t:D.t},F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G({c:D.c,r:D.r},v,t);break;case 638:v={ixfe:D.ixfe,XF:M[D.ixfe],v:D.rknum,t:"n"},F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G({c:D.c,r:D.r},v,t);break;case 189:for(var je=D.c;je<=D.C;++je){var xe=D.rkrec[je-D.c][0];v={ixfe:xe,XF:M[xe],v:D.rkrec[je-D.c][1],t:"n"},F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G({c:je,r:D.r},v,t)}break;case 6:case 518:case 1030:{if(D.val=="String"){c=D;break}if(v=on(D.val,D.cell.ixfe,D.tt),v.XF=M[v.ixfe],t.cellFormula){var Ve=D.formula;if(Ve&&Ve[0]&&Ve[0][0]&&Ve[0][0][0]=="PtgExp"){var er=Ve[0][0][1][0],qe=Ve[0][0][1][1],te=Le({r:er,c:qe});_[te]?v.f=""+Cr(D.formula,i,D.cell,B,C):v.F=((t.dense?(a[er]||[])[qe]:a[te])||{}).F}else v.f=""+Cr(D.formula,i,D.cell,B,C)}F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G(D.cell,v,t),c=D}break;case 7:case 519:if(c)c.val=D,v=on(D,c.cell.ixfe,"s"),v.XF=M[v.ixfe],t.cellFormula&&(v.f=""+Cr(c.formula,i,c.cell,B,C)),F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G(c.cell,v,t),c=null;else throw new Error("String record expects Formula");break;case 33:case 545:{k.push(D);var ne=Le(D[0].s);if(p=t.dense?(a[D[0].s.r]||[])[D[0].s.c]:a[ne],t.cellFormula&&p){if(!c||!ne||!p)break;p.f=""+Cr(D[1],i,D[0],B,C),p.F=Ke(D[0])}}break;case 1212:{if(!t.cellFormula)break;if(d){if(!c)break;_[Le(c.cell)]=D[0],p=t.dense?(a[c.cell.r]||[])[c.cell.c]:a[Le(c.cell)],(p||{}).f=""+Cr(D[0],i,x,B,C)}}break;case 253:v=on(o[D.isst].t,D.ixfe,"s"),o[D.isst].h&&(v.h=o[D.isst].h),v.XF=M[v.ixfe],F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G({c:D.c,r:D.r},v,t);break;case 513:t.sheetStubs&&(v={ixfe:D.ixfe,XF:M[D.ixfe],t:"z"},F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G({c:D.c,r:D.r},v,t));break;case 190:if(t.sheetStubs)for(var Ne=D.c;Ne<=D.C;++Ne){var Be=D.ixfe[Ne-D.c];v={ixfe:Be,XF:M[Be],t:"z"},F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G({c:Ne,r:D.r},v,t)}break;case 214:case 516:case 4:v=on(D.val,D.ixfe,"s"),v.XF=M[v.ixfe],F>0&&(v.z=U[v.ixfe>>8&63]),ft(v,t,r.opts.Date1904),G({c:D.c,r:D.r},v,t);break;case 0:case 512:he===1&&(i=D);break;case 252:o=D;break;case 1054:if(C.biff==4){U[F++]=D[1];for(var De=0;De<F+163&&Me[De]!=D[1];++De);De>=163&&Ut(D[1],F+163)}else Ut(D[1],D[0]);break;case 30:{U[F++]=D;for(var Ge=0;Ge<F+163&&Me[Ge]!=D;++Ge);Ge>=163&&Ut(D,F+163)}break;case 229:q=q.concat(D);break;case 93:ae[D.cmo[0]]=C.lastobj=D;break;case 438:C.lastobj.TxO=D;break;case 127:C.lastobj.ImData=D;break;case 440:for(h=D[0].s.r;h<=D[0].e.r;++h)for(m=D[0].s.c;m<=D[0].e.c;++m)p=t.dense?(a[h]||[])[m]:a[Le({c:m,r:h})],p&&(p.l=D[1]);break;case 2048:for(h=D[0].s.r;h<=D[0].e.r;++h)for(m=D[0].s.c;m<=D[0].e.c;++m)p=t.dense?(a[h]||[])[m]:a[Le({c:m,r:h})],p&&p.l&&(p.l.Tooltip=D[1]);break;case 28:{if(C.biff<=5&&C.biff>=2)break;p=t.dense?(a[D[0].r]||[])[D[0].c]:a[Le(D[0])];var Ye=ae[D[2]];p||(t.dense?(a[D[0].r]||(a[D[0].r]=[]),p=a[D[0].r][D[0].c]={t:"z"}):p=a[Le(D[0])]={t:"z"},i.e.r=Math.max(i.e.r,D[0].r),i.s.r=Math.min(i.s.r,D[0].r),i.e.c=Math.max(i.e.c,D[0].c),i.s.c=Math.min(i.s.c,D[0].c)),p.c||(p.c=[]),g={a:D[1],t:Ye.TxO.t},p.c.push(g)}break;case 2173:Kh(M[D.ixfe],D.ext);break;case 125:{if(!C.cellStyles)break;for(;D.e>=D.s;)ce[D.e--]={width:D.w/256,level:D.level||0,hidden:!!(D.flags&1)},ie||(ie=!0,Ps(D.w/256)),ua(ce[D.e+1])}break;case 520:{var Ze={};D.level!=null&&(de[D.r]=Ze,Ze.level=D.level),D.hidden&&(de[D.r]=Ze,Ze.hidden=!0),D.hpt&&(de[D.r]=Ze,Ze.hpt=D.hpt,Ze.hpx=Ha(D.hpt))}break;case 38:case 39:case 40:case 41:a["!margins"]||ja(a["!margins"]={}),a["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[ye]]=D;break;case 161:a["!margins"]||ja(a["!margins"]={}),a["!margins"].header=D.header,a["!margins"].footer=D.footer;break;case 574:D.RTL&&(w.Views[0].RTL=!0);break;case 146:S=D;break;case 2198:I=D;break;case 140:R=D;break;case 442:u?j.CodeName=D||j.name:w.WBProps.CodeName=D||"ThisWorkbook";break}}else le||console.error("Missing Info for XLS Record 0x"+ye.toString(16)),e.l+=oe}return r.SheetNames=ht(s).sort(function(fr,$e){return Number(fr)-Number($e)}).map(function(fr){return s[fr].name}),t.bookSheets||(r.Sheets=n),!r.SheetNames.length&&f["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=f)):r.Preamble=f,r.Sheets&&H.forEach(function(fr,$e){r.Sheets[r.SheetNames[$e]]["!autofilter"]=fr}),r.Strings=o,r.SSF=kr(Me),C.enc&&(r.Encryption=C.enc),I&&(r.Themes=I),r.Metadata={},R!==void 0&&(r.Metadata.Country=R),B.names.length>0&&(w.Names=B.names),r.Workbook=w,r}var V0={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function Sm(e,t,r){var n=Xe.find(e,"/!DocumentSummaryInformation");if(n&&n.size>0)try{var a=A0(n,Ho,V0.DSI);for(var s in a)t[s]=a[s]}catch(u){if(r.WTF)throw u}var i=Xe.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var c=A0(i,Go,V0.SI);for(var o in c)t[o]==null&&(t[o]=c[o])}catch(u){if(r.WTF)throw u}t.HeadingPairs&&t.TitlesOfParts&&(Qi(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}function Pc(e,t){t||(t={}),Us(t),ui(),t.codepage&&ys(t.codepage);var r,n;if(e.FullPaths){if(Xe.find(e,"/encryption"))throw new Error("File is password-protected");r=Xe.find(e,"!CompObj"),n=Xe.find(e,"/Workbook")||Xe.find(e,"/Book")}else{switch(t.type){case"base64":e=it(Xr(e));break;case"binary":e=it(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e));break}br(e,0),n={content:e}}var a,s;if(r&&Em(r),t.bookProps&&!t.bookSheets)a={};else{var i=We?"buffer":"array";if(n&&n.content)a=km(n.content,t);else if((s=Xe.find(e,"PerfectOffice_MAIN"))&&s.content)a=Ra.to_workbook(s.content,(t.type=i,t));else if((s=Xe.find(e,"NativeContent_MAIN"))&&s.content)a=Ra.to_workbook(s.content,(t.type=i,t));else throw(s=Xe.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");t.bookVBA&&e.FullPaths&&Xe.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(a.vbaraw=ux(e))}var c={};return e.FullPaths&&Sm(e,c,t),a.Props=a.Custprops=c,t.bookFiles&&(a.cfb=e),a}var Tn={0:{f:c1},1:{f:u1},2:{f:w1},3:{f:m1},4:{f:x1},5:{f:y1},6:{f:T1},7:{f:p1},8:{f:F1},9:{f:A1},10:{f:k1},11:{f:S1},12:{f:h1},13:{f:_1},14:{f:g1},15:{f:d1},16:{f:Oc},17:{f:b1},18:{f:v1},19:{f:Cs},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:rm},40:{},42:{},43:{f:kh},44:{f:bh},45:{f:Sh},46:{f:Fh},47:{f:Ah},48:{},49:{f:Fo},50:{},51:{f:Yh},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:uc},62:{f:E1},63:{f:ex},64:{f:L1},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Ir,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:P1},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:f1},148:{f:l1,p:16},151:{f:O1},152:{},153:{f:Q1},154:{},155:{},156:{f:Z1},157:{},158:{},159:{T:1,f:Vu},160:{T:-1},161:{T:1,f:Yt},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:N1},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:zh},336:{T:-1},337:{f:Jh,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:ns},357:{},358:{},359:{},360:{T:1},361:{},362:{f:fc},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:D1},427:{f:R1},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:j1},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:o1},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:C1},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:ns},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:lx},633:{T:1},634:{T:-1},635:{T:1,f:cx},636:{T:-1},637:{f:Co},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:G1},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:B1},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},os={6:{f:Kn},10:{f:kt},12:{f:mr},13:{f:mr},14:{f:ur},15:{f:ur},16:{f:Or},17:{f:ur},18:{f:ur},19:{f:mr},20:{f:O0},21:{f:O0},23:{f:fc},24:{f:R0},25:{f:ur},26:{},27:{},28:{f:su},29:{},34:{f:ur},35:{f:D0},38:{f:Or},39:{f:Or},40:{f:Or},41:{f:Or},42:{f:ur},43:{f:ur},47:{f:oh},49:{f:$f},51:{f:mr},60:{},61:{f:Lf},64:{f:ur},65:{f:Mf},66:{f:mr},77:{},80:{},81:{},82:{},85:{f:mr},89:{},90:{},91:{},92:{f:Nf},93:{f:cu},94:{},95:{f:ur},96:{},97:{},99:{f:ur},125:{f:uc},128:{f:Jf},129:{f:Cf},130:{f:mr},131:{f:ur},132:{f:ur},133:{f:Of},134:{},140:{f:xu},141:{f:mr},144:{},146:{f:mu},151:{},152:{},153:{},154:{},155:{},156:{f:mr},157:{},158:{},160:{f:wu},161:{f:pu},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:Xf},190:{f:Kf},193:{f:kt},197:{},198:{},199:{},200:{},201:{},202:{f:ur},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:mr},220:{},221:{f:ur},222:{},224:{f:Yf},225:{f:Ff},226:{f:kt},227:{},229:{f:iu},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:Df},253:{f:Uf},255:{f:Rf},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:nc},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:ur},353:{f:kt},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:Zf},431:{f:ur},432:{},433:{},434:{},437:{},438:{f:fu},439:{f:ur},440:{f:uu},441:{},442:{f:Ka},443:{},444:{f:mr},445:{},446:{},448:{f:kt},449:{f:Pf,r:2},450:{f:kt},512:{f:N0},513:{f:yu},515:{f:qf},516:{f:Hf},517:{f:C0},519:{f:_u},520:{f:If},523:{},545:{f:I0},549:{f:F0},566:{},574:{f:Bf},638:{f:Wf},659:{},1048:{},1054:{f:Gf},1084:{},1212:{f:tu},2048:{f:hu},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:sn},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:kt},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:gu,r:12},2173:{f:Xh,r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:ur,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:ru,r:12},2197:{},2198:{f:$h,r:12},2199:{},2200:{},2201:{},2202:{f:au,r:12},2203:{f:kt},2204:{},2205:{},2206:{},2207:{},2211:{f:jf},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:mr},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:vu},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:du},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:N0},1:{},2:{f:ku},3:{f:bu},4:{f:Tu},5:{f:C0},7:{f:Su},8:{},9:{f:sn},11:{},22:{f:mr},30:{f:Vf},31:{},32:{},33:{f:I0},36:{},37:{f:F0},50:{f:Au},62:{},52:{},67:{},68:{f:mr},69:{},86:{},126:{},127:{f:Eu},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:Fu},223:{},234:{},354:{},421:{},518:{f:Kn},521:{f:sn},536:{f:R0},547:{f:D0},561:{},579:{},1030:{f:Kn},1033:{f:sn},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function st(e,t,r,n){var a=t;if(!isNaN(a)){var s=(r||[]).length||0,i=e.next(4);i.write_shift(2,a),i.write_shift(2,s),s>0&&Wi(r)&&e.push(r)}}function W0(e,t){var r=t||{},n=r.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var a=e.match(/<table/i);if(!a)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=a.index,c=s&&s.index||e.length,o=Yl(e.slice(i,c),/(:?<tr[^>]*>)/i,"<tr>"),u=-1,f=0,x=0,d=0,p={s:{r:1e7,c:1e7},e:{r:0,c:0}},g=[];for(i=0;i<o.length;++i){var m=o[i].trim(),h=m.slice(0,3).toLowerCase();if(h=="<tr"){if(++u,r.sheetRows&&r.sheetRows<=u){--u;break}f=0;continue}if(!(h!="<td"&&h!="<th")){var _=m.split(/<\/t[dh]>/i);for(c=0;c<_.length;++c){var k=_[c].trim();if(k.match(/<t[dh]/i)){for(var v=k,R=0;v.charAt(0)=="<"&&(R=v.indexOf(">"))>-1;)v=v.slice(R+1);for(var M=0;M<g.length;++M){var S=g[M];S.s.c==f&&S.s.r<u&&u<=S.e.r&&(f=S.e.c+1,M=-1)}var w=Re(k.slice(0,k.indexOf(">")));d=w.colspan?+w.colspan:1,((x=+w.rowspan)>1||d>1)&&g.push({s:{r:u,c:f},e:{r:u+(x||1)-1,c:f+d-1}});var j=w.t||w["data-t"]||"";if(!v.length){f+=d;continue}if(v=Di(v),p.s.r>u&&(p.s.r=u),p.e.r<u&&(p.e.r=u),p.s.c>f&&(p.s.c=f),p.e.c<f&&(p.e.c=f),!v.length){f+=d;continue}var E={t:"s",v};r.raw||!v.trim().length||j=="s"||(v==="TRUE"?E={t:"b",v:!0}:v==="FALSE"?E={t:"b",v:!1}:isNaN(ot(v))?isNaN(fa(v).getDate())||(E={t:"d",v:Tr(v)},r.cellDates||(E={t:"n",v:Ur(E.v)}),E.z=r.dateNF||Me[14]):E={t:"n",v:ot(v)}),r.dense?(n[u]||(n[u]=[]),n[u][f]=E):n[Le({r:u,c:f})]=E,f+=d}}}}return n["!ref"]=Ke(p),g.length&&(n["!merges"]=g),n}function Am(e,t,r,n){for(var a=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var c=0,o=0,u=0;u<a.length;++u)if(!(a[u].s.r>r||a[u].s.c>i)&&!(a[u].e.r<r||a[u].e.c<i)){if(a[u].s.r<r||a[u].s.c<i){c=-1;break}c=a[u].e.r-a[u].s.r+1,o=a[u].e.c-a[u].s.c+1;break}if(!(c<0)){var f=Le({r,c:i}),x=n.dense?(e[r]||[])[i]:e[f],d=x&&x.v!=null&&(x.h||ks(x.w||(_t(x),x.w)||""))||"",p={};c>1&&(p.rowspan=c),o>1&&(p.colspan=o),n.editable?d='<span contenteditable="true">'+d+"</span>":x&&(p["data-t"]=x&&x.t||"z",x.v!=null&&(p["data-v"]=x.v),x.z!=null&&(p["data-z"]=x.z),x.l&&(x.l.Target||"#").charAt(0)!="#"&&(d='<a href="'+x.l.Target+'">'+d+"</a>")),p.id=(n.id||"sjs")+"-"+f,s.push(uo("td",d,p))}}var g="<tr>";return g+s.join("")+"</tr>"}var Fm='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Nm="</body></html>";function Cm(e,t){var r=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!r||r.length==0)throw new Error("Invalid HTML: could not find <table>");if(r.length==1)return Dt(W0(r[0],t),t);var n=Gs();return r.forEach(function(a,s){Vs(n,W0(a,t),"Sheet"+(s+1))}),n}function Om(e,t,r){var n=[];return n.join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Dm(e,t){var r=t||{},n=r.header!=null?r.header:Fm,a=r.footer!=null?r.footer:Nm,s=[n],i=xa(e["!ref"]);r.dense=Array.isArray(e),s.push(Om(e,i,r));for(var c=i.s.r;c<=i.e.r;++c)s.push(Am(e,i,c,r));return s.push("</table>"+a),s.join("")}function Lc(e,t,r){var n=r||{},a=0,s=0;if(n.origin!=null)if(typeof n.origin=="number")a=n.origin;else{var i=typeof n.origin=="string"?Mr(n.origin):n.origin;a=i.r,s=i.c}var c=t.getElementsByTagName("tr"),o=Math.min(n.sheetRows||1e7,c.length),u={s:{r:0,c:0},e:{r:a,c:s}};if(e["!ref"]){var f=xa(e["!ref"]);u.s.r=Math.min(u.s.r,f.s.r),u.s.c=Math.min(u.s.c,f.s.c),u.e.r=Math.max(u.e.r,f.e.r),u.e.c=Math.max(u.e.c,f.e.c),a==-1&&(u.e.r=a=f.e.r+1)}var x=[],d=0,p=e["!rows"]||(e["!rows"]=[]),g=0,m=0,h=0,_=0,k=0,v=0;for(e["!cols"]||(e["!cols"]=[]);g<c.length&&m<o;++g){var R=c[g];if(X0(R)){if(n.display)continue;p[m]={hidden:!0}}var M=R.children;for(h=_=0;h<M.length;++h){var S=M[h];if(!(n.display&&X0(S))){var w=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):Di(S.innerHTML),j=S.getAttribute("data-z")||S.getAttribute("z");for(d=0;d<x.length;++d){var E=x[d];E.s.c==_+s&&E.s.r<m+a&&m+a<=E.e.r&&(_=E.e.c+1-s,d=-1)}v=+S.getAttribute("colspan")||1,((k=+S.getAttribute("rowspan")||1)>1||v>1)&&x.push({s:{r:m+a,c:_+s},e:{r:m+a+(k||1)-1,c:_+s+(v||1)-1}});var L={t:"s",v:w},G=S.getAttribute("data-t")||S.getAttribute("t")||"";w!=null&&(w.length==0?L.t=G||"z":n.raw||w.trim().length==0||G=="s"||(w==="TRUE"?L={t:"b",v:!0}:w==="FALSE"?L={t:"b",v:!1}:isNaN(ot(w))?isNaN(fa(w).getDate())||(L={t:"d",v:Tr(w)},n.cellDates||(L={t:"n",v:Ur(L.v)}),L.z=n.dateNF||Me[14]):L={t:"n",v:ot(w)})),L.z===void 0&&j!=null&&(L.z=j);var C="",I=S.getElementsByTagName("A");if(I&&I.length)for(var q=0;q<I.length&&!(I[q].hasAttribute("href")&&(C=I[q].getAttribute("href"),C.charAt(0)!="#"));++q);C&&C.charAt(0)!="#"&&(L.l={Target:C}),n.dense?(e[m+a]||(e[m+a]=[]),e[m+a][_+s]=L):e[Le({c:_+s,r:m+a})]=L,u.e.c<_+s&&(u.e.c=_+s),_+=v}}++m}return x.length&&(e["!merges"]=(e["!merges"]||[]).concat(x)),u.e.r=Math.max(u.e.r,m-1+a),e["!ref"]=Ke(u),m>=o&&(e["!fullref"]=Ke((u.e.r=c.length-g+m-1+a,u))),e}function Bc(e,t){var r=t||{},n=r.dense?[]:{};return Lc(n,e,t)}function Rm(e,t){return Dt(Bc(e,t),t)}function X0(e){var t="",r=Im(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),t==="none"}function Im(e){return e.ownerDocument.defaultView&&typeof e.ownerDocument.defaultView.getComputedStyle=="function"?e.ownerDocument.defaultView.getComputedStyle:typeof getComputedStyle=="function"?getComputedStyle:null}function jm(e){var t=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(n,a){return Array(parseInt(a,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,`
`),r=ze(t.replace(/<[^>]*>/g,""));return[r]}var K0={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function Mc(e,t){var r=t||{},n=Ss(e),a=[],s,i,c={name:""},o="",u=0,f,x,d={},p=[],g=r.dense?[]:{},m,h,_={value:""},k="",v=0,R=[],M=-1,S=-1,w={s:{r:1e6,c:1e7},e:{r:0,c:0}},j=0,E={},L=[],G={},C=0,I=0,q=[],ae=1,ce=1,de=[],ie={Names:[]},B={},re=["",""],he=[],F={},U="",H=0,P=!1,Q=!1,me=0;for(Ma.lastIndex=0,n=n.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");m=Ma.exec(n);)switch(m[3]=m[3].replace(/_.*$/,"")){case"table":case"工作表":m[1]==="/"?(w.e.c>=w.s.c&&w.e.r>=w.s.r?g["!ref"]=Ke(w):g["!ref"]="A1:A1",r.sheetRows>0&&r.sheetRows<=w.e.r&&(g["!fullref"]=g["!ref"],w.e.r=r.sheetRows-1,g["!ref"]=Ke(w)),L.length&&(g["!merges"]=L),q.length&&(g["!rows"]=q),f.name=f.名称||f.name,typeof JSON<"u"&&JSON.stringify(f),p.push(f.name),d[f.name]=g,Q=!1):m[0].charAt(m[0].length-2)!=="/"&&(f=Re(m[0],!1),M=S=-1,w.s.r=w.s.c=1e7,w.e.r=w.e.c=0,g=r.dense?[]:{},L=[],q=[],Q=!0);break;case"table-row-group":m[1]==="/"?--j:++j;break;case"table-row":case"行":if(m[1]==="/"){M+=ae,ae=1;break}if(x=Re(m[0],!1),x.行号?M=x.行号-1:M==-1&&(M=0),ae=+x["number-rows-repeated"]||1,ae<10)for(me=0;me<ae;++me)j>0&&(q[M+me]={level:j});S=-1;break;case"covered-table-cell":m[1]!=="/"&&++S,r.sheetStubs&&(r.dense?(g[M]||(g[M]=[]),g[M][S]={t:"z"}):g[Le({r:M,c:S})]={t:"z"}),k="",R=[];break;case"table-cell":case"数据":if(m[0].charAt(m[0].length-2)==="/")++S,_=Re(m[0],!1),ce=parseInt(_["number-columns-repeated"]||"1",10),h={t:"z",v:null},_.formula&&r.cellFormula!=!1&&(h.f=U0(ze(_.formula))),(_.数据类型||_["value-type"])=="string"&&(h.t="s",h.v=ze(_["string-value"]||""),r.dense?(g[M]||(g[M]=[]),g[M][S]=h):g[Le({r:M,c:S})]=h),S+=ce-1;else if(m[1]!=="/"){++S,k="",v=0,R=[],ce=1;var ye=ae?M+ae-1:M;if(S>w.e.c&&(w.e.c=S),S<w.s.c&&(w.s.c=S),M<w.s.r&&(w.s.r=M),ye>w.e.r&&(w.e.r=ye),_=Re(m[0],!1),he=[],F={},h={t:_.数据类型||_["value-type"],v:null},r.cellFormula)if(_.formula&&(_.formula=ze(_.formula)),_["number-matrix-columns-spanned"]&&_["number-matrix-rows-spanned"]&&(C=parseInt(_["number-matrix-rows-spanned"],10)||0,I=parseInt(_["number-matrix-columns-spanned"],10)||0,G={s:{r:M,c:S},e:{r:M+C-1,c:S+I-1}},h.F=Ke(G),de.push([G,h.F])),_.formula)h.f=U0(_.formula);else for(me=0;me<de.length;++me)M>=de[me][0].s.r&&M<=de[me][0].e.r&&S>=de[me][0].s.c&&S<=de[me][0].e.c&&(h.F=de[me][1]);switch((_["number-columns-spanned"]||_["number-rows-spanned"])&&(C=parseInt(_["number-rows-spanned"],10)||0,I=parseInt(_["number-columns-spanned"],10)||0,G={s:{r:M,c:S},e:{r:M+C-1,c:S+I-1}},L.push(G)),_["number-columns-repeated"]&&(ce=parseInt(_["number-columns-repeated"],10)),h.t){case"boolean":h.t="b",h.v=cr(_["boolean-value"]);break;case"float":h.t="n",h.v=parseFloat(_.value);break;case"percentage":h.t="n",h.v=parseFloat(_.value);break;case"currency":h.t="n",h.v=parseFloat(_.value);break;case"date":h.t="d",h.v=Tr(_["date-value"]),r.cellDates||(h.t="n",h.v=Ur(h.v)),h.z="m/d/yy";break;case"time":h.t="n",h.v=Xl(_["time-value"])/86400,r.cellDates&&(h.t="d",h.v=Sn(h.v)),h.z="HH:MM:SS";break;case"number":h.t="n",h.v=parseFloat(_.数据数值);break;default:if(h.t==="string"||h.t==="text"||!h.t)h.t="s",_["string-value"]!=null&&(k=ze(_["string-value"]),R=[]);else throw new Error("Unsupported value type "+h.t)}}else{if(P=!1,h.t==="s"&&(h.v=k||"",R.length&&(h.R=R),P=v==0),B.Target&&(h.l=B),he.length>0&&(h.c=he,he=[]),k&&r.cellText!==!1&&(h.w=k),P&&(h.t="z",delete h.v),(!P||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=M))for(var oe=0;oe<ae;++oe){if(ce=parseInt(_["number-columns-repeated"]||"1",10),r.dense)for(g[M+oe]||(g[M+oe]=[]),g[M+oe][S]=oe==0?h:kr(h);--ce>0;)g[M+oe][S+ce]=kr(h);else for(g[Le({r:M+oe,c:S})]=h;--ce>0;)g[Le({r:M+oe,c:S+ce})]=kr(h);w.e.c<=S&&(w.e.c=S)}ce=parseInt(_["number-columns-repeated"]||"1",10),S+=ce-1,ce=0,h={},k="",R=[]}B={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if(m[1]==="/"){if((s=a.pop())[0]!==m[3])throw"Bad state: "+s}else m[0].charAt(m[0].length-2)!=="/"&&a.push([m[3],!0]);break;case"annotation":if(m[1]==="/"){if((s=a.pop())[0]!==m[3])throw"Bad state: "+s;F.t=k,R.length&&(F.R=R),F.a=U,he.push(F)}else m[0].charAt(m[0].length-2)!=="/"&&a.push([m[3],!1]);U="",H=0,k="",v=0,R=[];break;case"creator":m[1]==="/"?U=n.slice(H,m.index):H=m.index+m[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if(m[1]==="/"){if((s=a.pop())[0]!==m[3])throw"Bad state: "+s}else m[0].charAt(m[0].length-2)!=="/"&&a.push([m[3],!1]);k="",v=0,R=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if(m[1]==="/"){if(E[c.name]=o,(s=a.pop())[0]!==m[3])throw"Bad state: "+s}else m[0].charAt(m[0].length-2)!=="/"&&(o="",c=Re(m[0],!1),a.push([m[3],!0]));break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(a[a.length-1][0]){case"time-style":case"date-style":i=Re(m[0],!1),o+=K0[m[3]][i.style==="long"?1:0];break}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(a[a.length-1][0]){case"time-style":case"date-style":i=Re(m[0],!1),o+=K0[m[3]][i.style==="long"?1:0];break}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if(m[0].slice(-2)==="/>")break;if(m[1]==="/")switch(a[a.length-1][0]){case"number-style":case"date-style":case"time-style":o+=n.slice(u,m.index);break}else u=m.index+m[0].length;break;case"named-range":i=Re(m[0],!1),re=zn(i["cell-range-address"]);var le={Name:i.name,Ref:re[0]+"!"+re[1]};Q&&(le.Sheet=p.length),ie.Names.push(le);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":case"文本串":if(["master-styles"].indexOf(a[a.length-1][0])>-1)break;if(m[1]==="/"&&(!_||!_["string-value"])){var Ee=jm(n.slice(v,m.index));k=(k.length>0?k+`
`:"")+Ee[0]}else Re(m[0],!1),v=m.index+m[0].length;break;case"s":break;case"database-range":if(m[1]==="/")break;try{re=zn(Re(m[0])["target-range-address"]),d[re[0]]["!autofilter"]={ref:re[1]}}catch{}break;case"date":break;case"object":break;case"title":case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":break;case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":break;case"properties":break;case"property":break;case"a":if(m[1]!=="/"){if(B=Re(m[0],!1),!B.href)break;B.Target=ze(B.href),delete B.href,B.Target.charAt(0)=="#"&&B.Target.indexOf(".")>-1?(re=zn(B.Target.slice(1)),B.Target="#"+re[0]+"!"+re[1]):B.Target.match(/^\.\.[\\\/]/)&&(B.Target=B.Target.slice(3))}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(m[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw new Error(m)}}var D={Sheets:d,SheetNames:p,Workbook:ie};return r.bookSheets&&delete D.Sheets,D}function z0(e,t){t=t||{},et(e,"META-INF/manifest.xml")&&Jo(dr(e,"META-INF/manifest.xml"),t);var r=Wr(e,"content.xml");if(!r)throw new Error("Missing content.xml in ODS / UOF file");var n=Mc(ar(r),t);return et(e,"meta.xml")&&(n.Props=Zi(dr(e,"meta.xml"))),n}function Y0(e,t){return Mc(e,t)}/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Ms(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function fs(e){return typeof TextDecoder<"u"?new TextDecoder().decode(e):ar(Kt(e))}function us(e){var t=e.reduce(function(a,s){return a+s.length},0),r=new Uint8Array(t),n=0;return e.forEach(function(a){r.set(a,n),n+=a.length}),r}function J0(e){return e-=e>>1&1431655765,e=(e&858993459)+(e>>2&858993459),(e+(e>>4)&252645135)*16843009>>>24}function Pm(e,t){for(var r=(e[t+15]&127)<<7|e[t+14]>>1,n=e[t+14]&1,a=t+13;a>=t;--a)n=n*256+e[a];return(e[t+15]&128?-n:n)*Math.pow(10,r-6176)}function Ga(e,t){var r=t?t[0]:0,n=e[r]&127;e:if(e[r++]>=128&&(n|=(e[r]&127)<<7,e[r++]<128||(n|=(e[r]&127)<<14,e[r++]<128)||(n|=(e[r]&127)<<21,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,28),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,35),++r,e[r++]<128)||(n+=(e[r]&127)*Math.pow(2,42),++r,e[r++]<128)))break e;return t&&(t[0]=r),n}function wr(e){var t=0,r=e[t]&127;e:if(e[t++]>=128){if(r|=(e[t]&127)<<7,e[t++]<128||(r|=(e[t]&127)<<14,e[t++]<128)||(r|=(e[t]&127)<<21,e[t++]<128))break e;r|=(e[t]&127)<<28}return r}function Dr(e){for(var t=[],r=[0];r[0]<e.length;){var n=r[0],a=Ga(e,r),s=a&7;a=Math.floor(a/8);var i=0,c;if(a==0)break;switch(s){case 0:{for(var o=r[0];e[r[0]++]>=128;);c=e.slice(o,r[0])}break;case 5:i=4,c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 1:i=8,c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 2:i=Ga(e,r),c=e.slice(r[0],r[0]+i),r[0]+=i;break;case 3:case 4:default:throw new Error("PB Type ".concat(s," for Field ").concat(a," at offset ").concat(n))}var u={data:c,type:s};t[a]==null?t[a]=[u]:t[a].push(u)}return t}function $s(e,t){return e?.map(function(r){return t(r.data)})||[]}function Lm(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=Ga(e,n),s=Dr(e.slice(n[0],n[0]+a));n[0]+=a;var i={id:wr(s[1][0].data),messages:[]};s[2].forEach(function(c){var o=Dr(c.data),u=wr(o[3][0].data);i.messages.push({meta:o,data:e.slice(n[0],n[0]+u)}),n[0]+=u}),(t=s[3])!=null&&t[0]&&(i.merge=wr(s[3][0].data)>>>0>0),r.push(i)}return r}function Bm(e,t){if(e!=0)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=Ga(t,r),a=[];r[0]<t.length;){var s=t[r[0]]&3;if(s==0){var i=t[r[0]++]>>2;if(i<60)++i;else{var c=i-59;i=t[r[0]],c>1&&(i|=t[r[0]+1]<<8),c>2&&(i|=t[r[0]+2]<<16),c>3&&(i|=t[r[0]+3]<<24),i>>>=0,i++,r[0]+=c}a.push(t.slice(r[0],r[0]+i)),r[0]+=i;continue}else{var o=0,u=0;if(s==1?(u=(t[r[0]]>>2&7)+4,o=(t[r[0]++]&224)<<3,o|=t[r[0]++]):(u=(t[r[0]++]>>2)+1,s==2?(o=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(o=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[us(a)],o==0)throw new Error("Invalid offset 0");if(o>a[0].length)throw new Error("Invalid offset beyond length");if(u>=o)for(a.push(a[0].slice(-o)),u-=o;u>=a[a.length-1].length;)a.push(a[a.length-1]),u-=a[a.length-1].length;a.push(a[0].slice(-o,-o+u))}}var f=us(a);if(f.length!=n)throw new Error("Unexpected length: ".concat(f.length," != ").concat(n));return f}function Mm(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Bm(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return us(t)}function $m(e,t,r,n){var a=Ms(e),s=a.getUint32(4,!0),i=(n>1?12:8)+J0(s&(n>1?3470:398))*4,c=-1,o=-1,u=NaN,f=new Date(2001,0,1);s&512&&(c=a.getUint32(i,!0),i+=4),i+=J0(s&(n>1?12288:4096))*4,s&16&&(o=a.getUint32(i,!0),i+=4),s&32&&(u=a.getFloat64(i,!0),i+=8),s&64&&(f.setTime(f.getTime()+a.getFloat64(i,!0)*1e3),i+=8);var x;switch(e[2]){case 0:break;case 2:x={t:"n",v:u};break;case 3:x={t:"s",v:t[o]};break;case 5:x={t:"d",v:f};break;case 6:x={t:"b",v:u>0};break;case 7:x={t:"n",v:u/86400};break;case 8:x={t:"e",v:0};break;case 9:if(c>-1)x={t:"s",v:r[c]};else if(o>-1)x={t:"s",v:t[o]};else if(!isNaN(u))x={t:"n",v:u};else throw new Error("Unsupported cell type ".concat(e.slice(0,4)));break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return x}function Um(e,t,r){var n=Ms(e),a=n.getUint32(8,!0),s=12,i=-1,c=-1,o=NaN,u=NaN,f=new Date(2001,0,1);a&1&&(o=Pm(e,s),s+=16),a&2&&(u=n.getFloat64(s,!0),s+=8),a&4&&(f.setTime(f.getTime()+n.getFloat64(s,!0)*1e3),s+=8),a&8&&(c=n.getUint32(s,!0),s+=4),a&16&&(i=n.getUint32(s,!0),s+=4);var x;switch(e[1]){case 0:break;case 2:x={t:"n",v:o};break;case 3:x={t:"s",v:t[c]};break;case 5:x={t:"d",v:f};break;case 6:x={t:"b",v:u>0};break;case 7:x={t:"n",v:u/86400};break;case 8:x={t:"e",v:0};break;case 9:if(i>-1)x={t:"s",v:r[i]};else throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(a&31," : ").concat(e.slice(0,4)));break;case 10:x={t:"n",v:o};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(a&31," : ").concat(e.slice(0,4)))}return x}function Hm(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:return $m(e,t,r,e[0]);case 5:return Um(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function Nt(e){var t=Dr(e);return Ga(t[1][0].data)}function q0(e,t){var r=Dr(t.data),n=wr(r[1][0].data),a=r[3],s=[];return(a||[]).forEach(function(i){var c=Dr(i.data),o=wr(c[1][0].data)>>>0;switch(n){case 1:s[o]=fs(c[3][0].data);break;case 8:{var u=e[Nt(c[9][0].data)][0],f=Dr(u.data),x=e[Nt(f[1][0].data)][0],d=wr(x.meta[1][0].data);if(d!=2001)throw new Error("2000 unexpected reference to ".concat(d));var p=Dr(x.data);s[o]=p[3].map(function(g){return fs(g.data)}).join("")}break}}),s}function Gm(e,t){var r,n,a,s,i,c,o,u,f,x,d,p,g,m,h=Dr(e),_=wr(h[1][0].data)>>>0,k=wr(h[2][0].data)>>>0,v=((n=(r=h[8])==null?void 0:r[0])==null?void 0:n.data)&&wr(h[8][0].data)>0||!1,R,M;if((s=(a=h[7])==null?void 0:a[0])!=null&&s.data&&t!=0)R=(c=(i=h[7])==null?void 0:i[0])==null?void 0:c.data,M=(u=(o=h[6])==null?void 0:o[0])==null?void 0:u.data;else if((x=(f=h[4])==null?void 0:f[0])!=null&&x.data&&t!=1)R=(p=(d=h[4])==null?void 0:d[0])==null?void 0:p.data,M=(m=(g=h[3])==null?void 0:g[0])==null?void 0:m.data;else throw"NUMBERS Tile missing ".concat(t," cell storage");for(var S=v?4:1,w=Ms(R),j=[],E=0;E<R.length/2;++E){var L=w.getUint16(E*2,!0);L<65535&&j.push([E,L])}if(j.length!=k)throw"Expected ".concat(k," cells, found ").concat(j.length);var G=[];for(E=0;E<j.length-1;++E)G[j[E][0]]=M.subarray(j[E][1]*S,j[E+1][1]*S);return j.length>=1&&(G[j[j.length-1][0]]=M.subarray(j[j.length-1][1]*S)),{R:_,cells:G}}function Vm(e,t){var r,n=Dr(t.data),a=(r=n?.[7])!=null&&r[0]?wr(n[7][0].data)>>>0>0?1:0:-1,s=$s(n[5],function(i){return Gm(i,a)});return{nrows:wr(n[4][0].data)>>>0,data:s.reduce(function(i,c){return i[c.R]||(i[c.R]=[]),c.cells.forEach(function(o,u){if(i[c.R][u])throw new Error("Duplicate cell r=".concat(c.R," c=").concat(u));i[c.R][u]=o}),i},[])}}function Wm(e,t,r){var n,a=Dr(t.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(wr(a[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(a[6][0].data));if(s.e.c=(wr(a[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(a[7][0].data));r["!ref"]=Ke(s);var i=Dr(a[4][0].data),c=q0(e,e[Nt(i[4][0].data)][0]),o=(n=i[17])!=null&&n[0]?q0(e,e[Nt(i[17][0].data)][0]):[],u=Dr(i[3][0].data),f=0;u[1].forEach(function(x){var d=Dr(x.data),p=e[Nt(d[2][0].data)][0],g=wr(p.meta[1][0].data);if(g!=6002)throw new Error("6001 unexpected reference to ".concat(g));var m=Vm(e,p);m.data.forEach(function(h,_){h.forEach(function(k,v){var R=Le({r:f+_,c:v}),M=Hm(k,c,o);M&&(r[R]=M)})}),f+=m.nrows})}function Xm(e,t){var r=Dr(t.data),n={"!ref":"A1"},a=e[Nt(r[2][0].data)],s=wr(a[0].meta[1][0].data);if(s!=6001)throw new Error("6000 unexpected reference to ".concat(s));return Wm(e,a[0],n),n}function Km(e,t){var r,n=Dr(t.data),a={name:(r=n[1])!=null&&r[0]?fs(n[1][0].data):"",sheets:[]},s=$s(n[2],Nt);return s.forEach(function(i){e[i].forEach(function(c){var o=wr(c.meta[1][0].data);o==6e3&&a.sheets.push(Xm(e,c))})}),a}function zm(e,t){var r=Gs(),n=Dr(t.data),a=$s(n[1],Nt);if(a.forEach(function(s){e[s].forEach(function(i){var c=wr(i.meta[1][0].data);if(c==2){var o=Km(e,i);o.sheets.forEach(function(u,f){Vs(r,u,f==0?o.name:o.name+"_"+f,!0)})}})}),r.SheetNames.length==0)throw new Error("Empty NUMBERS file");return r}function Jn(e){var t,r,n,a,s={},i=[];if(e.FullPaths.forEach(function(o){if(o.match(/\.iwpv2/))throw new Error("Unsupported password protection")}),e.FileIndex.forEach(function(o){if(o.name.match(/\.iwa$/)){var u;try{u=Mm(o.content)}catch(x){return console.log("?? "+o.content.length+" "+(x.message||x))}var f;try{f=Lm(u)}catch(x){return console.log("## "+(x.message||x))}f.forEach(function(x){s[x.id]=x.messages,i.push(x.id)})}}),!i.length)throw new Error("File has no messages");var c=((a=(n=(r=(t=s?.[1])==null?void 0:t[0])==null?void 0:r.meta)==null?void 0:n[1])==null?void 0:a[0].data)&&wr(s[1][0].meta[1][0].data)==1&&s[1][0];if(c||i.forEach(function(o){s[o].forEach(function(u){var f=wr(u.meta[1][0].data)>>>0;if(f==1)if(!c)c=u;else throw new Error("Document has multiple roots")})}),!c)throw new Error("Cannot find Document root");return zm(s,c)}function Ym(e){return function(r){for(var n=0;n!=e.length;++n){var a=e[n];r[a[0]]===void 0&&(r[a[0]]=a[1]),a[2]==="n"&&(r[a[0]]=Number(r[a[0]]))}}}function Us(e){Ym([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function Jm(e){return sa.WS.indexOf(e)>-1?"sheet":e==sa.CS?"chart":e==sa.DS?"dialog":e==sa.MS?"macro":e&&e.length?e:"sheet"}function qm(e,t){if(!e)return 0;try{e=t.map(function(n){return n.id||(n.id=n.strRelID),[n.name,e["!id"][n.id].Target,Jm(e["!id"][n.id].Type)]})}catch{return null}return!e||e.length===0?null:e}function Zm(e,t,r,n,a,s,i,c,o,u,f,x){try{s[n]=Oa(Wr(e,r,!0),t);var d=dr(e,t),p;switch(c){case"sheet":p=nm(d,t,a,o,s[n],u,f,x);break;case"chart":if(p=sm(d,t,a,o,s[n],u,f,x),!p||!p["!drawel"])break;var g=ka(p["!drawel"].Target,t),m=ss(g),h=ax(Wr(e,g,!0),Oa(Wr(e,m,!0),g)),_=ka(h,g),k=ss(_);p=U1(Wr(e,_,!0),_,o,Oa(Wr(e,k,!0),_),u,p);break;case"macro":p=im(d,t,a,o,s[n],u,f,x);break;case"dialog":p=cm(d,t,a,o,s[n],u,f,x);break;default:throw new Error("Unrecognized sheet type "+c)}i[n]=p;var v=[];s&&s[n]&&ht(s[n]).forEach(function(R){var M="";if(s[n][R].Type==sa.CMNT){M=ka(s[n][R].Target,t);var S=um(dr(e,M,!0),M,o);if(!S||!S.length)return;P0(p,S,!1)}s[n][R].Type==sa.TCMNT&&(M=ka(s[n][R].Target,t),v=v.concat(sx(dr(e,M,!0),o)))}),v&&v.length&&P0(p,v,!0,o.people||[])}catch(R){if(o.WTF)throw R}}function Zr(e){return e.charAt(0)=="/"?e.slice(1):e}function Qm(e,t){if(Ti(),t=t||{},Us(t),et(e,"META-INF/manifest.xml")||et(e,"objectdata.xml"))return z0(e,t);if(et(e,"Index/Document.iwa")){if(typeof Uint8Array>"u")throw new Error("NUMBERS file parsing requires Uint8Array support");if(typeof Jn<"u"){if(e.FileIndex)return Jn(e);var r=Xe.utils.cfb_new();return o0(e).forEach(function(q){Zl(r,q,ql(e,q))}),Jn(r)}throw new Error("Unsupported NUMBERS file")}if(!et(e,"[Content_Types].xml"))throw et(e,"index.xml.gz")?new Error("Unsupported NUMBERS 08 file"):et(e,"index.xml")?new Error("Unsupported NUMBERS 09 file"):new Error("Unsupported ZIP file");var n=o0(e),a=zo(Wr(e,"[Content_Types].xml")),s=!1,i,c;if(a.workbooks.length===0&&(c="xl/workbook.xml",dr(e,c,!0)&&a.workbooks.push(c)),a.workbooks.length===0){if(c="xl/workbook.bin",!dr(e,c,!0))throw new Error("Could not find workbook");a.workbooks.push(c),s=!0}a.workbooks[0].slice(-3)=="bin"&&(s=!0);var o={},u={};if(!t.bookSheets&&!t.bookProps){if(Ia=[],a.sst)try{Ia=fm(dr(e,Zr(a.sst)),a.sst,t)}catch(q){if(t.WTF)throw q}t.cellStyles&&a.themes.length&&(o=om(Wr(e,a.themes[0].replace(/^\//,""),!0)||"",a.themes[0],t)),a.style&&(u=lm(dr(e,Zr(a.style)),a.style,o,t))}a.links.map(function(q){try{var ae=Oa(Wr(e,ss(Zr(q))),q);return xm(dr(e,Zr(q)),ae,q,t)}catch{}});var f=am(dr(e,Zr(a.workbooks[0])),a.workbooks[0],t),x={},d="";a.coreprops.length&&(d=dr(e,Zr(a.coreprops[0]),!0),d&&(x=Zi(d)),a.extprops.length!==0&&(d=dr(e,Zr(a.extprops[0]),!0),d&&Qo(d,x,t)));var p={};(!t.bookSheets||t.bookProps)&&a.custprops.length!==0&&(d=Wr(e,Zr(a.custprops[0]),!0),d&&(p=rf(d,t)));var g={};if((t.bookSheets||t.bookProps)&&(f.Sheets?i=f.Sheets.map(function(ae){return ae.name}):x.Worksheets&&x.SheetNames.length>0&&(i=x.SheetNames),t.bookProps&&(g.Props=x,g.Custprops=p),t.bookSheets&&typeof i<"u"&&(g.SheetNames=i),t.bookSheets?g.SheetNames:t.bookProps))return g;i={};var m={};t.bookDeps&&a.calcchain&&(m=hm(dr(e,Zr(a.calcchain)),a.calcchain));var h=0,_={},k,v;{var R=f.Sheets;x.Worksheets=R.length,x.SheetNames=[];for(var M=0;M!=R.length;++M)x.SheetNames[M]=R[M].name}var S=s?"bin":"xml",w=a.workbooks[0].lastIndexOf("/"),j=(a.workbooks[0].slice(0,w+1)+"_rels/"+a.workbooks[0].slice(w+1)+".rels").replace(/^\//,"");et(e,j)||(j="xl/_rels/workbook."+S+".rels");var E=Oa(Wr(e,j,!0),j.replace(/_rels.*/,"s5s"));(a.metadata||[]).length>=1&&(t.xlmeta=dm(dr(e,Zr(a.metadata[0])),a.metadata[0],t)),(a.people||[]).length>=1&&(t.people=ix(dr(e,Zr(a.people[0])),t)),E&&(E=qm(E,f.Sheets));var L=dr(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(h=0;h!=x.Worksheets;++h){var G="sheet";if(E&&E[h]?(k="xl/"+E[h][1].replace(/[\/]?xl\//,""),et(e,k)||(k=E[h][1]),et(e,k)||(k=j.replace(/_rels\/.*$/,"")+E[h][1]),G=E[h][2]):(k="xl/worksheets/sheet"+(h+1-L)+"."+S,k=k.replace(/sheet0\./,"sheet.")),v=k.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&t.sheets!=null)switch(typeof t.sheets){case"number":if(h!=t.sheets)continue e;break;case"string":if(x.SheetNames[h].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var C=!1,I=0;I!=t.sheets.length;++I)typeof t.sheets[I]=="number"&&t.sheets[I]==h&&(C=1),typeof t.sheets[I]=="string"&&t.sheets[I].toLowerCase()==x.SheetNames[h].toLowerCase()&&(C=1);if(!C)continue e}}Zm(e,k,v,x.SheetNames[h],h,_,i,G,t,f,o,u)}return g={Directory:a,Workbook:f,Props:x,Custprops:p,Deps:m,Sheets:i,SheetNames:x.SheetNames,Strings:Ia,Styles:u,Themes:o,SSF:kr(Me)},t&&t.bookFiles&&(e.files?(g.keys=n,g.files=e.files):(g.keys=[],g.files={},e.FullPaths.forEach(function(q,ae){q=q.replace(/^Root Entry[\/]/,""),g.keys.push(q),g.files[q]=e.FileIndex[ae]}))),t&&t.bookVBA&&(a.vba.length>0?g.vbaraw=dr(e,Zr(a.vba[0]),!0):a.defaults&&a.defaults.bin===fx&&(g.vbaraw=dr(e,"xl/vbaProject.bin",!0))),g}function eg(e,t){var r=t||{},n="Workbook",a=Xe.find(e,n);try{if(n="/!DataSpaces/Version",a=Xe.find(e,n),!a||!a.content)throw new Error("ECMA-376 Encrypted file missing "+n);if(Xu(a.content),n="/!DataSpaces/DataSpaceMap",a=Xe.find(e,n),!a||!a.content)throw new Error("ECMA-376 Encrypted file missing "+n);var s=zu(a.content);if(s.length!==1||s[0].comps.length!==1||s[0].comps[0].t!==0||s[0].name!=="StrongEncryptionDataSpace"||s[0].comps[0].v!=="EncryptedPackage")throw new Error("ECMA-376 Encrypted file bad "+n);if(n="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",a=Xe.find(e,n),!a||!a.content)throw new Error("ECMA-376 Encrypted file missing "+n);var i=Yu(a.content);if(i.length!=1||i[0]!="StrongEncryptionTransform")throw new Error("ECMA-376 Encrypted file bad "+n);if(n="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",a=Xe.find(e,n),!a||!a.content)throw new Error("ECMA-376 Encrypted file missing "+n);qu(a.content)}catch{}if(n="/EncryptionInfo",a=Xe.find(e,n),!a||!a.content)throw new Error("ECMA-376 Encrypted file missing "+n);var c=Zu(a.content);if(n="/EncryptedPackage",a=Xe.find(e,n),!a||!a.content)throw new Error("ECMA-376 Encrypted file missing "+n);if(c[0]==4&&typeof decrypt_agile<"u")return decrypt_agile(c[1],a.content,r.password||"",r);if(c[0]==2&&typeof decrypt_std76<"u")return decrypt_std76(c[1],a.content,r.password||"",r);throw new Error("File is password-protected")}function Hs(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Xr(e.slice(0,12));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function rg(e,t){return Xe.find(e,"EncryptedPackage")?eg(e,t):Pc(e,t)}function tg(e,t){var r,n=e,a=t||{};return a.type||(a.type=We&&Buffer.isBuffer(e)?"buffer":"base64"),r=Ni(n,a),Qm(r,a)}function $c(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return ls(e.slice(r),t);default:break e}return $a.to_workbook(e,t)}function ag(e,t){var r="",n=Hs(e,t);switch(t.type){case"base64":r=Xr(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=Vt(e);break;default:throw new Error("Unrecognized type "+t.type)}return n[0]==239&&n[1]==187&&n[2]==191&&(r=ar(r)),t.type="binary",$c(r,t)}function ng(e,t){var r=e;return t.type=="base64"&&(r=Xr(r)),r=ts.utils.decode(1200,r.slice(2),"str"),t.type="binary",$c(r,t)}function sg(e){return e.match(/[^\x00-\x7F]/)?Sa(e):e}function qn(e,t,r,n){return n?(r.type="string",$a.to_workbook(e,r)):$a.to_workbook(t,r)}function bn(e,t){ui();var r=t||{};if(typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer)return bn(new Uint8Array(e),(r=kr(r),r.type="array",r));typeof Uint8Array<"u"&&e instanceof Uint8Array&&!r.type&&(r.type=typeof Deno<"u"?"buffer":"array");var n=e,a=[0,0,0,0],s=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),oa={},r.dateNF&&(oa.dateNF=r.dateNF),r.type||(r.type=We&&Buffer.isBuffer(e)?"buffer":"base64"),r.type=="file"&&(r.type=We?"buffer":"binary",n=Vl(e),typeof Uint8Array<"u"&&!We&&(r.type="array")),r.type=="string"&&(s=!0,r.type="binary",r.codepage=65001,n=sg(e)),r.type=="array"&&typeof Uint8Array<"u"&&e instanceof Uint8Array&&typeof ArrayBuffer<"u"){var i=new ArrayBuffer(3),c=new Uint8Array(i);if(c.foo="bar",!c.foo)return r=kr(r),r.type="array",bn(ws(n),r)}switch((a=Hs(n,r))[0]){case 208:if(a[1]===207&&a[2]===17&&a[3]===224&&a[4]===161&&a[5]===177&&a[6]===26&&a[7]===225)return rg(Xe.read(n,r),r);break;case 9:if(a[1]<=8)return Pc(n,r);break;case 60:return ls(n,r);case 73:if(a[1]===73&&a[2]===42&&a[3]===0)throw new Error("TIFF Image File is not a spreadsheet");if(a[1]===68)return Ru(n,r);break;case 84:if(a[1]===65&&a[2]===66&&a[3]===76)return Ou.to_workbook(n,r);break;case 80:return a[1]===75&&a[2]<9&&a[3]<9?tg(n,r):qn(e,n,r,s);case 239:return a[3]===60?ls(n,r):qn(e,n,r,s);case 255:if(a[1]===254)return ng(n,r);if(a[1]===0&&a[2]===2&&a[3]===0)return Ra.to_workbook(n,r);break;case 0:if(a[1]===0&&(a[2]>=2&&a[3]===0||a[2]===0&&(a[3]===8||a[3]===9)))return Ra.to_workbook(n,r);break;case 3:case 131:case 139:case 140:return j0.to_workbook(n,r);case 123:if(a[1]===92&&a[2]===114&&a[3]===116)return fh.to_workbook(n,r);break;case 10:case 13:case 32:return ag(n,r);case 137:if(a[1]===80&&a[2]===78&&a[3]===71)throw new Error("PNG Image File is not a spreadsheet");break}return Nu.indexOf(a[0])>-1&&a[2]<=12&&a[3]<=31?j0.to_workbook(n,r):qn(e,n,r,s)}function ig(e,t,r,n,a,s,i,c){var o=Sr(r),u=c.defval,f=c.raw||!Object.prototype.hasOwnProperty.call(c,"raw"),x=!0,d=a===1?[]:{};if(a!==1)if(Object.defineProperty)try{Object.defineProperty(d,"__rowNum__",{value:r,enumerable:!1})}catch{d.__rowNum__=r}else d.__rowNum__=r;if(!i||e[r])for(var p=t.s.c;p<=t.e.c;++p){var g=i?e[r][p]:e[n[p]+o];if(g===void 0||g.t===void 0){if(u===void 0)continue;s[p]!=null&&(d[s[p]]=u);continue}var m=g.v;switch(g.t){case"z":if(m==null)break;continue;case"e":m=m==0?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+g.t)}if(s[p]!=null){if(m==null)if(g.t=="e"&&m===null)d[s[p]]=null;else if(u!==void 0)d[s[p]]=u;else if(f&&m===null)d[s[p]]=null;else continue;else d[s[p]]=f&&(g.t!=="n"||g.t==="n"&&c.rawNumbers!==!1)?m:_t(g,m,c);m!=null&&(x=!1)}}return{row:d,isempty:x}}function hs(e,t){if(e==null||e["!ref"]==null)return[];var r={t:"n",v:0},n=0,a=1,s=[],i=0,c="",o={s:{r:0,c:0},e:{r:0,c:0}},u=t||{},f=u.range!=null?u.range:e["!ref"];switch(u.header===1?n=1:u.header==="A"?n=2:Array.isArray(u.header)?n=3:u.header==null&&(n=0),typeof f){case"string":o=or(f);break;case"number":o=or(e["!ref"]),o.s.r=f;break;default:o=f}n>0&&(a=0);var x=Sr(o.s.r),d=[],p=[],g=0,m=0,h=Array.isArray(e),_=o.s.r,k=0,v={};h&&!e[_]&&(e[_]=[]);var R=u.skipHidden&&e["!cols"]||[],M=u.skipHidden&&e["!rows"]||[];for(k=o.s.c;k<=o.e.c;++k)if(!(R[k]||{}).hidden)switch(d[k]=yr(k),r=h?e[_][k]:e[d[k]+x],n){case 1:s[k]=k-o.s.c;break;case 2:s[k]=d[k];break;case 3:s[k]=u.header[k-o.s.c];break;default:if(r==null&&(r={w:"__EMPTY",t:"s"}),c=i=_t(r,null,u),m=v[i]||0,!m)v[i]=1;else{do c=i+"_"+m++;while(v[c]);v[i]=m,v[c]=1}s[k]=c}for(_=o.s.r+a;_<=o.e.r;++_)if(!(M[_]||{}).hidden){var S=ig(e,o,_,d,n,s,h,u);(S.isempty===!1||(n===1?u.blankrows!==!1:u.blankrows))&&(p[g++]=S.row)}return p.length=g,p}var Z0=/"/g;function cg(e,t,r,n,a,s,i,c){for(var o=!0,u=[],f="",x=Sr(r),d=t.s.c;d<=t.e.c;++d)if(n[d]){var p=c.dense?(e[r]||[])[d]:e[n[d]+x];if(p==null)f="";else if(p.v!=null){o=!1,f=""+(c.rawNumbers&&p.t=="n"?p.v:_t(p,null,c));for(var g=0,m=0;g!==f.length;++g)if((m=f.charCodeAt(g))===a||m===s||m===34||c.forceQuotes){f='"'+f.replace(Z0,'""')+'"';break}f=="ID"&&(f='"ID"')}else p.f!=null&&!p.F?(o=!1,f="="+p.f,f.indexOf(",")>=0&&(f='"'+f.replace(Z0,'""')+'"')):f="";u.push(f)}return c.blankrows===!1&&o?null:u.join(i)}function Uc(e,t){var r=[],n=t??{};if(e==null||e["!ref"]==null)return"";var a=or(e["!ref"]),s=n.FS!==void 0?n.FS:",",i=s.charCodeAt(0),c=n.RS!==void 0?n.RS:`
`,o=c.charCodeAt(0),u=new RegExp((s=="|"?"\\|":s)+"+$"),f="",x=[];n.dense=Array.isArray(e);for(var d=n.skipHidden&&e["!cols"]||[],p=n.skipHidden&&e["!rows"]||[],g=a.s.c;g<=a.e.c;++g)(d[g]||{}).hidden||(x[g]=yr(g));for(var m=0,h=a.s.r;h<=a.e.r;++h)(p[h]||{}).hidden||(f=cg(e,a,h,x,i,o,s,n),f!=null&&(n.strip&&(f=f.replace(u,"")),(f||n.blankrows!==!1)&&r.push((m++?c:"")+f)));return delete n.dense,r.join("")}function lg(e,t){t||(t={}),t.FS="	",t.RS=`
`;var r=Uc(e,t);return r}function og(e){var t="",r,n="";if(e==null||e["!ref"]==null)return[];var a=or(e["!ref"]),s="",i=[],c,o=[],u=Array.isArray(e);for(c=a.s.c;c<=a.e.c;++c)i[c]=yr(c);for(var f=a.s.r;f<=a.e.r;++f)for(s=Sr(f),c=a.s.c;c<=a.e.c;++c)if(t=i[c]+s,r=u?(e[f]||[])[c]:e[t],n="",r!==void 0){if(r.F!=null){if(t=r.F,!r.f)continue;n=r.f,t.indexOf(":")==-1&&(t=t+":"+t)}if(r.f!=null)n=r.f;else{if(r.t=="z")continue;if(r.t=="n"&&r.v!=null)n=""+r.v;else if(r.t=="b")n=r.v?"TRUE":"FALSE";else if(r.w!==void 0)n="'"+r.w;else{if(r.v===void 0)continue;r.t=="s"?n="'"+r.v:n=""+r.v}}o[o.length]=t+"="+n}return o}function Hc(e,t,r){var n=r||{},a=+!n.skipHeader,s=e||{},i=0,c=0;if(s&&n.origin!=null)if(typeof n.origin=="number")i=n.origin;else{var o=typeof n.origin=="string"?Mr(n.origin):n.origin;i=o.r,c=o.c}var u,f={s:{c:0,r:0},e:{c,r:i+t.length-1+a}};if(s["!ref"]){var x=or(s["!ref"]);f.e.c=Math.max(f.e.c,x.e.c),f.e.r=Math.max(f.e.r,x.e.r),i==-1&&(i=x.e.r+1,f.e.r=i+t.length-1+a)}else i==-1&&(i=0,f.e.r=t.length-1+a);var d=n.header||[],p=0;t.forEach(function(m,h){ht(m).forEach(function(_){(p=d.indexOf(_))==-1&&(d[p=d.length]=_);var k=m[_],v="z",R="",M=Le({c:c+p,r:i+h+a});u=Va(s,M),k&&typeof k=="object"&&!(k instanceof Date)?s[M]=k:(typeof k=="number"?v="n":typeof k=="boolean"?v="b":typeof k=="string"?v="s":k instanceof Date?(v="d",n.cellDates||(v="n",k=Ur(k)),R=n.dateNF||Me[14]):k===null&&n.nullError&&(v="e",k=0),u?(u.t=v,u.v=k,delete u.w,delete u.R,R&&(u.z=R)):s[M]=u={t:v,v:k},R&&(u.z=R))})}),f.e.c=Math.max(f.e.c,c+d.length-1);var g=Sr(i);if(a)for(p=0;p<d.length;++p)s[yr(p+c)+g]={t:"s",v:d[p]};return s["!ref"]=Ke(f),s}function fg(e,t){return Hc(null,e,t)}function Va(e,t,r){if(typeof t=="string"){if(Array.isArray(e)){var n=Mr(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return typeof t!="number"?Va(e,Le(t)):Va(e,Le({r:t,c:r||0}))}function ug(e,t){if(typeof t=="number"){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}else if(typeof t=="string"){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}else throw new Error("Cannot find sheet |"+t+"|")}function Gs(){return{SheetNames:[],Sheets:{}}}function Vs(e,t,r,n){var a=1;if(!r)for(;a<=65535&&e.SheetNames.indexOf(r="Sheet"+a)!=-1;++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var s=r.match(/(^.*?)(\d+)$/);a=s&&+s[2]||0;var i=s&&s[1]||r;for(++a;a<=65535&&e.SheetNames.indexOf(r=i+a)!=-1;++a);}if(Y1(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function hg(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=ug(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r}function xg(e,t){return e.z=t,e}function Gc(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}function dg(e,t,r){return Gc(e,"#"+t,r)}function mg(e,t,r){e.c||(e.c=[]),e.c.push({t,a:r||"SheetJS"})}function gg(e,t,r,n){for(var a=typeof t!="string"?t:or(t),s=typeof t=="string"?t:Ke(t),i=a.s.r;i<=a.e.r;++i)for(var c=a.s.c;c<=a.e.c;++c){var o=Va(e,i,c);o.t="n",o.F=s,delete o.v,i==a.s.r&&c==a.s.c&&(o.f=r,n&&(o.D=!0))}return e}var xs={encode_col:yr,encode_row:Sr,encode_cell:Le,encode_range:Ke,decode_col:Ns,decode_row:Fs,split_cell:Ao,decode_cell:Mr,decode_range:xa,format_cell:_t,sheet_add_aoa:Ki,sheet_add_json:Hc,sheet_add_dom:Lc,aoa_to_sheet:da,json_to_sheet:fg,table_to_sheet:Bc,table_to_book:Rm,sheet_to_csv:Uc,sheet_to_txt:lg,sheet_to_json:hs,sheet_to_html:Dm,sheet_to_formulae:og,sheet_to_row_object_array:hs,sheet_get_cell:Va,book_new:Gs,book_append_sheet:Vs,book_set_sheet_visibility:hg,cell_set_number_format:xg,cell_set_hyperlink:Gc,cell_set_internal_link:dg,cell_add_comment:mg,sheet_set_array_formula:gg,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};async function pg(e){return e.length<5e4?JSON.parse(e):new Promise((t,r)=>{const n=()=>{try{const a=JSON.parse(e);t(a)}catch(a){r(a)}};"requestIdleCallback"in window?requestIdleCallback(n,{timeout:100}):setTimeout(n,0)})}async function Q0(e){const t=JSON.stringify(e);return t.length<5e4?t:new Promise((r,n)=>{const a=()=>{try{const s=JSON.stringify(e,null,2);r(s)}catch(s){n(s)}};"requestIdleCallback"in window?requestIdleCallback(a,{timeout:100}):setTimeout(a,0)})}const vg=!!window.electronAPI;function yg(e){if(vg){setTimeout(e,0);return}"requestIdleCallback"in window?requestIdleCallback(e,{timeout:50}):requestAnimationFrame(()=>setTimeout(e,0))}function kn(){const e=document.createElement("div");e.style.cssText="position:fixed;top:-9999px;width:1px;height:1px",document.body.appendChild(e);let t=0;const r=()=>{e.style.transform=`translateX(${Math.sin(++t*.1)}px)`,t<60?requestAnimationFrame(r):document.body.removeChild(e)};requestAnimationFrame(r)}async function wg(e,t){kn();const r=[];e.dailyData&&r.push(()=>t.setDailyData(e.dailyData)),e.clientList&&r.push(()=>t.setClientList(e.clientList)),e.employeeList&&r.push(()=>t.setEmployeeList(e.employeeList)),e.customHolidays&&r.push(()=>t.setCustomHolidays(e.customHolidays)),e.employeeTransactions&&r.push(()=>t.setEmployeeTransactions(e.employeeTransactions)),e.transactionGroups&&r.push(()=>t.setTransactionGroups(e.transactionGroups)),e.departments&&r.push(()=>t.setDepartments(e.departments)),e.turnoverOverrides&&r.push(()=>t.setTurnoverOverrides(e.turnoverOverrides)),e.salaryCashPayments&&r.push(()=>t.setSalaryCashPayments(e.salaryCashPayments)),e.leaveData&&r.push(()=>t.setLeaveData(e.leaveData)),e.salaryInputs&&r.push(()=>t.setSalaryInputs(e.salaryInputs)),e.changeHistory&&r.push(()=>t.setChangeHistory(e.changeHistory));for(let n=0;n<r.length;n++)await new Promise(a=>yg(()=>{r[n](),a()})),n%3===2&&await new Promise(a=>setTimeout(a,0))}function _g(){window.electronAPI&&window.addEventListener("keep-alive",kn),setInterval(()=>{},1e3)}class Eg{constructor(){this.queue=[],this.processing=!1,this.cache=new Map}async save(t,r){try{const n=JSON.stringify(r),a=JSON.parse(n);this.cache.set(t,a),this.queue.push({key:t,serializedValue:n}),this.processing||this.processQueue()}catch(n){console.error(`[StorageManager] Failed to serialize value for key "${t}" during the initial save call.`,n)}}async processQueue(){if(this.queue.length===0){this.processing=!1;return}this.processing=!0;const t=this.queue.splice(0,10),r=()=>{t.forEach(({key:n,serializedValue:a})=>{try{localStorage.setItem(n,a)}catch(s){console.error("Storage error:",s),this.handleStorageError(n,a,s)}}),this.queue.length>0?setTimeout(()=>this.processQueue(),0):this.processing=!1};"requestIdleCallback"in window?requestIdleCallback(r,{timeout:1e3}):setTimeout(r,16)}handleStorageError(t,r,n){if(n.name==="QuotaExceededError"){["cashManagement_changeHistory","cashManagement_dailyData"].forEach(s=>{try{const i=localStorage.getItem(s);if(i){const c=JSON.parse(i);if(Array.isArray(c)&&c.length>100)localStorage.setItem(s,JSON.stringify(c.slice(-100)));else if(typeof c=="object"&&!Array.isArray(c)&&c!==null){const o=Object.keys(c);if(o.length>365){const f=o.sort().slice(-365),x={};f.forEach(d=>x[d]=c[d]),localStorage.setItem(s,JSON.stringify(x))}}}}catch(i){console.error("Failed to clean storage:",i)}});try{localStorage.setItem(t,r)}catch(s){console.error("Failed to save after cleanup:",s)}}}load(t,r){if(this.cache.has(t))return this.cache.get(t);try{const n=localStorage.getItem(t);if(n===null)return this.cache.set(t,r),r;const a=JSON.parse(n);return this.cache.set(t,a),a}catch(n){return console.error("Error loading from storage:",n),this.cache.set(t,r),r}}flushSync(){for(;this.queue.length;){const{key:t,serializedValue:r}=this.queue.shift();try{localStorage.setItem(t,r)}catch(n){console.error("[StorageManager] flush failed",n)}}}}const Je=new Eg,Vc=ds.createContext(),Tg=({children:e})=>{const[t,r]=J.useState([]),n=(a,s="info")=>{const i=Date.now()+Math.random(),c={id:i,message:a,type:s};r(x=>[...x,c]);const o=(x,d)=>{r(p=>p.map(g=>g.id===i?{...g,message:x,type:d}:g))},u=()=>{r(x=>x.filter(d=>d.id!==i))},f=setTimeout(u,3e3);return{update:o,remove:u,timer:f}};return l.jsxs(Vc.Provider,{value:{showToast:n},children:[e,l.jsx("div",{className:"fixed bottom-4 right-4 z-50 space-y-2",children:t.map(a=>l.jsx("div",{className:`
              px-4 py-2 rounded shadow-lg text-white transition-all
              ${a.type==="success"?"bg-green-500":""}
              ${a.type==="error"?"bg-red-500":""}
              ${a.type==="warning"?"bg-yellow-500":""}
              ${a.type==="info"?"bg-blue-500":""}
            `,children:a.message},a.id))})]})},bg=()=>{const e=J.useContext(Vc);if(!e)throw new Error("useToast must be used within ToastProvider");return e},kg={2024:[{date:"01-01",name:"Нова година"},{date:"03-03",name:"Освобождение на България"},{date:"04-28",name:"Великден"},{date:"04-29",name:"Великден"},{date:"05-01",name:"Ден на труда"},{date:"05-06",name:"Гергьовден"},{date:"05-24",name:"Ден на славянската писменост"},{date:"09-06",name:"Ден на Съединението"},{date:"09-22",name:"Ден на независимостта"},{date:"12-24",name:"Бъдни вечер"},{date:"12-25",name:"Коледа"},{date:"12-26",name:"Коледа"}],2025:[{date:"01-01",name:"Нова година"},{date:"03-03",name:"Освобождение на България"},{date:"04-20",name:"Великден"},{date:"04-21",name:"Великден"},{date:"05-01",name:"Ден на труда"},{date:"05-06",name:"Гергьовден"},{date:"05-24",name:"Ден на славянската писменост"},{date:"09-06",name:"Ден на Съединението"},{date:"09-22",name:"Ден на независимостта"},{date:"12-24",name:"Бъдни вечер"},{date:"12-25",name:"Коледа"},{date:"12-26",name:"Коледа"}]},He=[100,50,20,10,5,2,1,.5,.2,.1,.05,.02,.01],sr=[500,200,100,50,20,10,5],Sg=["Аванс заплата","Заплати","Пренос","Пари за банка","Обмяна валута","Ремонти","Доставки","Услуги","Продажби","Други приходи","Други разходи","Наем","Комунални","Такси","Застраховки","Транспорт","Бонус"],Ag={Администрация:{workDays:["Monday","Tuesday","Wednesday","Thursday","Friday"],halfDays:[]},Цех:{workDays:["Sunday","Monday","Tuesday","Wednesday","Thursday"],halfDays:["Friday"]},Магазин:{workDays:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],halfDays:[]}},gr=e=>xs.decode_col(e),Fg={100:gr("D"),50:gr("E"),20:gr("F"),10:gr("G"),5:gr("H"),2:gr("I"),1:gr("J"),.5:gr("K"),.2:gr("L"),.1:gr("M"),.05:gr("N"),.02:gr("O"),.01:gr("P")},Ng={500:gr("R"),200:gr("S"),100:gr("T"),50:gr("U"),20:gr("V"),10:gr("W"),5:gr("X")};function ei(e){const t=e.match(/(\d{1,2})\.(\d{2})/);if(!t)return null;const r=Number(t[1]),n=2e3+Number(t[2]);return{month:r-1,year:n}}function Zn(){return{id:Math.random(),name:"",group:"",bgnCounts:{},eurCounts:{},bgnTotal:0,eurTotal:0,isCarryOver:!1}}const ri=e=>{const t={а:"a",б:"b",в:"v",г:"g",д:"d",е:"e",ж:"zh",з:"z",и:"i",й:"y",к:"k",л:"l",м:"m",н:"n",о:"o",п:"p",р:"r",с:"s",т:"t",у:"u",ф:"f",х:"h",ц:"ts",ч:"ch",ш:"sh",щ:"sht",ъ:"a",ь:"y",ю:"yu",я:"ya"};return e.toLowerCase().split("").map(r=>t[r]||r).join("")},ma=(e,t)=>{if(!e)return!1;if(!t)return!0;const r=ri(e),n=ri(t);return r.includes(n)},rt=({value:e,onChange:t,onBlur:r,className:n,type:a="text",blurOnEnter:s=!0,cellId:i,...c})=>{const[o,u]=J.useState(e||""),[f,x]=J.useState(!1),d=J.useRef(null);J.useEffect(()=>{f||u(e||"")},[e,f]);const p=(k,v)=>{if(!k)return;const R=k.split("-");if(R.length<3)return;const[M,S,w,j]=R,E=parseInt(S),L=j?parseInt(j):null;let G=null;if(v==="ArrowRight")w==="name"?G=`${M}-${S}-bgn-0`:w==="bgn"&&L!==null?L<6?G=`${M}-${S}-bgn-${L+1}`:G=`${M}-${S}-eur-0`:w==="eur"&&L!==null&&L<7&&(G=`${M}-${S}-eur-${L+1}`);else if(v==="ArrowLeft")w==="bgn"&&L!==null?L>0?G=`${M}-${S}-bgn-${L-1}`:G=`${M}-${S}-name`:w==="eur"&&L!==null&&(L>0?G=`${M}-${S}-eur-${L-1}`:G=`${M}-${S}-bgn-6`);else if(v==="ArrowUp"||v==="ArrowDown"){const C=v==="ArrowUp"?E-1:E+1;C>=0&&(G=`${M}-${C}-${w}${L!==null?"-"+L:""}`)}if(G){const C=document.querySelector(`[data-cell-id="${G}"]`);C&&(C.focus(),C.select())}},g=k=>{if(k.key==="Delete"){k.preventDefault(),u(""),t("");return}if(["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(k.key)){k.preventDefault(),p(i,k.key);return}k.key==="Enter"?(k.preventDefault(),s&&k.target.blur()):k.key==="Escape"&&(k.preventDefault(),u(e||""),k.target.blur())},m=k=>{x(!1),o!==e&&t(o),r&&r(k)},h=()=>{x(!0)},_=k=>{u(k.target.value)};return l.jsx("input",{ref:d,type:a,value:o,onChange:_,onBlur:m,onFocus:h,onKeyDown:g,className:n,"data-cell-id":i,...c})},ti=({value:e,onChange:t,className:r,suggestions:n=[],disabled:a,cellId:s,...i})=>{const[c,o]=J.useState(e||""),[u,f]=J.useState(!1),[x,d]=J.useState(!1),[p,g]=J.useState(-1),m=J.useRef(null),h=J.useRef(null),[_,k]=J.useState(!1),v=J.useMemo(()=>!c||!x||!Array.isArray(n)?[]:n.filter(I=>!I||typeof I!="object"||!I.name?!1:ma(I.name,c)&&I.name.toLowerCase()!==c.toLowerCase()).slice(0,10),[c,n,x]);J.useEffect(()=>{const I=q=>{m.current&&!m.current.contains(q.target)&&(d(!1),g(-1))};return document.addEventListener("mousedown",I),()=>document.removeEventListener("mousedown",I)},[]),J.useEffect(()=>{!u&&!_&&o(e||"")},[e,u,_]);const R=(I,q)=>{if(!I)return;const ae=I.split("-");if(ae.length<3)return;const[ce,de,ie,B]=ae,re=parseInt(de),he=B?parseInt(B):null;let F=null;if(q==="ArrowRight")ie==="name"&&(F=`${ce}-${de}-bgn-0`);else if(q==="ArrowUp"||q==="ArrowDown"){const U=q==="ArrowUp"?re-1:re+1;U>=0&&(F=`${ce}-${U}-${ie}${he!==null?"-"+he:""}`)}if(F){const U=document.querySelector(`[data-cell-id="${F}"]`);U&&(U.focus(),U.select())}},M=I=>{if(!_){if(I.key==="Delete"){I.preventDefault(),o(""),t("");return}if((!x||v.length===0)&&["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(I.key)){I.preventDefault(),S(),R(s,I.key);return}switch(I.key){case"ArrowDown":x&&v.length>0?(I.preventDefault(),g(q=>q<v.length-1?q+1:q)):(I.preventDefault(),S(),R(s,I.key));break;case"ArrowUp":x&&v.length>0?(I.preventDefault(),g(q=>q>0?q-1:-1)):(I.preventDefault(),S(),R(s,I.key));break;case"ArrowLeft":case"ArrowRight":(!x||v.length===0)&&(I.preventDefault(),S(),R(s,I.key));break;case"Enter":I.preventDefault(),p>=0&&v[p]?L(v[p].name):S();break;case"Escape":I.preventDefault(),o(e||""),d(!1),g(-1),h.current?.blur();break;case"Tab":x&&v.length>0&&p>=0&&(I.preventDefault(),L(v[p].name));break}}},S=()=>{c!==e&&t(c),d(!1),g(-1)},w=()=>{f(!1),setTimeout(()=>{const I=document.activeElement;(!m.current||!m.current.contains(I))&&S()},200)},j=()=>{f(!0),d(!0),g(-1)},E=I=>{const q=I.target.value;o(q),d(!0),g(-1),q===""&&t("")},L=I=>{o(I),t(I),d(!1),g(-1),h.current?.focus()},G=()=>{k(!0)},C=I=>{k(!1),E(I)};return l.jsxs("div",{className:"relative",ref:m,children:[l.jsx("input",{ref:h,type:"text",autoComplete:"off",value:c,onChange:E,onBlur:w,onFocus:j,onKeyDown:M,onCompositionStart:G,onCompositionEnd:C,className:r,disabled:a,"data-cell-id":s,...i}),x&&v.length>0&&!a&&l.jsx("div",{className:"absolute z-50 w-full bg-white border border-gray-300 rounded-md mt-1 max-h-48 overflow-y-auto shadow-lg",style:{minWidth:"200px"},children:v.map((I,q)=>l.jsxs("div",{onMouseDown:ae=>{ae.preventDefault(),L(I.name)},onMouseEnter:()=>g(q),className:`px-3 py-2 cursor-pointer transition-colors ${q===p?"bg-blue-500 text-white":"text-gray-900 hover:bg-gray-100"}`,children:[l.jsx("div",{className:"font-medium",children:I.name}),I.group&&l.jsx("div",{className:`text-xs ${q===p?"text-blue-100":"text-gray-500"}`,children:I.group})]},`${I.name}-${q}`))})]})},Cg=({rowData:e,isDarkMode:t,viewMode:r,columnWidths:n})=>{if(!e)return null;const{name:a,group:s,bgnTotal:i,eurTotal:c,bgnCounts:o,eurCounts:u}=e,f=`${t?"bg-gray-700 text-gray-500":"bg-gray-200 text-gray-500"} cursor-not-allowed`;return l.jsxs("tr",{className:`
        transition-colors duration-300
        ${t?"bg-gray-700/50":"bg-gray-200"}
        border-b ${t?"border-gray-700":"border-gray-200"}
      `,children:[l.jsx("td",{className:"px-2 py-2 text-center text-xs text-gray-500",children:"AUTO"}),l.jsx("td",{className:"px-2 py-2"}),l.jsx("td",{className:"px-2 py-2",children:l.jsx("input",{type:"text",value:s,disabled:!0,className:`w-full px-2 py-1 text-base border rounded ${f}`})}),l.jsx("td",{className:"px-3 py-2",children:l.jsx("input",{type:"text",value:a,disabled:!0,className:`w-full px-2 py-1 text-base border rounded ${f}`})}),(r==="both"||r==="bgn")&&l.jsxs("td",{className:"px-2 py-2 text-center font-semibold",children:[i.toFixed(2)," лв"]}),(r==="both"||r==="bgn")&&He.map((x,d)=>l.jsx("td",{className:"px-2 py-2",children:l.jsx("input",{type:"number",value:o[d]||"",disabled:!0,className:`w-full px-1 py-1 text-base text-center border rounded ${f}`})},d)),(r==="both"||r==="eur")&&l.jsxs("td",{className:"px-2 py-2 text-center font-semibold",children:["€",c.toFixed(2)]}),(r==="both"||r==="eur")&&sr.map((x,d)=>l.jsx("td",{className:"px-2 py-2",children:l.jsx("input",{type:"number",value:u[d]||"",disabled:!0,className:`w-full px-1 py-1 text-base text-center border rounded ${f}`})},d))]})},Og=({isDarkMode:e,currentMonth:t,setCurrentMonth:r,currentYear:n,setCurrentYear:a,selectedDay:s,setSelectedDay:i,daysInMonth:c,monthNames:o,isCurrentMonth:u,isWeekend:f,isHoliday:x,hasScrolledRef:d})=>l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-2 flex flex-col`,children:[l.jsxs("div",{className:"flex justify-between items-center mb-2",children:[l.jsx("button",{onClick:()=>{d.current=!1,t===0?(r(11),a(n-1)):r(t-1)},className:`p-1 ${e?"hover:bg-gray-700":"hover:bg-gray-100"} rounded`,children:l.jsx(gs,{className:"w-5 h-5"})}),l.jsxs("h3",{className:"font-bold text-lg",children:[o[t]," ",n]}),l.jsx("button",{onClick:()=>{d.current=!1,t===11?(r(0),a(n+1)):r(t+1)},className:`p-1 ${e?"hover:bg-gray-700":"hover:bg-gray-100"} rounded`,children:l.jsx(ps,{className:"w-5 h-5"})})]}),!u&&l.jsx("div",{className:"bg-red-100 border-l-8 border-red-600 p-4 my-3 rounded-md",children:l.jsxs("div",{className:"flex items-center",children:[l.jsx(cl,{className:"w-8 h-8 mr-4 text-red-600"}),l.jsx("p",{className:"text-xl font-bold text-red-700",children:"Внимание: Не е текущият месец!"})]})}),l.jsx("div",{className:"overflow-x-auto",children:l.jsx("div",{className:"flex gap-1",children:Array.from({length:c}).map((p,g)=>{const m=g+1,h=m===s,_=f(m),k=x(m);return l.jsx("button",{onClick:()=>{d.current=!1,i(m)},className:`flex-shrink-0 w-12 h-12 rounded text-center transition-colors flex items-center justify-center text-base ${h?"bg-blue-500 text-white":k?"bg-red-100 text-red-700 hover:bg-red-200":_?`${e?"bg-gray-700 text-gray-400":"bg-gray-200 text-gray-600"} hover:bg-gray-300`:e?"hover:bg-gray-700":"hover:bg-gray-100"}`,children:m},m)})})})]}),Dg=({isDarkMode:e,currentYear:t,currentMonth:r,selectedDay:n,dailyData:a,initializeDayData:s,scrollToRowId:i,setScrollToRowId:c,highlightedRowId:o,setHighlightedRowId:u,hasScrolledRef:f,monthNames:x,isWeekend:d,isHoliday:p,salaryCashPayments:g,calculateTotal:m,viewMode:h,frozenHeaders:_,setFrozenHeaders:k,scrollContainerRef:v,columnWidths:R,handleMouseDown:M,nameColumnWidth:S,transactionGroups:w,clientList:j,employeeList:E,handleNameChange:L,deleteRowAt:G,addRowAt:C,updateTransaction:I,panelHeight:q,setPanelHeight:ae,isFocusMode:ce,setIsFocusMode:de,setCurrentMonth:ie,setCurrentYear:B,setSelectedDay:re,daysInMonth:he,isCurrentMonth:F})=>{const U=`${t}-${r}-${n}`,P=new Date(t,r,n).getDay(),Q=a[U]||s(P),me=55,ye=450,oe=J.useRef(null),le=te=>{te.preventDefault(),oe.current={startY:te.clientY,startHeight:q},document.addEventListener("mousemove",Ee),document.addEventListener("mouseup",D)},Ee=te=>{if(!oe.current)return;const{startY:ne,startHeight:Ne}=oe.current,Be=te.clientY-ne,De=Ne+Be;De<me?(de(!0),document.removeEventListener("mousemove",Ee),document.removeEventListener("mouseup",D),oe.current=null):ae(Math.min(ye,De))},D=()=>{oe.current=null,document.removeEventListener("mousemove",Ee),document.removeEventListener("mouseup",D)};J.useEffect(()=>{if(!i)return;f.current=!0;const te=document.getElementById(i);te?setTimeout(()=>{te.scrollIntoView({behavior:"smooth",block:"center"}),u(i);const ne=setTimeout(()=>{u(null),c(null)},2500);return()=>clearTimeout(ne)},100):c(null)},[i,f,u,c]);const ke=new Date,Oe=n===ke.getDate()&&r===ke.getMonth()&&t===ke.getFullYear(),Ie=ke.getMonth()===0?11:ke.getMonth()-1,je=ke.getMonth()===0?ke.getFullYear()-1:ke.getFullYear(),xe=J.useMemo(()=>{const te=`${je}-${Ie}`,ne={};if(Object.values(g).forEach(Be=>{const De=Be[te];De&&Object.entries(De).forEach(([Ge,Ye])=>{Ye>0&&(ne[Ge]=(ne[Ge]||0)+Ye)})}),Object.keys(ne).length===0)return null;const Ne=m(He,He.map((Be,De)=>ne[De]||0));return{name:`Заплати за ${x[Ie]}`,group:"Заплати",bgnTotal:Ne,eurTotal:0,bgnCounts:ne,eurCounts:{}}},[g,je,Ie,x,m]),Ve=J.useMemo(()=>{const te=Q.income.reduce((De,Ge)=>De+(Ge?.bgnTotal||0),0),ne=Q.income.reduce((De,Ge)=>De+(Ge?.eurTotal||0),0);let Ne=Q.expense.reduce((De,Ge)=>De+(Ge?.bgnTotal||0),0),Be=Q.expense.reduce((De,Ge)=>De+(Ge?.eurTotal||0),0);return Oe&&xe&&(Ne+=xe.bgnTotal||0),{incomeBGN:te,incomeEUR:ne,expenseBGN:Ne,expenseEUR:Be}},[Q,Oe,xe]),er={bgn:Ve.incomeBGN-Ve.expenseBGN,eur:Ve.incomeEUR-Ve.expenseEUR},qe=J.useMemo(()=>{const te={income:{bgn:Array(He.length).fill(0),eur:Array(sr.length).fill(0)},expense:{bgn:Array(He.length).fill(0),eur:Array(sr.length).fill(0)},balance:{bgn:Array(He.length).fill(0),eur:Array(sr.length).fill(0)}};return Q.income.forEach(ne=>{ne&&(He.forEach((Ne,Be)=>{te.income.bgn[Be]+=ne.bgnCounts?.[Be]||0}),sr.forEach((Ne,Be)=>{te.income.eur[Be]+=ne.eurCounts?.[Be]||0}))}),Q.expense.forEach(ne=>{ne&&(He.forEach((Ne,Be)=>{te.expense.bgn[Be]+=ne.bgnCounts?.[Be]||0}),sr.forEach((Ne,Be)=>{te.expense.eur[Be]+=ne.eurCounts?.[Be]||0}))}),Oe&&xe&&He.forEach((ne,Ne)=>{te.expense.bgn[Ne]+=xe.bgnCounts?.[Ne]||0}),He.forEach((ne,Ne)=>{te.balance.bgn[Ne]=te.income.bgn[Ne]-te.expense.bgn[Ne]}),sr.forEach((ne,Ne)=>{te.balance.eur[Ne]=te.income.eur[Ne]-te.expense.eur[Ne]}),te},[Q,Oe,xe]);return l.jsxs("div",{className:`${e?"bg-gray-800 text-white":"bg-white"} rounded-lg shadow-md overflow-hidden h-full flex flex-col`,children:[l.jsxs("div",{style:{height:q},className:`relative flex flex-col transition-all duration-150 ease-out overflow-hidden border-b ${e?"border-gray-700":"border-gray-200"}`,children:[l.jsxs("div",{className:"flex-grow p-4 overflow-y-auto",children:[q>240&&l.jsx("div",{className:"mb-4",children:l.jsx(Og,{isDarkMode:e,currentMonth:r,setCurrentMonth:ie,currentYear:t,setCurrentYear:B,selectedDay:n,setSelectedDay:re,daysInMonth:he,monthNames:x,isCurrentMonth:F,isWeekend:d,isHoliday:p,hasScrolledRef:f})}),q>150&&l.jsx("div",{className:"flex justify-between items-center mb-4",children:l.jsxs("div",{children:[l.jsxs("h2",{className:"text-2xl font-bold",children:[n," ",x[r]," ",t,d(n)&&l.jsx("span",{className:"ml-2 text-orange-500 text-base",children:"(Почивен ден)"}),p(n)&&l.jsx("span",{className:"ml-2 text-red-500 text-base",children:"(Празник)"})]}),l.jsx("div",{className:"flex gap-4 items-center mt-2",children:[{label:"Баланс ЛВ",val:er.bgn,currency:"лв",pos:er.bgn>=0},{label:"Баланс EUR",val:er.eur,currency:"€",pos:er.eur>=0},{label:"Приходи",val:Ve.incomeBGN,currency:"лв",pos:!0},{label:"Разходи",val:Ve.expenseBGN,currency:"лв",pos:!1}].map(({label:te,val:ne,pos:Ne,currency:Be})=>l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-xl font-bold text-grey-600",children:te}),l.jsxs("div",{className:`text-lg font-bold ${Ne?"text-green-600":"text-red-600"}`,children:[Be==="€"?"€":"",ne.toFixed(2),Be==="лв"?" лв":""]})]},te))})]})}),l.jsx("div",{className:`w-full overflow-x-auto ${e?"bg-gray-900":"bg-gray-100"} sticky top-0 z-10`,children:l.jsx("table",{className:"w-full text-base",children:l.jsx("thead",{children:l.jsxs("tr",{className:"text-sm font-semibold",children:[l.jsx("th",{className:"px-2 py-2 text-center w-8"}),l.jsx("th",{className:"px-2 py-2 w-10"}),l.jsx("th",{className:"px-2 py-2 text-left",style:{width:R.group||120},children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{children:"Група"}),l.jsx("div",{className:"w-1 h-5"})]})}),l.jsx("th",{className:"px-3 py-2 text-left",style:{width:S},children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{children:"Име"}),l.jsx("div",{className:"w-1 h-5"})]})}),(h==="both"||h==="bgn")&&l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R.incomeTotalBGN||100},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("span",{}),l.jsx("div",{className:"w-1 h-5 ml-2"})]})}),(h==="both"||h==="bgn")&&He.map((te,ne)=>l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R[`bgn${ne}`]||60},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsxs("span",{className:"text-base",children:[te,"лв"]}),l.jsx("div",{className:"w-1 h-5 ml-2"})]})},`header-bgn-${te}`)),(h==="both"||h==="eur")&&l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R.incomeTotalEUR||100},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("span",{}),l.jsx("div",{className:"w-1 h-5 ml-2"})]})}),(h==="both"||h==="eur")&&sr.map((te,ne)=>l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R[`eur${ne}`]||60},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsxs("span",{className:"text-base",children:["€",te]}),l.jsx("div",{className:"w-1 h-5 ml-2"})]})},`header-eur-${te}`))]})})})})]}),l.jsx("div",{onMouseDown:le,className:`absolute bottom-0 left-0 w-full h-2 bg-gray-300 hover:bg-blue-500 transition-colors cursor-row-resize z-20 ${e?"bg-gray-600":"bg-gray-300"}`,title:"Оразмери панела"})]}),l.jsxs("div",{className:"flex-1 overflow-auto",ref:v,children:[l.jsxs("div",{className:`${e?"bg-green-900/20":"bg-green-50"} px-4 py-4`,children:[l.jsx("div",{className:"flex justify-between items-center mb-3",children:l.jsx("h3",{className:"text-xl font-bold text-green-600 px-2",children:"Приходи"})}),l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full text-base",children:[l.jsx("thead",{className:"sticky top-0 z-10",children:l.jsxs("tr",{className:`${e?"bg-gray-800":"bg-green-100"}`,children:[l.jsx("th",{className:"px-2 py-2 text-center w-8",children:"#"}),l.jsx("th",{className:"px-2 py-2 w-10"}),l.jsx("th",{className:"px-2 py-2 text-left",style:{width:R.group||120},children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{children:"Група"}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600",onMouseDown:te=>M(te,"group")})]})}),l.jsx("th",{className:"px-3 py-2 text-left",style:{width:S},children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{children:"Име"}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600",onMouseDown:te=>M(te,"name")})]})}),(h==="both"||h==="bgn")&&l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R.incomeTotalBGN||100},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("span",{children:"Приходи"}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2",onMouseDown:te=>M(te,"incomeTotalBGN")})]})}),(h==="both"||h==="bgn")&&He.map((te,ne)=>l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R[`bgn${ne}`]||60},"data-column":`bgn${ne}`,children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsxs("span",{className:"text-base",children:[te,"лв"]}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2",onMouseDown:Ne=>M(Ne,`bgn${ne}`)})]})},te)),(h==="both"||h==="eur")&&l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R.incomeTotalEUR||100},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("span",{children:"Приходи"}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2",onMouseDown:te=>M(te,"incomeTotalEUR")})]})}),(h==="both"||h==="eur")&&sr.map((te,ne)=>l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R[`eur${ne}`]||60},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsxs("span",{className:"text-base",children:["€",te]}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2",onMouseDown:Ne=>M(Ne,`eur${ne}`)})]})},te))]})}),l.jsxs("tbody",{children:[Q.income.map((te,ne)=>{if(!te)return null;const Ne=`${U}-income-${te.id}`,Be=o===Ne,De=te.isCarryOver,Ge=De?`${e?"bg-gray-700 text-gray-500":"bg-gray-200 text-gray-500"} cursor-not-allowed`:`${e?"bg-gray-700 border-gray-600":"bg-white"}`;return l.jsxs("tr",{id:Ne,className:`
                      transition-colors duration-300
                      ${e?"hover:bg-gray-700":"hover:bg-green-50"}
                      border-b ${e?"border-gray-700":"border-gray-200"}
                      ${Be?e?"bg-blue-800":"bg-blue-200":""}
                    `,children:[l.jsx("td",{className:"px-2 py-2 text-center text-xs text-gray-500",children:ne+1}),l.jsx("td",{className:"px-2 py-2",children:l.jsxs("div",{className:"flex gap-1.5",children:[l.jsx("button",{onClick:()=>G("income",ne),className:"text-red-500 hover:text-red-700 text-lg",title:"Изтрий ред",disabled:De,children:"-"}),l.jsx("button",{onClick:()=>C("income",ne),className:"text-green-500 hover:text-green-700 text-lg",title:"Добави ред",children:"+"})]})}),l.jsx("td",{className:"px-2 py-2",children:l.jsxs("select",{value:te.group||"",onChange:Ye=>I("income",ne,"group",Ye.target.value),className:`w-full px-2 py-1 text-base border rounded ${Ge}`,disabled:De,children:[l.jsx("option",{value:"",children:"-"}),w.map(Ye=>l.jsx("option",{value:Ye,children:Ye},Ye))]})}),l.jsx("td",{className:"px-3 py-2 relative",children:l.jsx(ti,{value:te.name||"",onChange:Ye=>L("income",ne,Ye),className:`w-full px-2 py-1 text-base border rounded ${Ge}`,suggestions:j,disabled:De,cellId:`income-${ne}-name`})}),(h==="both"||h==="bgn")&&l.jsxs("td",{className:"px-2 py-2 text-center font-semibold",children:[te.bgnTotal.toFixed(2)," лв"]}),(h==="both"||h==="bgn")&&He.map((Ye,Ze)=>l.jsx("td",{className:"px-2 py-2",children:l.jsx(rt,{type:"number",min:"0",value:te.bgnCounts[Ze]||"",onChange:fr=>I("income",ne,"bgnCount",["bgn",Ze,fr]),className:`w-full px-1 py-1 text-base text-center border rounded ${Ge}`,disabled:De,cellId:`income-${ne}-bgn-${Ze}`})},Ze)),(h==="both"||h==="eur")&&l.jsxs("td",{className:"px-2 py-2 text-center font-semibold",children:["€",te.eurTotal.toFixed(2)]}),(h==="both"||h==="eur")&&sr.map((Ye,Ze)=>l.jsx("td",{className:"px-2 py-2",children:l.jsx(rt,{type:"number",min:"0",value:te.eurCounts[Ze]||"",onChange:fr=>I("income",ne,"eurCount",["eur",Ze,fr]),className:`w-full px-1 py-1 text-base text-center border rounded ${Ge}`,disabled:De,cellId:`income-${ne}-eur-${Ze}`})},Ze))]},te.id)}),l.jsxs("tr",{className:`${e?"bg-gray-700":"bg-gray-100"} text-sm font-semibold`,children:[l.jsx("td",{colSpan:"4",className:"px-3 py-1"}),(h==="both"||h==="bgn")&&l.jsx("td",{className:"px-2 py-1 text-center"}),(h==="both"||h==="bgn")&&He.map((te,ne)=>l.jsxs("td",{className:"px-2 py-1 text-center text-gray-600",children:[te,"лв"]},`label-bgn-${ne}`)),(h==="both"||h==="eur")&&l.jsx("td",{className:"px-2 py-1 text-center"}),(h==="both"||h==="eur")&&sr.map((te,ne)=>l.jsxs("td",{className:"px-2 py-1 text-center text-gray-600",children:["€",te]},`label-eur-${ne}`))]}),l.jsxs("tr",{className:`font-bold ${e?"bg-gray-800":"bg-green-200"} ${_.income?"sticky top-0 z-20":""}`,children:[l.jsx("td",{colSpan:"1",className:"px-2 py-2",children:l.jsx("button",{onClick:()=>k({..._,income:!_.income}),className:`p-1 rounded ${_.income?"text-blue-600":"text-gray-600"} hover:bg-gray-200`,title:_.income?"Отключи ред":"Заключи ред",children:_.income?l.jsx(Mn,{className:"w-4 h-4"}):l.jsx($n,{className:"w-4 h-4"})})}),l.jsx("td",{colSpan:"3",className:"px-3 py-2 text-right",children:"Всичко Приходи"}),(h==="both"||h==="bgn")&&l.jsxs("td",{className:"px-2 py-2 text-center",children:[Ve.incomeBGN.toFixed(2)," лв"]}),(h==="both"||h==="bgn")&&He.map((te,ne)=>l.jsx("td",{className:"px-2 py-2 text-center text-green-700",children:qe.income.bgn[ne]>0?qe.income.bgn[ne]:""},`total-bgn-${ne}`)),(h==="both"||h==="eur")&&l.jsxs("td",{className:"px-2 py-2 text-center",children:["€",Ve.incomeEUR.toFixed(2)]}),(h==="both"||h==="eur")&&sr.map((te,ne)=>l.jsx("td",{className:"px-2 py-2 text-center text-green-700",children:qe.income.eur[ne]>0?qe.income.eur[ne]:""},`total-eur-${ne}`))]})]})]})})]}),l.jsxs("div",{className:`${e?"bg-red-900/20":"bg-red-50"} px-4 py-4 mt-4`,children:[l.jsx("div",{className:"flex justify-between items-center mb-3",children:l.jsx("h3",{className:"text-xl font-bold text-red-600 px-2",children:"Разходи"})}),l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full text-base",children:[l.jsx("thead",{className:"sticky top-0 z-10",children:l.jsxs("tr",{className:`${e?"bg-gray-800":"bg-red-100"}`,children:[l.jsx("th",{className:"px-2 py-2 text-center w-8",children:"#"}),l.jsx("th",{className:"px-2 py-2 w-10"}),l.jsx("th",{className:"px-2 py-2 text-left",style:{width:R.group||120},children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{children:"Група"}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600",onMouseDown:te=>M(te,"group")})]})}),l.jsx("th",{className:"px-3 py-2 text-left",style:{width:S},children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{children:"Име"}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600",onMouseDown:te=>M(te,"name")})]})}),(h==="both"||h==="bgn")&&l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R.expenseTotalBGN||100},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("span",{children:"Разходи"}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2",onMouseDown:te=>M(te,"expenseTotalBGN")})]})}),(h==="both"||h==="bgn")&&He.map((te,ne)=>l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R[`bgn${ne}`]||60},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsxs("span",{className:"text-base",children:[te,"лв"]}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2",onMouseDown:Ne=>M(Ne,`bgn${ne}`)})]})},te)),(h==="both"||h==="eur")&&l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R.expenseTotalEUR||100},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsx("span",{children:"Разходи"}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2",onMouseDown:te=>M(te,"expenseTotalEUR")})]})}),(h==="both"||h==="eur")&&sr.map((te,ne)=>l.jsx("th",{className:"px-2 py-2 text-center",style:{width:R[`eur${ne}`]||60},children:l.jsxs("div",{className:"flex items-center justify-center",children:[l.jsxs("span",{className:"text-base",children:["€",te]}),l.jsx("div",{className:"w-1 h-5 bg-gray-400 cursor-col-resize hover:bg-gray-600 ml-2",onMouseDown:Ne=>M(Ne,`eur${ne}`)})]})},te))]})}),l.jsxs("tbody",{children:[Oe&&l.jsx(Cg,{rowData:xe,isDarkMode:e,viewMode:h,columnWidths:R}),Q.expense.map((te,ne)=>{if(!te)return null;const Ne=`${U}-expense-${te.id}`,Be=o===Ne;return l.jsxs("tr",{id:Ne,className:`
                      transition-colors duration-300
                      ${e?"hover:bg-gray-700":"hover:bg-red-50"}
                      border-b ${e?"border-gray-700":"border-gray-200"}
                      ${Be?e?"bg-blue-800":"bg-blue-200":""}
                    `,children:[l.jsx("td",{className:"px-2 py-2 text-center text-xs text-gray-500",children:ne+1}),l.jsx("td",{className:"px-2 py-2",children:l.jsxs("div",{className:"flex gap-1.5",children:[l.jsx("button",{onClick:()=>G("expense",ne),className:"text-red-500 hover:text-red-700 text-lg",title:"Изтрий ред",children:"-"}),l.jsx("button",{onClick:()=>C("expense",ne),className:"text-green-500 hover:text-green-700 text-lg",title:"Добави ред",children:"+"})]})}),l.jsx("td",{className:"px-2 py-2",children:l.jsxs("select",{value:te.group||"",onChange:De=>I("expense",ne,"group",De.target.value),className:`w-full px-2 py-1 text-base ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded`,children:[l.jsx("option",{value:"",children:"-"}),w.map(De=>l.jsx("option",{value:De,children:De},De))]})}),l.jsx("td",{className:"px-3 py-2 relative",children:l.jsx(ti,{value:te.name||"",onChange:De=>L("expense",ne,De),className:`w-full px-2 py-1 text-base ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded`,suggestions:[...j,...E.map(De=>({name:De.name,group:"Заплати"}))],cellId:`expense-${ne}-name`})}),(h==="both"||h==="bgn")&&l.jsxs("td",{className:"px-2 py-2 text-center font-semibold",children:[te.bgnTotal.toFixed(2)," лв"]}),(h==="both"||h==="bgn")&&He.map((De,Ge)=>l.jsx("td",{className:"px-2 py-2",children:l.jsx(rt,{type:"number",min:"0",value:te.bgnCounts[Ge]||"",onChange:Ye=>I("expense",ne,"bgnCount",["bgn",Ge,Ye]),className:`w-full px-1 py-1 text-base text-center ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded`,cellId:`expense-${ne}-bgn-${Ge}`})},Ge)),(h==="both"||h==="eur")&&l.jsxs("td",{className:"px-2 py-2 text-center font-semibold",children:["€",te.eurTotal.toFixed(2)]}),(h==="both"||h==="eur")&&sr.map((De,Ge)=>l.jsx("td",{className:"px-2 py-2",children:l.jsx(rt,{type:"number",min:"0",value:te.eurCounts[Ge]||"",onChange:Ye=>I("expense",ne,"eurCount",["eur",Ge,Ye]),className:`w-full px-1 py-1 text-base text-center ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded`,cellId:`expense-${ne}-eur-${Ge}`})},Ge))]},te.id)}),l.jsxs("tr",{className:`${e?"bg-gray-700":"bg-gray-100"} text-sm font-semibold`,children:[l.jsx("td",{colSpan:"4",className:"px-3 py-1"}),(h==="both"||h==="bgn")&&l.jsx("td",{className:"px-2 py-1 text-center"}),(h==="both"||h==="bgn")&&He.map((te,ne)=>l.jsxs("td",{className:"px-2 py-1 text-center text-gray-600",children:[te,"лв"]},`label-bgn-${ne}`)),(h==="both"||h==="eur")&&l.jsx("td",{className:"px-2 py-1 text-center"}),(h==="both"||h==="eur")&&sr.map((te,ne)=>l.jsxs("td",{className:"px-2 py-1 text-center text-gray-600",children:["€",te]},`label-eur-${ne}`))]}),l.jsxs("tr",{className:`font-bold ${e?"bg-gray-800":"bg-red-200"} ${_.expense?"sticky top-0 z-20":""}`,children:[l.jsx("td",{colSpan:"1",className:"px-2 py-2",children:l.jsx("button",{onClick:()=>k({..._,expense:!_.expense}),className:`p-1 rounded ${_.expense?"text-blue-600":"text-gray-600"} hover:bg-gray-200`,title:_.expense?"Отключи ред":"Заключи ред",children:_.expense?l.jsx(Mn,{className:"w-4 h-4"}):l.jsx($n,{className:"w-4 h-4"})})}),l.jsx("td",{colSpan:"3",className:"px-3 py-2 text-right",children:"Всички Разходи"}),(h==="both"||h==="bgn")&&l.jsxs("td",{className:"px-2 py-2 text-center",children:[Ve.expenseBGN.toFixed(2)," лв"]}),(h==="both"||h==="bgn")&&He.map((te,ne)=>l.jsx("td",{className:"px-2 py-2 text-center text-red-700",children:qe.expense.bgn[ne]>0?qe.expense.bgn[ne]:""},`total-bgn-${ne}`)),(h==="both"||h==="eur")&&l.jsxs("td",{className:"px-2 py-2 text-center",children:["€",Ve.expenseEUR.toFixed(2)]}),(h==="both"||h==="eur")&&sr.map((te,ne)=>l.jsx("td",{className:"px-2 py-2 text-center text-red-700",children:qe.expense.eur[ne]>0?qe.expense.eur[ne]:""},`total-eur-${ne}`))]})]}),l.jsxs("tfoot",{children:[l.jsxs("tr",{className:`${e?"bg-gray-700":"bg-gray-100"} text-sm font-semibold`,children:[l.jsx("td",{colSpan:"4",className:"px-3 py-1"}),(h==="both"||h==="bgn")&&l.jsx("td",{className:"px-2 py-1 text-center"}),(h==="both"||h==="bgn")&&He.map((te,ne)=>l.jsxs("td",{className:"px-2 py-1 text-center text-gray-600",children:[te,"лв"]},`label-bgn-${ne}`)),(h==="both"||h==="eur")&&l.jsx("td",{className:"px-2 py-1 text-center"}),(h==="both"||h==="eur")&&sr.map((te,ne)=>l.jsxs("td",{className:"px-2 py-1 text-center text-gray-600",children:["€",te]},`label-eur-${ne}`))]}),l.jsxs("tr",{className:`font-bold text-lg ${e?"bg-gray-900":"bg-blue-200"} ${_.balance?"sticky bottom-0 z-20":""}`,children:[l.jsx("td",{colSpan:"1",className:"px-2 py-3",children:l.jsx("button",{onClick:()=>k({..._,balance:!_.balance}),className:`p-1 rounded ${_.balance?"text-blue-600":"text-gray-600"} hover:bg-gray-200`,title:_.balance?"Отключи ред":"Заключи ред",children:_.balance?l.jsx(Mn,{className:"w-4 h-4"}):l.jsx($n,{className:"w-4 h-4"})})}),l.jsx("td",{colSpan:"3",className:"px-3 py-3 text-right",children:"Дневен Баланс"}),(h==="both"||h==="bgn")&&l.jsxs("td",{className:`px-2 py-3 text-center ${er.bgn>=0?"text-green-600":"text-red-600"}`,children:[er.bgn.toFixed(2)," лв"]}),(h==="both"||h==="bgn")&&He.map((te,ne)=>l.jsx("td",{className:`px-2 py-3 text-center ${qe.balance.bgn[ne]>=0?e?"text-blue-300":"text-blue-800":"text-red-600"}`,children:qe.balance.bgn[ne]!==0?qe.balance.bgn[ne]:""},`balance-bgn-${ne}`)),(h==="both"||h==="eur")&&l.jsxs("td",{className:`px-2 py-3 text-center ${er.eur>=0?"text-green-600":"text-red-600"}`,children:["€",er.eur.toFixed(2)]}),(h==="both"||h==="eur")&&sr.map((te,ne)=>l.jsx("td",{className:`px-2 py-3 text-center ${qe.balance.eur[ne]>=0?e?"text-blue-300":"text-blue-800":"text-red-600"}`,children:qe.balance.eur[ne]!==0?qe.balance.eur[ne]:""},`balance-eur-${ne}`))]})]})]})})]})]})]})},Rg=({isDarkMode:e,analysisFilters:t,setAnalysisFilters:r,allTransactions:n,currentYear:a,currentMonth:s,dailyData:i,transactionGroups:c,setActiveTab:o,setCurrentYear:u,setCurrentMonth:f,setSelectedDay:x,setScrollToRowId:d,hasScrolledRef:p,salaryCashPayments:g,monthNames:m,calculateTotal:h})=>{const _=J.useMemo(()=>{const E=[],L=new Set;return Object.entries(g).forEach(([G,C])=>{Object.keys(C).forEach(I=>{if(!L.has(I)){L.add(I);const[q,ae]=I.split("-").map(Number);let ce={};if(Object.values(g).forEach(de=>{const ie=de[I];ie&&Object.entries(ie).forEach(([B,re])=>{re>0&&(ce[B]=(ce[B]||0)+re)})}),Object.keys(ce).length>0){const de=h(He,He.map((B,re)=>ce[re]||0)),ie=new Date(q,ae+2,0);E.push({id:`auto-salary-${I}`,uniqueKey:`auto-salary-${I}`,type:"expense",name:`Заплати за ${m[ae]}`,bgnTotal:de,eurTotal:0,year:ie.getFullYear(),month:ie.getMonth(),day:ie.getDate(),date:ie,isAutoGenerated:!0})}}})}),E},[g,m,h]),k=J.useMemo(()=>{const E=parseFloat(t.amountSearch),L=!Number.isNaN(E),G=[...n,..._];if(t.showOnlyAutoSalaries)return _.sort((I,q)=>q.date-I.date);let C=G.filter(I=>{if(t.searchName&&!ma(I.name,t.searchName)||t.selectedGroups.length&&!t.selectedGroups.includes(I.group)||t.excludedGroups.length&&t.excludedGroups.includes(I.group)||t.excludeCarryOver&&I.isCarryOver)return!1;const q=new Date,ae=q.getDay(),ce=new Date(q);ce.setDate(q.getDate()-(ae===0?6:ae-1)),ce.setHours(0,0,0,0);const de=new Date(ce);if(de.setDate(ce.getDate()+6),de.setHours(23,59,59,999),t.dateRange==="month"){if(!(I.year===a&&I.month===s))return!1}else if(t.dateRange==="week"){if(!(I.date>=ce&&I.date<=de))return!1}else if(t.dateRange==="lastMonth"){const ie=s===0?11:s-1,B=s===0?a-1:a;if(!(I.year===B&&I.month===ie))return!1}else if(t.dateRange==="last3Months"){const ie=new Date(a,s-2,1),B=new Date(a,s+1,0,23,59,59,999);if(!(I.date>=ie&&I.date<=B))return!1}else if(t.dateRange==="year"){if(I.year!==a)return!1}else if(t.dateRange==="q1"){if(!(I.year===a&&I.month>=0&&I.month<=2))return!1}else if(t.dateRange==="q2"){if(!(I.year===a&&I.month>=3&&I.month<=5))return!1}else if(t.dateRange==="q3"){if(!(I.year===a&&I.month>=6&&I.month<=8))return!1}else if(t.dateRange==="q4"&&!(I.year===a&&I.month>=9&&I.month<=11))return!1;return!(L&&!(I.bgnTotal?.toFixed(2).startsWith(t.amountSearch)||I.eurTotal?.toFixed(2).startsWith(t.amountSearch)))});return C=C.sort((I,q)=>{if(L){const ae=I.bgnTotal||I.eurTotal||0,ce=q.bgnTotal||q.eurTotal||0;return Math.abs(ae-E)-Math.abs(ce-E)}return q.date-I.date}),C},[n,_,t,a,s]),v=J.useMemo(()=>{const E={incomeBGN:0,incomeEUR:0,expenseBGN:0,expenseEUR:0};return k.forEach(L=>{L.type==="income"?(E.incomeBGN+=L.bgnTotal||0,E.incomeEUR+=L.eurTotal||0):L.type==="expense"&&(E.expenseBGN+=L.bgnTotal||0,E.expenseEUR+=L.eurTotal||0)}),E},[k]),R=v.incomeBGN-v.expenseBGN,M=v.incomeEUR-v.expenseEUR,S=E=>{r(L=>({...L,selectedGroups:L.selectedGroups.includes(E)?L.selectedGroups.filter(G=>G!==E):[...L.selectedGroups,E],showOnlyAutoSalaries:!1}))},w=E=>{r(L=>({...L,excludedGroups:L.excludedGroups.includes(E)?L.excludedGroups.filter(G=>G!==E):[...L.excludedGroups,E],showOnlyAutoSalaries:!1}))},j=E=>{if(E.isAutoGenerated){p.current=!1,o("daily"),u(E.year),f(E.month),x(E.day),d(null);return}p.current=!0,o("daily"),u(E.year),f(E.month),x(E.day),d(E.uniqueKey)};return l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsxs("h3",{className:"font-bold text-lg mb-3 flex items-center",children:[l.jsx(li,{className:"w-5 h-5 mr-2"}),"Филтри"]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[l.jsx(rt,{type:"text",value:t.searchName,onChange:E=>r(L=>({...L,searchName:E,showOnlyAutoSalaries:!1})),placeholder:"Търси по име...",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2 w-full`}),l.jsx(rt,{type:"number",value:t.amountSearch,onChange:E=>r(L=>({...L,amountSearch:E,showOnlyAutoSalaries:!1})),placeholder:"Сума (напр. 200)",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2 w-full`}),l.jsxs("select",{value:t.dateRange,onChange:E=>r(L=>({...L,dateRange:E.target.value,showOnlyAutoSalaries:!1})),className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`,children:[l.jsx("option",{value:"month",children:"Текущ месец"}),l.jsx("option",{value:"week",children:"Тази седмица"}),l.jsx("option",{value:"lastMonth",children:"Миналия месец"}),l.jsx("option",{value:"last3Months",children:"Последни 3 месеца"}),l.jsx("option",{value:"year",children:"Текуща година"}),l.jsx("option",{value:"q1",children:"Q1 (Ян-Мар)"}),l.jsx("option",{value:"q2",children:"Q2 (Апр-Юни)"}),l.jsx("option",{value:"q3",children:"Q3 (Юли-Сеп)"}),l.jsx("option",{value:"q4",children:"Q4 (Окт-Дек)"}),l.jsx("option",{value:"all",children:"Всички"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"text-sm font-semibold mb-2 block",children:"Филтрирай по групи:"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:c.map(E=>l.jsx("button",{onClick:()=>S(E),className:`px-3 py-1 rounded text-sm ${t.selectedGroups.includes(E)?"bg-blue-500 text-white":e?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:E},E))})]}),l.jsxs("div",{children:[l.jsx("label",{className:"text-sm font-semibold mb-2 block",children:"Изключи групи:"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:c.map(E=>l.jsx("button",{onClick:()=>w(E),className:`px-3 py-1 rounded text-sm ${t.excludedGroups.includes(E)?"bg-red-500 text-white":e?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:E},E))}),l.jsxs("div",{className:"flex items-center gap-4 mt-3",children:[l.jsxs("label",{className:"inline-flex items-center gap-2",children:[l.jsx("input",{type:"checkbox",checked:t.excludeCarryOver,onChange:E=>r(L=>({...L,excludeCarryOver:E.target.checked,showOnlyAutoSalaries:!1}))}),"Изключи „Пренос“"]}),l.jsxs("button",{onClick:()=>r(E=>({searchName:"",selectedGroups:[],excludedGroups:[],dateRange:"all",amountSearch:"",excludeCarryOver:!0,showOnlyAutoSalaries:!E.showOnlyAutoSalaries})),className:`px-3 py-1 rounded text-sm ${t.showOnlyAutoSalaries?"bg-purple-600 text-white":e?"bg-gray-600 hover:bg-gray-500":"bg-gray-300 hover:bg-gray-400"}`,children:[l.jsx(ms,{className:"w-4 h-4 mr-1 inline-block"}),"Само заплати"]})]})]})]})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[l.jsxs("div",{className:`${e?"bg-green-900":"bg-green-100"} rounded-lg p-4`,children:[l.jsx("h4",{className:`font-semibold ${e?"text-green-200":"text-green-800"} mb-2`,children:"Общо приходи"}),l.jsxs("div",{className:`text-2xl font-bold ${e?"text-green-100":"text-green-900"}`,children:[v.incomeBGN.toFixed(2)," лв"]}),l.jsxs("div",{className:`text-lg font-semibold ${e?"text-green-200":"text-green-800"}`,children:["€",v.incomeEUR.toFixed(2)]})]}),l.jsxs("div",{className:`${e?"bg-red-900":"bg-red-100"} rounded-lg p-4`,children:[l.jsx("h4",{className:`font-semibold ${e?"text-red-200":"text-red-800"} mb-2`,children:"Общо разходи"}),l.jsxs("div",{className:`text-2xl font-bold ${e?"text-red-100":"text-red-900"}`,children:[v.expenseBGN.toFixed(2)," лв"]}),l.jsxs("div",{className:`text-lg font-semibold ${e?"text-red-200":"text-red-800"}`,children:["€",v.expenseEUR.toFixed(2)]})]}),l.jsxs("div",{className:`${R>=0?e?"bg-blue-900":"bg-blue-100":e?"bg-orange-900":"bg-orange-100"} rounded-lg p-4`,children:[l.jsx("h4",{className:`font-semibold ${R>=0?e?"text-blue-200":"text-blue-800":e?"text-orange-200":"text-orange-800"} mb-2`,children:"Нетен баланс"}),l.jsxs("div",{className:`text-2xl font-bold ${R>=0?e?"text-blue-100":"text-blue-900":e?"text-orange-100":"text-orange-900"}`,children:[R.toFixed(2)," лв"]}),l.jsxs("div",{className:`text-lg font-semibold ${M>=0?e?"text-blue-200":"text-blue-800":e?"text-orange-200":"text-orange-800"}`,children:["€",M.toFixed(2)]})]})]}),l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsx("h3",{className:"font-bold text-lg mb-3",children:"Транзакции"}),l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full text-sm",children:[l.jsx("thead",{children:l.jsxs("tr",{className:`border-b ${e?"border-gray-700":"border-gray-200"}`,children:[l.jsx("th",{className:"text-left py-2 px-2",children:"Дата"}),l.jsx("th",{className:"text-left py-2 px-2",children:"Тип"}),l.jsx("th",{className:"text-left py-2 px-2",children:"Група"}),l.jsx("th",{className:"text-left py-2 px-2",children:"Име"}),l.jsx("th",{className:"text-center py-2 px-2",children:"BGN"}),l.jsx("th",{className:"text-center py-2 px-2",children:"EUR"}),l.jsx("th",{className:"text-center py-2 px-2",children:"Действие"})]})}),l.jsx("tbody",{children:k.map((E,L)=>l.jsxs("tr",{className:`border-b ${E.isAutoGenerated?e?"bg-purple-900/40":"bg-purple-100":""} ${e?"border-gray-700 hover:bg-gray-700":"border-gray-200 hover:bg-gray-50"}`,children:[l.jsx("td",{className:"py-2 px-2",children:`${E.day}.${E.month+1}.${E.year}`}),l.jsx("td",{className:`py-2 px-2 ${E.type==="income"?"text-green-600":"text-red-600"}`,children:E.type==="income"?"Приход":"Разход"}),l.jsx("td",{className:"py-2 px-2",children:E.group}),l.jsx("td",{className:"py-2 px-2",children:E.name}),l.jsx("td",{className:"py-2 text-center px-2",children:(E.bgnTotal||0).toFixed(2)}),l.jsx("td",{className:"py-2 text-center px-2",children:(E.eurTotal||0).toFixed(2)}),l.jsx("td",{className:"py-1 px-1 text-center",children:l.jsx("button",{onClick:()=>j(E),className:"px-4 py-2 rounded-full text-blue-500 hover:text-blue-700 hover:bg-blue-100",title:E.isAutoGenerated?"Отиди до деня на плащане":"Отиди до транзакцията",children:l.jsx(oi,{className:"w-4 h-4"})})})]},E.uniqueKey||L))})]})})]})]})},Ig=({isDarkMode:e,departments:t,setDepartments:r,employeeList:n,setEmployeeList:a})=>{const[s,i]=J.useState(!1),[c,o]=J.useState(""),[u,f]=J.useState(null),[x,d]=J.useState(""),p=()=>{c.trim()&&!t[c]&&(r({...t,[c]:{workDays:["Monday","Tuesday","Wednesday","Thursday","Friday"],halfDays:[]}}),o(""),i(!1))},g=()=>{if(x.trim()&&u&&x!==u){const h={},_=n.map(k=>k.department===u?{...k,department:x}:k);Object.entries(t).forEach(([k,v])=>{k===u?h[x]=v:h[k]=v}),r(h),a(_),f(null),d("")}},m=h=>{if(n.some(v=>v.department===h)){alert("Не може да изтриете отдел с служители!");return}const{[h]:_,...k}=t;r(k)};return l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsxs("div",{className:"flex justify-between items-center mb-4",children:[l.jsx("h3",{className:"font-bold text-lg",children:"Управление на отдели"}),l.jsxs("button",{onClick:()=>i(!s),className:"bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center",children:[l.jsx(Aa,{className:"w-4 h-4 mr-1"}),"Добави отдел"]})]}),s&&l.jsxs("div",{className:"flex gap-2 mb-4",children:[l.jsx("input",{type:"text",value:c,onChange:h=>o(h.target.value),onKeyPress:h=>h.key==="Enter"&&p(),placeholder:"Име на нов отдел",className:`flex-1 ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`}),l.jsx("button",{onClick:p,className:"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600",children:"Добави"})]}),l.jsx("div",{className:"space-y-2",children:Object.keys(t).map(h=>l.jsx("div",{className:`flex items-center justify-between p-2 border rounded ${e?"border-gray-700":""}`,children:u===h?l.jsxs(l.Fragment,{children:[l.jsx("input",{type:"text",value:x,onChange:_=>d(_.target.value),onKeyPress:_=>_.key==="Enter"&&g(),className:`flex-1 ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1`}),l.jsxs("div",{className:"flex gap-2 ml-2",children:[l.jsx("button",{onClick:g,className:"text-green-500 hover:text-green-700",children:"✓"}),l.jsx("button",{onClick:()=>{f(null),d("")},className:"text-red-500 hover:text-red-700",children:"✗"})]})]}):l.jsxs(l.Fragment,{children:[l.jsx("span",{children:h}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("button",{onClick:()=>{f(h),d(h)},className:"text-blue-500 hover:text-blue-700",children:l.jsx(hn,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>m(h),className:"text-red-500 hover:text-red-700",children:l.jsx(ia,{className:"w-4 h-4"})})]})]})},h))})]})},jg=({isDarkMode:e,departments:t,setDepartments:r,employeeList:n,setEmployeeList:a,employeeTransactions:s,setEmployeeTransactions:i,setClientList:c,leaveData:o,currentYear:u,importEmployeesFromFile:f})=>{const[x,d]=J.useState(null),[p,g]=J.useState(!1),[m,h]=J.useState({name:"",totalSalary:"",bankSalary:"",socialSecurity:"",department:Object.keys(t)[0]||"Администрация",annualLeave:""}),[_,k]=J.useState(null),[v,R]=J.useState(null),M=C=>{k(C.id),R({...C})},S=()=>{k(null),R(null)},w=C=>{a(n.map(I=>I.id===C?v:I)),S()},j=(C,I)=>{R(q=>({...q,[C]:I}))},E=C=>{d(x===C?null:C)},L=()=>{if(m.name&&m.totalSalary){const C={id:Date.now(),name:m.name,totalSalary:parseFloat(m.totalSalary)||0,bankSalary:parseFloat(m.bankSalary)||0,socialSecurity:parseFloat(m.socialSecurity)||0,department:m.department,annualLeave:parseInt(m.annualLeave)||0};a([...n,C]),c(I=>[...new Set([...I,{name:C.name,group:"Заплати"}])]),h({name:"",totalSalary:"",bankSalary:"",socialSecurity:"",department:Object.keys(t)[0]||"Администрация",annualLeave:""}),g(!1)}},G=C=>{if(window.confirm("Сигурни ли сте, че искате да изтриете този служител?")){const I=n.find(q=>q.id===C);a(n.filter(q=>q.id!==C)),i(q=>{const ae={...q};return delete ae[C],ae}),I&&c(q=>q.filter(ae=>ae.name!==I.name))}};return l.jsxs("div",{className:"space-y-4",children:[l.jsx(Ig,{isDarkMode:e,departments:t,setDepartments:r,employeeList:n,setEmployeeList:a}),l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsxs("div",{className:"flex justify-between items-center mb-4",children:[l.jsxs("h3",{className:"font-bold text-lg flex items-center",children:[l.jsx(ii,{className:"w-5 h-5 mr-2"}),"Списък служители"]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsxs("label",{className:"bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 flex items-center cursor-pointer",children:[l.jsx(ni,{className:"w-4 h-4 mr-1"}),"Импорт",l.jsx("input",{type:"file",accept:".csv,.xlsx,.xls",onChange:f,className:"hidden"})]}),l.jsxs("button",{onClick:()=>g(!p),className:"bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center",children:[l.jsx(Aa,{className:"w-4 h-4 mr-1"}),"Добави служител"]})]})]}),p&&l.jsxs("div",{className:"border-t pt-3 mt-3 space-y-3",children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-3",children:[l.jsx("input",{type:"text",value:m.name,onChange:C=>h({...m,name:C.target.value}),placeholder:"Име на служител",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`}),l.jsx("input",{type:"number",value:m.totalSalary,onChange:C=>h({...m,totalSalary:C.target.value}),placeholder:"Обща заплата (BGN)",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`}),l.jsx("input",{type:"number",value:m.bankSalary,onChange:C=>h({...m,bankSalary:C.target.value}),placeholder:"Заплата по банка (BGN)",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`}),l.jsx("input",{type:"number",value:m.socialSecurity,onChange:C=>h({...m,socialSecurity:C.target.value}),placeholder:"Осигуровки (BGN)",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`}),l.jsx("select",{value:m.department,onChange:C=>h({...m,department:C.target.value}),className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`,children:Object.keys(t).map(C=>l.jsx("option",{value:C,children:C},C))}),l.jsx("input",{type:"number",value:m.annualLeave,onChange:C=>h({...m,annualLeave:C.target.value}),placeholder:"Годишен отпуск",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`})]}),l.jsx("button",{onClick:L,className:"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600",children:"Запази"})]})]}),n.map(C=>{const I=_===C.id,q=s[C.id]||[],ae=x===C.id,ce=Object.values(o[C.id]?.[u]||{}).reduce((B,re)=>B+(re||0),0),de=(C.annualLeave||0)-ce,ie=(C.totalSalary||0)-(C.socialSecurity||0)-(C.bankSalary||0);return I?l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-3",children:[l.jsxs("div",{children:[l.jsx("label",{className:`block text-sm font-medium mb-1 ${e?"text-gray-300":"text-gray-700"}`,children:"Име"}),l.jsx("input",{type:"text",value:v.name,onChange:B=>j("name",B.target.value),placeholder:"Име",className:`${e?"bg-gray-700 border-gray-600":""} border rounded px-3 py-2 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:`block text-sm font-medium mb-1 ${e?"text-gray-300":"text-gray-700"}`,children:"Обща заплата"}),l.jsx("input",{type:"number",value:v.totalSalary,onChange:B=>j("totalSalary",parseFloat(B.target.value)||0),placeholder:"Обща заплата",className:`${e?"bg-gray-700 border-gray-600":""} border rounded px-3 py-2 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:`block text-sm font-medium mb-1 ${e?"text-gray-300":"text-gray-700"}`,children:"Заплата по банка"}),l.jsx("input",{type:"number",value:v.bankSalary,onChange:B=>j("bankSalary",parseFloat(B.target.value)||0),placeholder:"Заплата по банка",className:`${e?"bg-gray-700 border-gray-600":""} border rounded px-3 py-2 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:`block text-sm font-medium mb-1 ${e?"text-gray-300":"text-gray-700"}`,children:"Осигуровки"}),l.jsx("input",{type:"number",value:v.socialSecurity,onChange:B=>j("socialSecurity",parseFloat(B.target.value)||0),placeholder:"Осигуровки",className:`${e?"bg-gray-700 border-gray-600":""} border rounded px-3 py-2 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:`block text-sm font-medium mb-1 ${e?"text-gray-300":"text-gray-700"}`,children:"Отдел"}),l.jsx("select",{value:v.department,onChange:B=>j("department",B.target.value),className:`${e?"bg-gray-700 border-gray-600":""} border rounded px-3 py-2 w-full`,children:Object.keys(t).map(B=>l.jsx("option",{value:B,children:B},B))})]}),l.jsxs("div",{children:[l.jsx("label",{className:`block text-sm font-medium mb-1 ${e?"text-gray-300":"text-gray-700"}`,children:"Годишен отпуск"}),l.jsx("input",{type:"number",value:v.annualLeave,onChange:B=>j("annualLeave",parseInt(B.target.value)||0),placeholder:"Годишен отпуск",className:`${e?"bg-gray-700 border-gray-600":""} border rounded px-3 py-2 w-full`})]})]}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("button",{onClick:()=>w(C.id),className:"bg-green-500 text-white px-4 py-1 rounded hover:bg-green-600",children:"Запази"}),l.jsx("button",{onClick:S,className:"bg-gray-500 text-white px-4 py-1 rounded hover:bg-gray-600",children:"Отказ"})]})]},C.id):l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsx("button",{onClick:()=>E(C.id),className:`p-1 ${e?"hover:bg-gray-700":"hover:bg-gray-100"} rounded`,children:ae?l.jsx(fn,{className:"w-5 h-5"}):l.jsx(un,{className:"w-5 h-5"})}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-semibold",children:C.name}),l.jsxs("div",{className:"text-sm text-gray-500 flex flex-wrap gap-x-4",children:[l.jsx("span",{children:C.department}),l.jsxs("span",{children:["• Общо: ",(C.totalSalary||0).toFixed(2)," лв"]}),l.jsxs("span",{children:["• По банка: ",(C.bankSalary||0).toFixed(2)," лв"]}),l.jsxs("span",{className:"text-red-500",children:["• Осигуровки: ",(C.socialSecurity||0).toFixed(2)," лв"]}),l.jsxs("span",{className:"text-green-600 font-semibold",children:["• В брой: ",ie.toFixed(2)," лв"]}),l.jsxs("span",{className:"text-purple-500",children:["• Отпуск: ",de," / ",C.annualLeave||0," (изп. ",ce,")"]})]})]})]}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("button",{onClick:()=>M(C),className:"text-blue-500 hover:text-blue-700",children:l.jsx(hn,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>G(C.id),className:"text-red-500 hover:text-red-700",children:l.jsx(ia,{className:"w-4 h-4"})})]})]}),ae&&l.jsxs("div",{className:"mt-4 pl-10",children:[l.jsx("h5",{className:"font-semibold mb-2",children:"Транзакции:"}),q.length>0?l.jsx("div",{className:"space-y-1",children:q.map((B,re)=>l.jsxs("div",{className:`flex justify-between text-sm p-2 rounded ${e?"bg-gray-700":"bg-gray-100"}`,children:[l.jsx("span",{children:B.date}),l.jsx("span",{children:B.type}),l.jsxs("span",{className:"font-mono",children:[B.amount.toFixed(2)," лв"]})]},re))}):l.jsx("p",{className:"text-sm text-gray-500",children:"Няма транзакции"})]})]},C.id)})]})},Pg=({popupData:e,setPopupData:t,onClose:r,allTransactions:n,monthNames:a,isDarkMode:s})=>{const{employee:i,month:c,year:o}=e,u=J.useMemo(()=>i?n.filter(x=>ma(x.name,i.name)&&x.month===c&&x.year===o).sort((x,d)=>x.date-d.date):[],[i,c,o,n]),f=J.useMemo(()=>u.reduce((x,d)=>x+(d.bgnTotal||0),0),[u]);return i?l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:l.jsxs("div",{className:`${s?"bg-gray-900":"bg-gray-100"} rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[90vh] flex flex-col`,children:[l.jsxs("div",{className:"flex justify-between items-center mb-4",children:[l.jsxs("h3",{className:"font-bold text-xl",children:["Транзакции за ",i.name]}),l.jsx("button",{onClick:r,className:"p-1 rounded-full hover:bg-gray-300",children:l.jsx(ll,{className:"w-6 h-6"})})]}),l.jsxs("div",{className:"flex items-center gap-4 mb-4 p-3 rounded-md border",children:[l.jsx("span",{className:"font-semibold",children:"Период:"}),l.jsx("select",{value:c,onChange:x=>t({...e,month:parseInt(x.target.value)}),className:`${s?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`,children:a.map((x,d)=>l.jsx("option",{value:d,children:x},x))}),l.jsx("input",{type:"number",value:o,onChange:x=>t({...e,year:parseInt(x.target.value)}),className:`${s?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2 w-24`})]}),l.jsx("div",{className:"flex-grow overflow-y-auto",children:u.length>0?l.jsxs("table",{className:"w-full text-sm",children:[l.jsx("thead",{children:l.jsxs("tr",{className:`border-b ${s?"border-gray-700":"border-gray-200"}`,children:[l.jsx("th",{className:"text-left py-2 px-2",children:"Дата"}),l.jsx("th",{className:"text-left py-2 px-2",children:"Тип"}),l.jsx("th",{className:"text-left py-2 px-2",children:"Група"}),l.jsx("th",{className:"text-left py-2 px-2",children:"Име"}),l.jsx("th",{className:"text-center py-2 px-2",children:"Сума (BGN)"})]})}),l.jsx("tbody",{children:u.map(x=>l.jsxs("tr",{className:`border-b ${s?"border-gray-700 hover:bg-gray-700":"border-gray-200 hover:bg-gray-50"}`,children:[l.jsx("td",{className:"py-2 px-2",children:`${x.day}.${x.month+1}.${x.year}`}),l.jsx("td",{className:`py-2 px-2 ${x.type==="income"?"text-green-600":"text-red-600"}`,children:x.type==="income"?"Приход":"Разход"}),l.jsx("td",{className:"py-2 px-2",children:x.group}),l.jsx("td",{className:"py-2 px-2",children:x.name}),l.jsx("td",{className:"py-2 text-center px-2 font-mono",children:(x.bgnTotal||0).toFixed(2)})]},x.uniqueKey))}),l.jsx("tfoot",{children:l.jsxs("tr",{className:`font-bold border-t-2 ${s?"border-gray-700":"border-gray-300"}`,children:[l.jsx("td",{colSpan:"4",className:"text-right py-2 px-2",children:"Общо:"}),l.jsxs("td",{className:"text-center py-2 px-2 font-mono",children:[f.toFixed(2)," лв"]})]})})]}):l.jsx("p",{className:"text-center py-8 text-gray-500",children:"Няма намерени транзакции за избрания период."})})]})}):null},Lg=({isDarkMode:e,employeesByDepartment:t,monthNames:r,salaryCashPayments:n,handleSalaryCashChange:a,initialYear:s,initialMonth:i,calculateTotal:c})=>{const[o,u]=J.useState({year:s,month:i}),f=p=>{const g=new Date(o.year,o.month,1);g.setMonth(g.getMonth()+p),u({year:g.getFullYear(),month:g.getMonth()})},x=(p,g,m,h,_)=>{const k=`${g}-${m}`,R={...n[p]?.[k]||{},[h]:parseInt(_)||0};a(p,g,m,R)},d=J.useMemo(()=>{const p={},g=`${o.year}-${o.month}`;for(const m in t){const h=Array(He.length).fill(0);t[m].forEach(k=>{const v=n[k.id]?.[g]||{};He.forEach((R,M)=>{h[M]+=v[M]||0})}),p[m]={counts:h,totalAmount:c(He,h)}}return p},[t,n,o,c]);return l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex justify-between items-center p-3 rounded-lg border",children:[l.jsx("h3",{className:"font-bold text-lg",children:"Разпределение на пари в брой"}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("button",{onClick:()=>f(-1),className:"p-1 rounded hover:bg-gray-300",children:l.jsx(gs,{className:"w-5 h-5"})}),l.jsxs("span",{className:"font-bold text-lg",children:[r[o.month]," ",o.year]}),l.jsx("button",{onClick:()=>f(1),className:"p-1 rounded hover:bg-gray-300",children:l.jsx(ps,{className:"w-5 h-5"})})]})]}),l.jsx("div",{className:"overflow-x-auto",children:l.jsxs("table",{className:"w-full text-base",children:[l.jsx("thead",{children:l.jsxs("tr",{className:`${e?"bg-gray-700":"bg-gray-200"} font-semibold`,children:[l.jsx("th",{className:"px-3 py-2 text-left w-64",children:"Име"}),He.map(p=>l.jsxs("th",{className:"px-2 py-2 text-center w-20",children:[p,"лв"]},`header-bgn-${p}`)),l.jsx("th",{className:"px-3 py-2 text-center w-28",children:"Общо"})]})}),l.jsx("tbody",{children:Object.entries(t).map(([p,g])=>l.jsxs(ds.Fragment,{children:[l.jsxs("tr",{className:`font-bold ${e?"bg-gray-900/50":"bg-gray-100"}`,children:[l.jsx("td",{className:"px-3 py-2",children:p}),d[p].counts.map((m,h)=>l.jsx("td",{className:"px-2 py-2 text-center text-blue-600",children:m>0?m:""},h)),l.jsx("td",{className:"px-3 py-2 text-center text-blue-600 font-semibold",children:d[p].totalAmount>0?d[p].totalAmount.toFixed(2)+" лв":""})]}),g.map(m=>{const h=`${o.year}-${o.month}`,_=n[m.id]?.[h]||{},k=c(He,He.map((v,R)=>_[R]||0));return l.jsxs("tr",{className:`border-b ${e?"border-gray-800 hover:bg-gray-700/50":"border-gray-200 hover:bg-gray-50"}`,children:[l.jsx("td",{className:"px-3 py-2",children:m.name}),He.map((v,R)=>l.jsx("td",{className:"px-2 py-2",children:l.jsx(rt,{type:"number",min:"0",value:_[R]||"",onChange:M=>x(m.id,o.year,o.month,R,M),className:`w-full px-1 py-1 text-base text-center border rounded ${e?"bg-gray-700 border-gray-600":"bg-white"}`})},R)),l.jsx("td",{className:"px-3 py-2 text-center font-semibold",children:k>0?k.toFixed(2)+" лв":""})]},m.id)})]},p))})]})})]})},Bg=({isDarkMode:e,currentMonth:t,currentYear:r,monthNames:n,calculateWorkingDays:a,employeeList:s,allTransactions:i,salaryCashPayments:c,handleSalaryCashChange:o,leaveData:u,handleLeaveDataChange:f,calculateTotal:x,popupData:d,setPopupData:p,departments:g,salaryInputs:m,setSalaryInputs:h})=>{const[_,k]=J.useState({}),[v,R]=J.useState(null),[M,S]=J.useState({}),w=t===0?11:t-1,j=t===0?r-1:r,E=`${j}-${w}`,L=J.useMemo(()=>a("Администрация",w,j),[g,w,j,a]),G=J.useMemo(()=>a("Цех",w,j),[g,w,j,a]),C=J.useMemo(()=>[...s].sort((he,F)=>he.name.localeCompare(F.name)).reduce((he,F)=>{const U=F.department||"Без отдел";return he[U]||(he[U]=[]),he[U].push(F),he},{}),[s]),I=re=>{k(he=>({...he,[re]:!he[re]}))},q=(re,he,F,U,H)=>{const P=`${he}-${F}`,me={...c[re]?.[P]||{},[U]:parseInt(H)||0};o(re,he,F,me)},ae=(re,he)=>{f(re,j,w,parseInt(he)||0)},ce=(re,he,F)=>{h(U=>{const H=JSON.parse(JSON.stringify(U));return H[E]||(H[E]={}),H[E][re]||(H[E][re]={}),H[E][re][he]=F,H})},de=J.useCallback(re=>{const he=m[E]?.[re.id]||{},F=a(re.department,w,j),U=he.workedDays!==void 0?he.workedDays:F,P=(F>0?(re.totalSalary||0)/F:0)*U,Q=parseFloat(he.additionalIncome)||0,me=parseFloat(he.bonuses)||0,ye=parseFloat(he.deductions)||0,oe=parseFloat(he.manualAdvance)||0,le=parseFloat(he.sickLeave)||0,Ee=F>0&&U<F?(re.socialSecurity||0)/F*U:re.socialSecurity||0;return P+Q+me-ye-Ee-oe-(re.bankSalary||0)-le},[m,E,w,j,a,s]),ie=re=>{p({isOpen:!0,employee:re,month:w,year:j})},B=(re,he)=>{S(F=>{const U=F[re]||{year:j,month:w},H=new Date(U.year,U.month,1);return H.setMonth(H.getMonth()+he),{...F,[re]:{year:H.getFullYear(),month:H.getMonth()}}})};return l.jsxs("div",{className:"space-y-4",children:[d.isOpen&&l.jsx(Pg,{popupData:d,setPopupData:p,onClose:()=>p({isOpen:!1,employee:null,month:0,year:0}),allTransactions:i,monthNames:n,isDarkMode:e}),l.jsx("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:l.jsxs("h3",{className:"font-bold text-lg mb-4 flex items-center flex-wrap",children:["Изчисляване на заплати за ",n[w]," ",j,l.jsxs("span",{className:"text-base font-semibold text-gray-500 ml-4",children:["(Раб. дни: ",L," | Раб. дни Цех: ",G,")"]})]})}),Object.entries(C).map(([re,he])=>{const F=!!_[re];return l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md overflow-hidden`,children:[l.jsxs("div",{onClick:()=>I(re),className:`flex justify-between items-center p-4 cursor-pointer ${e?"hover:bg-gray-700":"hover:bg-gray-50"}`,children:[l.jsxs("h4",{className:"font-bold text-xl",children:[re," (",he.length," служители)"]}),F?l.jsx(fn,{className:"w-6 h-6"}):l.jsx(un,{className:"w-6 h-6"})]}),F&&l.jsx("div",{className:`border-t ${e?"border-gray-700":"border-gray-200"} p-4 space-y-4`,children:he.map(U=>{const H=de(U),P=m[E]?.[U.id]||{},Q=a(U.department,w,j),me=v===U.id,ye=M[U.id]?.year||j,oe=M[U.id]?.month!==void 0?M[U.id].month:w,le=`${ye}-${oe}`,Ee=c[U.id]?.[le]||{},D=x(He,He.map((ke,Oe)=>Ee[Oe]||0));return l.jsxs("div",{className:`${e?"bg-gray-900/50":"bg-gray-50"} rounded-lg shadow-sm p-4 border ${e?"border-gray-700":"border-gray-200"}`,children:[l.jsxs("div",{className:"flex justify-between items-start mb-3",children:[l.jsxs("div",{children:[l.jsx("button",{onClick:()=>ie(U),className:"font-semibold text-lg text-blue-600 hover:underline text-left",children:U.name}),l.jsx("p",{className:"text-sm text-gray-500",children:U.department})]}),l.jsxs("div",{className:"text-right",children:[l.jsx("div",{className:"text-sm text-gray-500",children:"Обща заплата"}),l.jsxs("div",{className:"font-semibold",children:[(U.totalSalary||0).toFixed(2)," лв"]}),l.jsx("div",{className:"text-sm text-gray-500",children:"Платени по банка"}),l.jsxs("div",{className:"font-semibold text-blue-500",children:[(U.bankSalary||0).toFixed(2)," лв"]}),l.jsx("div",{className:"text-sm text-gray-500",children:"Осигуровки (удръжка)"}),l.jsxs("div",{className:"font-semibold text-red-500",children:[(U.socialSecurity||0).toFixed(2)," лв"]})]})]}),l.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-7 gap-3 mb-3",children:[l.jsxs("div",{children:[l.jsxs("label",{className:"text-sm text-gray-600",children:["Работни дни (от ",Q,")"]}),l.jsx("input",{type:"number",value:P.workedDays!==void 0?P.workedDays:"",placeholder:Q.toString(),onChange:ke=>ce(U.id,"workedDays",ke.target.value===""?void 0:parseFloat(ke.target.value)||0),className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:"text-sm text-gray-600",children:"Изп. отпуск (дни)"}),l.jsx("input",{type:"number",value:u[U.id]?.[j]?.[w]||"",onChange:ke=>ae(U.id,ke.target.value),className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:"text-sm text-gray-600",children:"Допълнителни"}),l.jsx("input",{type:"number",value:P.additionalIncome||"",onChange:ke=>ce(U.id,"additionalIncome",ke.target.value),className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:"text-sm text-gray-600",children:"Бонуси"}),l.jsx("input",{type:"number",value:P.bonuses||"",onChange:ke=>ce(U.id,"bonuses",ke.target.value),className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:"text-sm text-gray-600",children:"Аванс заплата"}),l.jsx("input",{type:"number",value:P.manualAdvance||"",onChange:ke=>ce(U.id,"manualAdvance",ke.target.value),className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:"text-sm text-red-500",children:"Болнични (лв)"}),l.jsx("input",{type:"number",value:P.sickLeave||"",onChange:ke=>ce(U.id,"sickLeave",ke.target.value),className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1 w-full`})]}),l.jsxs("div",{children:[l.jsx("label",{className:"text-sm text-gray-600",children:"Други удръжки"}),l.jsx("input",{type:"number",value:P.deductions||"",onChange:ke=>ce(U.id,"deductions",ke.target.value),className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1 w-full`})]})]}),l.jsx("div",{className:`border-t pt-3 mt-3 ${e?"border-gray-700":"border-gray-300"}`,children:l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsxs("div",{className:"font-semibold flex items-center",children:["За плащане в брой:",l.jsxs("button",{onClick:()=>R(me?null:U.id),className:"ml-4 bg-blue-500 text-white px-3 py-1 text-sm rounded hover:bg-blue-600 flex items-center",children:[l.jsx(ms,{className:"w-4 h-4 mr-1"}),"Пари",me?l.jsx(fn,{className:"w-4 h-4 ml-1"}):l.jsx(un,{className:"w-4 h-4 ml-1"})]})]}),l.jsxs("span",{className:`text-xl font-bold ${H>=0?"text-green-600":"text-red-500"}`,children:[H.toFixed(2)," лв"]})]})}),me&&l.jsxs("div",{className:`mt-2 p-3 ${e?"bg-blue-900/20":"bg-blue-50"} border ${e?"border-blue-800":"border-blue-200"} rounded-lg`,children:[l.jsxs("div",{className:"flex justify-between items-center mb-3",children:[l.jsx("span",{className:"font-semibold",children:"Плащане в брой за:"}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("button",{onClick:()=>B(U.id,-1),className:"p-1 rounded hover:bg-gray-300",children:l.jsx(gs,{className:"w-5 h-5"})}),l.jsxs("span",{className:"font-bold text-lg",children:[n[oe]," ",ye]}),l.jsx("button",{onClick:()=>B(U.id,1),className:"p-1 rounded hover:bg-gray-300",children:l.jsx(ps,{className:"w-5 h-5"})})]})]}),l.jsx("div",{className:"grid grid-cols-7 gap-2 items-center",children:He.slice(0,7).map((ke,Oe)=>l.jsxs("div",{children:[l.jsxs("label",{className:"text-xs text-center block",children:[ke," лв"]}),l.jsx(rt,{type:"number",min:"0",value:Ee[Oe]||"",onChange:Ie=>q(U.id,ye,oe,Oe,Ie),className:`w-full px-1 py-1 text-base text-center border rounded ${e?"bg-gray-700 border-gray-600":"bg-white"}`})]},Oe))}),l.jsx("div",{className:"grid grid-cols-6 gap-2 mt-2 items-center",children:He.slice(7).map((ke,Oe)=>{const Ie=Oe+7;return l.jsxs("div",{children:[l.jsxs("label",{className:"text-xs text-center block",children:[ke," лв"]}),l.jsx(rt,{type:"number",min:"0",value:Ee[Ie]||"",onChange:je=>q(U.id,ye,oe,Ie,je),className:`w-full px-1 py-1 text-base text-center border rounded ${e?"bg-gray-700 border-gray-600":"bg-white"}`})]},Ie)})}),l.jsxs("div",{className:"mt-2 text-right font-semibold",children:["Въведена сума:",l.jsxs("span",{className:`ml-2 ${D.toFixed(2)!==H.toFixed(2)&&`${ye}-${oe}`==`${j}-${w}`?"text-red-500":"text-green-600"}`,children:[D.toFixed(2)," лв"]})]})]})]},U.id)})})]},re)}),l.jsx("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4 mt-6`,children:l.jsx(Lg,{isDarkMode:e,employeesByDepartment:C,monthNames:n,salaryCashPayments:c,handleSalaryCashChange:o,initialYear:j,initialMonth:w,calculateTotal:x})})]})},Mg=({isDarkMode:e,currentYear:t,currentMonth:r,monthNames:n,dailyData:a,turnoverOverrides:s,setTurnoverOverrides:i,isHoliday:c})=>{const o=["Иван Р-С","Недялко Р-С","Сини Камъни","Добрович","Денонощен","Хали"],u=["Нд","Пн","Вт","Ср","Чт","Пт","Сб"],f=J.useRef(null),x=g=>{const m=f.current;if(!m){g();return}const h=m.scrollTop,_=document.activeElement;g(),requestAnimationFrame(()=>{f.current&&(f.current.scrollTop=h),_&&typeof _.focus=="function"&&_.focus()})},d=(g,m,h)=>{x(()=>{const _=`${t}-${r}-${g}`;i(k=>{const v=JSON.parse(JSON.stringify(k));return v[_]||(v[_]={}),v[_][m]=h,v})})},p=J.useMemo(()=>{const g=new Date(t,r+1,0).getDate(),m=[],h=Array(o.length).fill(0),_={};for(let v=1;v<=g;v++)_[`${t}-${r}-${v}`]=Array(o.length).fill(0);const k=(v,R)=>{const M=R.trim().match(/(\d{2})\.(\d{2})$/);if(M){const w=parseInt(M[1],10),j=parseInt(M[2],10)-1;let E=v.getFullYear();return j===11&&v.getMonth()===0&&(E-=1),j===0&&v.getMonth()===11&&(E+=1),`${E}-${j}-${w}`}const S=new Date(v);return S.setDate(S.getDate()-1),`${S.getFullYear()}-${S.getMonth()}-${S.getDate()}`};Object.entries(a).forEach(([v,R])=>{if(!R?.income?.length)return;const[M,S,w]=v.split("-").map(Number),j=new Date(M,S,w);R.income.forEach(E=>{!E||!E.name||o.forEach((L,G)=>{if(!ma(E.name,L))return;const C=k(j,E.name),[I,q]=C.split("-").map(Number);I===t&&q===r&&(_[C]||(_[C]=Array(o.length).fill(0)),_[C][G]+=E.bgnTotal||0)})})});for(let v=1;v<=g;v++){const R=`${t}-${r}-${v}`,M=[],S=s[R]||{};for(let w=0;w<o.length;w++){const j=_[R][w],E=S[w],L=E!==void 0&&E!==""?E:j>0?j.toFixed(2):"";M.push(L);const G=E!==void 0&&E!==""?parseFloat(E)||0:j;h[w]+=G}m.push({day:v,totals:M})}return{dailyData:m,monthlyTotals:h}},[a,s,r,t]);return l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4 h-full flex flex-col`,children:[l.jsxs("h3",{className:"font-bold text-xl mb-4",children:["Оборот за ",n[r]," ",t]}),l.jsx("div",{className:"flex-grow overflow-auto",ref:f,children:l.jsxs("table",{className:"w-full text-base text-center",children:[l.jsxs("thead",{className:`sticky top-0 z-10 ${e?"bg-gray-800":"bg-white"}`,children:[l.jsxs("tr",{className:`${e?"bg-gray-700":"bg-gray-200"} font-bold`,children:[l.jsx("th",{className:"py-3 px-3 text-left",colSpan:"2",children:"Общо за месеца"}),p.monthlyTotals.map((g,m)=>l.jsxs("th",{className:"py-3 px-3 text-center text-green-600 font-semibold",children:[g>0?g.toFixed(2):"-"," лв"]},m))]}),l.jsxs("tr",{className:`${e?"bg-gray-900":"bg-gray-100"} border-b ${e?"border-gray-700":"border-gray-300"} font-semibold`,children:[l.jsx("th",{className:"py-3 px-3 text-left w-20",children:"Дата"}),l.jsx("th",{className:"py-3 px-3 text-left w-16",children:"Ден"}),o.map(g=>l.jsx("th",{className:"py-3 px-3 text-center",children:g},g))]})]}),l.jsx("tbody",{children:p.dailyData.map(({day:g,totals:m})=>{const _=new Date(t,r,g).getDay(),k=_===0||_===6,R=c(g)?e?"bg-red-900/40":"bg-red-100":k?e?"bg-gray-700/50":"bg-gray-100":"";return l.jsxs("tr",{className:`border-b ${e?"border-gray-700":"border-gray-200"} ${R}`,children:[l.jsx("td",{className:"py-2 px-3 text-left",children:`${g.toString().padStart(2,"0")}.${(r+1).toString().padStart(2,"0")}.${t}`}),l.jsx("td",{className:"py-2 px-3 text-left",children:u[_]}),m.map((M,S)=>l.jsx("td",{className:"py-1 px-2 text-center font-mono",children:l.jsx(rt,{type:"number",value:M,onChange:w=>d(g,S,w),className:`w-full px-2 py-2 text-base text-center font-mono ${e?"bg-transparent border-gray-600 hover:border-gray-400":"bg-transparent border-gray-300 hover:border-gray-400"} border-b focus:outline-none focus:ring-1 focus:ring-blue-500 rounded-sm`,placeholder:"-",blurOnEnter:!1})},S))]},g)})})]})})]})},$g=({isDarkMode:e,clientList:t,setClientList:r,customHolidays:n,setCustomHolidays:a,transactionGroups:s,setTransactionGroups:i,importFromFile:c})=>{const[o,u]=J.useState(""),[f,x]=J.useState({name:"",group:""}),[d,p]=J.useState(null),[g,m]=J.useState({name:"",group:""}),[h,_]=J.useState({date:"",name:""}),[k,v]=J.useState(""),[R,M]=J.useState(null),[S,w]=J.useState(""),j=J.useMemo(()=>o.trim()?t.filter(ie=>ma(ie.name,o)):t,[t,o]),E=()=>{if(f.name.trim()&&!t.find(ie=>ie.name.toLowerCase()===f.name.trim().toLowerCase())){const ie={name:f.name.trim(),group:f.group||""};r(B=>[...B,ie].sort((re,he)=>re.name.localeCompare(he.name))),x({name:"",group:""})}},L=()=>{if(!g.name.trim()||!d)return;if(t.some(re=>re.name.toLowerCase()===g.name.trim().toLowerCase()&&re.name.toLowerCase()!==d.name.toLowerCase())){alert("Това име на клиент вече съществува.");return}const B=t.map(re=>re.name===d.name?{...re,name:g.name.trim(),group:g.group}:re);r(B.sort((re,he)=>re.name.localeCompare(he.name))),p(null),m({name:"",group:""})},G=ie=>{r(t.filter(B=>B.name!==ie.name))},C=ie=>{if(!ie||!/^\d{4}-\d{2}-\d{2}$/.test(ie))return"";const[B,re,he]=ie.split("-");return`${he}/${re}/${B}`},I=()=>{if(h.date&&h.name){const ie=/^(\d{1,2})[\.\/](\d{1,2})[\.\/](\d{4})$/,B=h.date.match(ie);if(!B){alert("Моля, въведете дата във формат ДД/ММ/ГГГГ.");return}const re=B[1].padStart(2,"0"),he=B[2].padStart(2,"0"),U=`${B[3]}-${he}-${re}`;a([...n,{date:U,name:h.name}]),_({date:"",name:""})}},q=ie=>{a(n.filter((B,re)=>re!==ie))},ae=()=>{k.trim()&&!s.includes(k.trim())&&(i([...s,k.trim()]),v(""))},ce=ie=>{const B=[...s];B[ie]=S,i(B),M(null),w("")},de=ie=>{i(s.filter((B,re)=>re!==ie))};return l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsx("h3",{className:"font-bold text-lg mb-4",children:"Групи транзакции"}),l.jsxs("div",{className:"flex gap-2 mb-4",children:[l.jsx("input",{type:"text",value:k,onChange:ie=>v(ie.target.value),onKeyPress:ie=>ie.key==="Enter"&&ae(),placeholder:"Добави нова група",className:`flex-1 ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`}),l.jsx("button",{onClick:ae,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",children:l.jsx(Aa,{className:"w-5 h-5"})})]}),l.jsx("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:s.map((ie,B)=>l.jsx("div",{className:`flex items-center gap-2 p-2 border rounded ${e?"border-gray-700 hover:bg-gray-700":"hover:bg-gray-50"}`,children:R===B?l.jsxs(l.Fragment,{children:[l.jsx("input",{type:"text",value:S,onChange:re=>w(re.target.value),onKeyPress:re=>re.key==="Enter"&&ce(B),className:`flex-1 ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1`}),l.jsx("button",{onClick:()=>ce(B),className:"text-green-500 hover:text-green-700",children:"✓"}),l.jsx("button",{onClick:()=>{M(null),w("")},className:"text-red-500 hover:text-red-700",children:"✗"})]}):l.jsxs(l.Fragment,{children:[l.jsx("span",{className:"flex-1",children:ie}),l.jsx("button",{onClick:()=>{M(B),w(ie)},className:"text-blue-500 hover:text-blue-700",children:l.jsx(hn,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>de(B),className:"text-red-500 hover:text-red-700",children:l.jsx(ia,{className:"w-4 h-4"})})]})},B))})]}),l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsx("h3",{className:"font-bold text-lg mb-4",children:"Списък клиенти"}),l.jsxs("div",{className:"flex items-center gap-2 mb-2 p-2 border rounded-md",children:[l.jsx(il,{className:`w-5 h-5 ${e?"text-gray-400":"text-gray-500"}`}),l.jsx("input",{type:"text",value:o,onChange:ie=>u(ie.target.value),placeholder:"Търси клиент...",className:"flex-1 bg-transparent focus:outline-none w-full"})]}),l.jsxs("div",{className:"flex gap-2 mb-4",children:[l.jsx("input",{type:"text",value:f.name,onChange:ie=>x({...f,name:ie.target.value}),onKeyPress:ie=>ie.key==="Enter"&&E(),placeholder:"Добави нов клиент",className:`flex-1 ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`}),l.jsxs("select",{value:f.group,onChange:ie=>x({...f,group:ie.target.value}),className:`w-1/3 ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`,children:[l.jsx("option",{value:"",children:"Без група"}),s.map(ie=>l.jsx("option",{value:ie,children:ie},ie))]}),l.jsx("button",{onClick:E,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",children:l.jsx(Aa,{className:"w-5 h-5"})})]}),l.jsx("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:j.map((ie,B)=>l.jsx("div",{className:`flex items-center gap-2 p-2 border rounded ${e?"border-gray-700 hover:bg-gray-700":"hover:bg-gray-50"}`,children:d&&d.name===ie.name?l.jsxs(l.Fragment,{children:[l.jsx("input",{type:"text",value:g.name,onChange:re=>m({...g,name:re.target.value}),className:`flex-1 ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1`,autoFocus:!0}),l.jsxs("select",{value:g.group,onChange:re=>m({...g,group:re.target.value}),className:`w-1/3 ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-2 py-1`,children:[l.jsx("option",{value:"",children:"Без група"}),s.map(re=>l.jsx("option",{value:re,children:re},re))]}),l.jsx("button",{onClick:L,className:"text-green-500 hover:text-green-700",children:"✓"}),l.jsx("button",{onClick:()=>p(null),className:"text-red-500 hover:text-red-700",children:"✗"})]}):l.jsxs(l.Fragment,{children:[l.jsx("span",{className:"flex-1",children:ie.name}),ie.group&&l.jsx("span",{className:"text-xs bg-gray-500 text-white px-2 py-1 rounded-full",children:ie.group}),l.jsx("button",{onClick:()=>{p(ie),m({name:ie.name,group:ie.group||""})},className:"text-blue-500 hover:text-blue-700",children:l.jsx(hn,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>G(ie),className:"text-red-500 hover:text-red-700",children:l.jsx(ia,{className:"w-4 h-4"})})]})},B))})]}),l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsx("h3",{className:"font-bold text-lg mb-4",children:"Персонализирани празници (Ден/Месец/Година)"}),l.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-4",children:[l.jsx("input",{type:"text",value:h.date,onChange:ie=>_({...h,date:ie.target.value}),placeholder:"ДД/ММ/ГГГГ",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("input",{type:"text",value:h.name,onChange:ie=>_({...h,name:ie.target.value}),placeholder:"Име на празник",className:`flex-1 ${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2`}),l.jsx("button",{onClick:I,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",children:l.jsx(Aa,{className:"w-5 h-5"})})]})]}),l.jsx("div",{className:"space-y-2",children:n.map((ie,B)=>l.jsxs("div",{className:`flex items-center justify-between p-2 border rounded ${e?"border-gray-700":""}`,children:[l.jsxs("span",{children:[C(ie.date)," - ",ie.name]}),l.jsx("button",{onClick:()=>q(B),className:"text-red-500 hover:text-red-700",children:l.jsx(ia,{className:"w-4 h-4"})})]},B))})]})]})},Ug=({isDarkMode:e,changeHistory:t,setChangeHistory:r})=>{const[n,a]=J.useState(!1),[s,i]=J.useState([]),c=()=>{window.confirm("Сигурни ли сте, че искате да изтриете цялата история на промените? Тази операция е необратима.")&&(r([]),alert("Историята е изчистена."))},o=async()=>{if(!window.electronAPI)return;const d=await window.electronAPI.saveHistory(t);d.success?alert("Историята е запазена успешно!"):alert(`Грешка: ${d.error}`)},u=async()=>{if(!window.electronAPI)return;const d=await window.electronAPI.loadHistory();d.success&&d.data?(r(d.data),alert("Историята е заредена успешно!")):alert("Няма запазена история или възникна грешка.")},f=async()=>{if(!window.electronAPI)return;const d=await window.electronAPI.listHistoryArchives();i(d),a(!0)},x=async d=>{if(!window.electronAPI)return;const p=await window.electronAPI.loadHistoryArchive(d);p.success&&p.data&&(p.data.entries?(r(p.data.entries),alert(`Архив зареден: ${p.data.entryCount} записа`)):Array.isArray(p.data)&&(r(p.data),alert("Историята е заредена успешно!")),a(!1))};return l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4 mb-4`,children:[l.jsx("h3",{className:"font-bold text-lg mb-3",children:"Управление на историята"}),l.jsxs("div",{className:"flex gap-2 flex-wrap",children:[l.jsxs("button",{onClick:o,className:"bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center",children:[l.jsx(Qn,{className:"w-4 h-4 mr-1"}),"Запази история"]}),l.jsxs("button",{onClick:u,className:"bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center",children:[l.jsx(es,{className:"w-4 h-4 mr-1"}),"Зареди история"]}),l.jsxs("button",{onClick:f,className:"bg-purple-500 text-white px-3 py-1 rounded hover:bg-purple-600 flex items-center",children:[l.jsx(ci,{className:"w-4 h-4 mr-1"}),"Архиви"]}),l.jsxs("button",{onClick:async()=>{if(window.electronAPI&&t&&t.length>0){const d=await window.electronAPI.archiveHistory(t);d.success?(r([]),alert(`Архивирано: ${d.filename}`)):alert(`Грешка при архивиране: ${d.error}`)}else alert("Няма история за архивиране")},className:"bg-orange-500 text-white px-3 py-1 rounded hover:bg-orange-600 flex items-center",children:[l.jsx(si,{className:"w-4 h-4 mr-1"}),"Архивирай и изчисти"]}),l.jsxs("button",{onClick:c,className:"bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 flex items-center",children:[l.jsx(ia,{className:"w-4 h-4 mr-1"}),"Изчисти история"]})]}),n&&s.length>0&&l.jsxs("div",{className:"mt-4 max-h-60 overflow-y-auto border rounded p-2",children:[l.jsx("h4",{className:"font-semibold mb-2",children:"Налични архиви:"}),s.map(d=>l.jsxs("div",{className:"flex justify-between items-center p-2 hover:bg-gray-100 rounded",children:[l.jsxs("div",{children:[l.jsx("p",{className:"font-medium",children:d.filename}),l.jsxs("p",{className:"text-sm text-gray-500",children:[new Date(d.modified).toLocaleString("bg-BG")," • ",d.size]})]}),l.jsx("button",{onClick:()=>x(d.filepath),className:"bg-blue-500 text-white px-2 py-1 rounded text-sm hover:bg-blue-600",children:"Зареди"})]},d.filename))]})]})},Hg=({isDarkMode:e,changeHistory:t,setChangeHistory:r,isElectron:n,setActiveTab:a,setCurrentYear:s,setCurrentMonth:i,setSelectedDay:c,setScrollToRowId:o,hasScrolledRef:u})=>{const[f,x]=J.useState({searchName:"",amountSearch:""}),[d,p]=J.useState("all"),[g,m]=J.useState({}),h=J.useCallback(S=>{let w="",j=0,E=0,L=null;return S.details.updatedTransaction?L=S.details.updatedTransaction:S.type==="delete"&&S.details.deletedData?L=S.details.deletedData:S.type==="add"&&S.details.addedData&&(L=S.details.addedData),L&&(w=L.name||"",j=L.bgnTotal||0,E=L.eurTotal||0),S.type==="edit"&&S.details.fieldName==="Име"&&(w=S.details.newValue||w),{name:w,bgnTotal:j,eurTotal:E}},[]),_=J.useMemo(()=>{const S=f.searchName.trim(),w=f.amountSearch.trim(),j=w!=="";let E=[...t].filter(L=>{const{name:G,bgnTotal:C,eurTotal:I}=h(L),q=S?ma(G,S):!0,ae=j?C?.toFixed(2).startsWith(w)||I?.toFixed(2).startsWith(w):!0;return q&&ae});return d==="excludeDeleted"?E.filter(L=>L.type!=="delete"):d==="onlyDeleted"?E.filter(L=>L.type==="delete"):E},[t,f,h,d]),k=J.useMemo(()=>{if(d!=="grouped")return null;const S={};return _.forEach(w=>{const j=w.location.rowId;S[j]||(S[j]={latestTimestamp:w.timestamp,location:w.location,transactionInfo:h(w),changes:[]}),S[j].changes.push(w),new Date(w.timestamp)>new Date(S[j].latestTimestamp)&&(S[j].latestTimestamp=w.timestamp,S[j].transactionInfo=h(w))}),Object.values(S).sort((w,j)=>new Date(j.latestTimestamp)-new Date(w.latestTimestamp))},[_,d,h]),v=S=>{const{year:w,month:j,day:E,section:L,rowId:G}=S.location,C=`${w}-${j}-${E}-${L}-${G}`;u.current=!0,a("daily"),s(w),i(j),c(E),o(C)},R=S=>{const{year:w,month:j,day:E,section:L,rowIndex:G}=S.location,C=`${E}.${j+1}.${w}`,I=L==="income"?"Приходи":"Разходи";switch(S.type){case"edit":const q=S.details.oldValue!==void 0&&S.details.oldValue!==""?`"${S.details.oldValue}"`:"празно",ae=S.details.newValue!==void 0&&S.details.newValue!==""?`"${S.details.newValue}"`:"празно";return`Промяна на ред ${G+1} в "${I}" за ${C}: полето "${S.details.fieldName}" е променено от ${q} на ${ae}.`;case"add":return`Добавен нов ред на позиция ${G+1} в "${I}" за ${C}.`;case"delete":const ce=S.details.deletedData||{},de=ce.name||"празно",ie=(ce.bgnTotal||0).toFixed(2),B=(ce.eurTotal||0).toFixed(2);return`Изтрит ред от поз. ${G+1} в "${I}" за ${C}. (Име: "${de}", Сума: ${ie}лв / ${B}€)`;default:return"Неизвестно действие"}},M=S=>{const{name:w,bgnTotal:j,eurTotal:E}=h(S),L=[];j&&L.push(`${j.toFixed(2)} лв`),E&&L.push(`€${E.toFixed(2)}`);const G=L.join(" / "),C=S.type==="delete"?e?"bg-red-900/50":"bg-red-100":e?"bg-gray-700/50":"bg-gray-100";return l.jsxs("div",{className:`p-3 rounded-md flex justify-between items-start ${C}`,children:[l.jsxs("div",{className:"flex-grow",children:[l.jsx("div",{className:"text-xs text-gray-500 font-mono",children:new Date(S.timestamp).toLocaleString("bg-BG")}),l.jsx("p",{className:"text-sm",children:R(S)}),(w||G)&&l.jsxs("div",{className:"text-xs text-gray-400 mt-1 pl-2 border-l-2 border-gray-500",children:['Име: "',w||"няма",'", Сума: ',G||"0.00 лв"]})]}),S.type!=="delete"&&l.jsx("button",{onClick:()=>v(S),className:"px-4 py-2 text-blue-500 hover:text-blue-700 rounded-full hover:bg-blue-100 ml-4 flex-shrink-0",title:"Отиди до транзакцията",children:l.jsx(oi,{className:"w-5 h-5"})})]},S.id)};return l.jsxs("div",{className:"h-full flex flex-col space-y-4",children:[n&&l.jsx(Ug,{isDarkMode:e,changeHistory:t,setChangeHistory:r}),l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4`,children:[l.jsxs("h3",{className:"font-bold text-lg mb-3 flex items-center",children:[l.jsx(li,{className:"w-5 h-5 mr-2"}),"Търсене в историята"]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-4",children:[l.jsx(rt,{type:"text",value:f.searchName,onChange:S=>x(w=>({...w,searchName:S})),placeholder:"Търси по име...",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2 w-full`}),l.jsx(rt,{type:"number",value:f.amountSearch,onChange:S=>x(w=>({...w,amountSearch:S})),placeholder:"Търси по сума...",className:`${e?"bg-gray-700 border-gray-600":"bg-white"} border rounded px-3 py-2 w-full`})]}),l.jsxs("div",{className:"flex flex-wrap gap-2",children:[l.jsx("button",{onClick:()=>p("all"),className:`px-3 py-1 text-sm rounded ${d==="all"?"bg-blue-600 text-white":"bg-gray-200 text-black hover:bg-gray-300"}`,children:"Всички"}),l.jsx("button",{onClick:()=>p("grouped"),className:`px-3 py-1 text-sm rounded ${d==="grouped"?"bg-blue-600 text-white":"bg-gray-200 text-black hover:bg-gray-300"}`,children:"Групирай по ред"}),l.jsx("button",{onClick:()=>p("onlyDeleted"),className:`px-3 py-1 text-sm rounded ${d==="onlyDeleted"?"bg-red-600 text-white":"bg-gray-200 text-black hover:bg-gray-300"}`,children:"Само изтрити"}),l.jsx("button",{onClick:()=>p("excludeDeleted"),className:`px-3 py-1 text-sm rounded ${d==="excludeDeleted"?"bg-yellow-500 text-white":"bg-gray-200 text-black hover:bg-gray-300"}`,children:"Без изтрити"})]})]}),l.jsxs("div",{className:`${e?"bg-gray-800":"bg-white"} rounded-lg shadow-md p-4 flex-grow overflow-y-auto`,children:[l.jsxs("h3",{className:"font-bold text-lg mb-4 flex items-center",children:[l.jsx(rs,{className:"w-5 h-5 mr-2"}),"История на промените"]}),l.jsx("div",{className:"flex-grow",children:d==="grouped"?l.jsx("div",{className:"space-y-2",children:k.map(S=>{const{name:w,bgnTotal:j,eurTotal:E}=S.transactionInfo,L=[];j&&L.push(`${j.toFixed(2)} лв`),E&&L.push(`€${E.toFixed(2)}`);const G=L.join(" / "),C=!!g[S.location.rowId];return l.jsxs("div",{children:[l.jsxs("div",{onClick:()=>m(I=>({...I,[S.location.rowId]:!I[S.location.rowId]})),className:`p-3 rounded-md flex justify-between items-center cursor-pointer ${e?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("span",{className:"p-1",children:C?l.jsx(fn,{className:"w-5 h-5"}):l.jsx(un,{className:"w-5 h-5"})}),l.jsxs("div",{children:[l.jsx("div",{className:"font-semibold",children:w||"Без име"}),l.jsxs("div",{className:"text-sm text-gray-500",children:[S.changes.length," промени • Последна: ",new Date(S.latestTimestamp).toLocaleString("bg-BG")]})]})]}),l.jsxs("div",{className:"text-right",children:[l.jsx("div",{className:"font-mono",children:G||"0.00 лв"}),l.jsxs("div",{className:"text-sm text-gray-500",children:["Ред ",S.location.rowIndex+1," в ",S.location.section==="income"?"Приходи":"Разходи"]})]})]}),C&&l.jsx("div",{className:"pl-8 pt-2 space-y-2 border-l-2 ml-4 mt-1 border-gray-400",children:S.changes.sort((I,q)=>new Date(q.timestamp)-new Date(I.timestamp)).map(I=>M(I))})]},S.location.rowId)})}):l.jsx("div",{className:"space-y-3",children:_.length===0?l.jsx("p",{className:"text-center text-gray-500 py-8",children:"Няма намерени промени, отговарящи на търсенето."}):_.map(S=>M(S))})})]})]})};function ai({day:e,month:t,year:r},n=1){const a=new Date(r,t,e);return a.setDate(a.getDate()+n),{day:a.getDate(),month:a.getMonth(),year:a.getFullYear()}}const Gg=()=>l.jsx(Tg,{children:l.jsx(Vg,{})}),Vg=()=>{const[e,t]=J.useState(new Date().getMonth()),[r,n]=J.useState(new Date().getFullYear()),[a,s]=J.useState(new Date().getDate()),[i,c]=J.useState("daily"),[o,u]=J.useState(!1),[f,x]=J.useState(!1),[d,p]=J.useState(250),[g,m]=J.useState(!1),[h,_]=J.useState({}),[k,v]=J.useState([]),[R,M]=J.useState(-1),[S,w]=J.useState("bgn"),[j,E]=J.useState({income:!1,expense:!1,balance:!1}),[L,G]=J.useState({searchName:"",selectedGroups:[],excludedGroups:[],dateRange:"month",amountSearch:"",excludeCarryOver:!0,showOnlyAutoSalaries:!1}),C=J.useRef(null),I=J.useRef(null),q=J.useRef(!1),[ae,ce]=J.useState(null),[de,ie]=J.useState(null),[B,re]=J.useState(0),[he,F]=J.useState(!1),[U,H]=J.useState(320),{showToast:P}=bg(),[Q,me]=J.useState(()=>Je.load("cashManagement_tabZoom",{daily:100,analysis:100,salary:100,employees:100,turnover:100,lists:100,history:100})),[ye,oe]=J.useState(!1),le=V=>{me(Y=>{const Z=Math.max(70,Math.min(200,(Y[i]||100)+V)),se={...Y,[i]:Z};return Je.save("cashManagement_tabZoom",se),se})},[Ee,D]=J.useState(()=>Je.load("cashManagement_dailyData",{})),[ke,Oe]=J.useState(()=>Je.load("cashManagement_clients",[{name:"ВЪРШИНА СУНА",group:"Продажби"},{name:"СИНИ КАМЪНИ",group:"Продажби"},{name:"ДОБРОВИН",group:"Продажби"},{name:"ДЕНОШОЩЕН",group:"Продажби"},{name:"КЛИЕНТ",group:""}])),[Ie,je]=J.useState(()=>Je.load("cashManagement_employees",[{id:1,name:"Иван Иванов",totalSalary:1200,bankSalary:800,socialSecurity:150,department:"Администрация",annualLeave:20},{id:2,name:"Петър Петров",totalSalary:1500,bankSalary:1e3,socialSecurity:200,department:"Цех",annualLeave:22}])),[xe,Ve]=J.useState(()=>Je.load("cashManagement_holidays",[])),[er,qe]=J.useState(()=>Je.load("cashManagement_employeeTransactions",{})),[te,ne]=J.useState(()=>Je.load("cashManagement_transactionGroups",Sg)),[Ne,Be]=J.useState(()=>Je.load("cashManagement_departments",Ag)),[De,Ge]=J.useState(()=>Je.load("cashManagement_turnoverOverrides",{})),[Ye,Ze]=J.useState(()=>Je.load("cashManagement_salaryCashPayments",{})),[fr,$e]=J.useState(()=>Je.load("cashManagement_leaveData",{})),[rr,Pr]=J.useState(()=>Je.load("cashManagement_salaryInputs",{})),[Qe,Kr]=J.useState(()=>Je.load("cashManagement_changeHistory",[])),Ar=typeof window<"u"&&window.electronAPI!==void 0;J.useEffect(()=>{_g()},[]);const mt=J.useCallback(()=>{const V=[];return Object.entries(Ee).forEach(([Y,Z])=>{if(!Z||!Z.income||!Z.expense)return;const[se,pe,ue]=Y.split("-").map(Number);Z.income.forEach(we=>we&&we.id&&we.name&&V.push({...we,type:"income",year:se,month:pe,day:ue,date:new Date(se,pe,ue),uniqueKey:`${Y}-income-${we.id}`})),Z.expense.forEach(we=>we&&we.id&&we.name&&V.push({...we,type:"expense",year:se,month:pe,day:ue,date:new Date(se,pe,ue),uniqueKey:`${Y}-expense-${we.id}`}))}),{version:"1.0",savedAt:new Date().toISOString(),data:{dailyData:Ee,clientList:ke,employeeList:Ie,customHolidays:xe,employeeTransactions:er,transactionGroups:te,departments:Ne,turnoverOverrides:De,salaryCashPayments:Ye,leaveData:fr,salaryInputs:rr},metadata:{currentMonth:e,currentYear:r,selectedDay:a,totalTransactions:V.length,totalHistoryEntries:Qe.length}}},[Ee,ke,Ie,xe,er,te,Ne,De,Ye,fr,rr,Qe,e,r,a]),[Ws,qa]=J.useState(null);J.useEffect(()=>{if(!Ar)return;const V=async()=>{try{const se=await window.electronAPI.autoSave(JSON.stringify(mt()));se.success&&(qa(new Date),console.log("Auto-saved:",se.filename))}catch(se){console.error("Auto-save error:",se)}},Y=setTimeout(V,6e4),Z=setInterval(V,1440*60*1e3);return()=>{clearTimeout(Y),clearInterval(Z)}},[mt,Ar]);const Zt=async V=>{try{if(!V||!V.data)throw new Error("Файлът за възстановяване е непълен или повреден.");const Y=V.data,Z=P("Зареждане на данните...","info");kn(),await wg(Y,{setDailyData:D,setClientList:Oe,setEmployeeList:je,setCustomHolidays:Ve,setEmployeeTransactions:qe,setTransactionGroups:ne,setDepartments:Be,setTurnoverOverrides:Ge,setSalaryCashPayments:Ze,setLeaveData:$e,setSalaryInputs:Pr,setChangeHistory:Kr});const se=[JSON.parse(JSON.stringify(Y.dailyData||{}))];v(se),M(0),Z.update("Данните са заредени успешно!","success"),setTimeout(()=>{Za(Y.dailyData)&&Tt()},100)}catch(Y){throw console.error("Error restoring data:",Y),Y}},Za=V=>{const Y=Object.keys(V||{});for(const Z of Y)if(V[Z]?.income?.[0]?.isCarryOver===void 0)return!0;return!1},Cn=async()=>{if(!Ar)return;const V=P("Запазване...","info");try{const Y=mt();if(!Y||!Y.data)throw new Error("Invalid data structure");const Z=new Date().toISOString().replace(/[:.]/g,"-"),se=JSON.stringify({...Y,timestamp:Z}),pe=await window.electronAPI.quickSave(se);if(pe.success)V.update(`Успешно запазено! ${pe.filename}`,"success"),Je.save("lastSuccessfulSave",new Date().toISOString()),console.log(`Quick save completed: ${pe.filename}`);else throw new Error(pe.error)}catch(Y){console.error("Quick save error:",Y),V.update(`Грешка: ${Y.message}`,"error");try{Je.save("emergencyBackup",mt()),V.update("Запазено локално като резервно копие","warning")}catch(Z){console.error("Failed to save emergency backup:",Z)}}},On=async V=>{if(!Ar)return;const Y=P("Зареждане...","info");try{kn();const Z=await window.electronAPI.loadBackup(V);if(!Z.success){if(Z.error==="cancelled"){Y.remove();return}throw new Error(Z.error)}Y.update("Обработка на данните...","info");const se=await pg(Z.dataString);if(!se||!se.data)throw new Error("Невалиден файл");const pe=se.version||"0.0";if(pe!=="1.0"&&!window.confirm(`Файлът е от версия ${pe}. Възможни са проблеми със съвместимостта. Продължаване?`)){Y.remove();return}await Zt(se)}catch(Z){console.error("Load error:",Z),Y.update(`Грешка: ${Z.message}`,"error");const se=Je.load("emergencyBackup",null);se&&window.confirm("Има локално резервно копие. Искате ли да го заредите?")&&(await Zt(se),Y.update("Заредено от резервно копие","warning"))}},Qt=async()=>{if(Ar)try{const V=await window.electronAPI.listBackups();if(!V||V.length===0){P("Няма намерени архивни копия!","warning");return}const Y=V.find(Z=>Z.filename.includes("quicksave"))||V[0];window.confirm(`Сигурни ли сте, че искате да заредите архивно копие от ${new Date(Y.modified).toLocaleString("bg-BG")}? Всички незапазени промени ще бъдат загубени.`)&&await On(Y.filepath)}catch(V){console.error("Failed to load last backup:",V),P(`Грешка при зареждане на архива: ${V.message}`,"error")}};J.useEffect(()=>{if(!Ar)return;const V=async()=>{const Z=localStorage.getItem("lastHistoryArchive"),se=new Date,pe=new Date(se);if(pe.setMonth(pe.getMonth()-3),(!Z||new Date(Z)<pe)&&Qe.length>100){const ue=await window.electronAPI.archiveHistory(Qe);ue.success&&(Kr([]),localStorage.setItem("lastHistoryArchive",se.toISOString()),P(`Историята е автоматично архивирана: ${ue.filename}`,"info"))}};V();const Y=setInterval(V,720*60*60*1e3);return()=>clearInterval(Y)},[Qe,Ar,P]);const Rt=J.useCallback(V=>{const Y=bt(k,Z=>{const se=Z.slice(0,R+1);return se.push(V),se});v(Y),M(Y.length-1)},[k,R]),It=J.useCallback(V=>{const Y=JSON.parse(JSON.stringify(V)),Z={id:Math.random(),timestamp:new Date().toISOString(),...Y};Kr(bt(se=>{se.unshift(Z),se.length>500&&se.splice(500)}))},[]),ga=J.useCallback(()=>{R>0&&(M(R-1),D(k[R-1]))},[k,R]),pa=J.useCallback(()=>{R<k.length-1&&(M(R+1),D(k[R+1]))},[k,R]);J.useEffect(()=>{k.length===0&&(v([JSON.parse(JSON.stringify(Ee))]),M(0))},[Ee,k.length]),Ar||(J.useEffect(()=>{Je.save("cashManagement_dailyData",Ee)},[Ee]),J.useEffect(()=>{Je.save("cashManagement_clients",ke)},[ke]),J.useEffect(()=>{Je.save("cashManagement_employees",Ie)},[Ie]),J.useEffect(()=>{Je.save("cashManagement_holidays",xe)},[xe]),J.useEffect(()=>{Je.save("cashManagement_employeeTransactions",er)},[er]),J.useEffect(()=>{Je.save("cashManagement_transactionGroups",te)},[te]),J.useEffect(()=>{Je.save("cashManagement_departments",Ne)},[Ne]),J.useEffect(()=>{Je.save("cashManagement_turnoverOverrides",De)},[De]),J.useEffect(()=>{Je.save("cashManagement_salaryCashPayments",Ye)},[Ye]),J.useEffect(()=>{Je.save("cashManagement_leaveData",fr)},[fr]),J.useEffect(()=>{Je.save("cashManagement_salaryInputs",rr)},[rr]),J.useEffect(()=>{Je.save("cashManagement_changeHistory",Qe)},[Qe]));const Dn=new Date(r,e+1,0).getDate(),ea=f?["January","February","March","April","May","June","July","August","September","October","November","December"]:["Януари","Февруари","Март","Април","Май","Юни","Юли","Август","Септември","Октомври","Ноември","Декември"],Rn=["Яну","Фев","Мар","Апр","Май","Юни","Юли","Авг","Сеп","Окт","Ное","Дек"],In=new Date().getMonth(),va=new Date().getFullYear(),jn=e===In&&r===va,Pn=V=>{const Z=new Date(r,e,V).getDay();return Z===0||Z===6},ra=J.useCallback((V,Y=e,Z=r)=>{const se=`${String(Y+1).padStart(2,"0")}-${String(V).padStart(2,"0")}`;return(kg[Z]||[]).some(ue=>ue.date===se)||xe.some(ue=>{const[we,Se,be]=ue.date.split("-");return parseInt(be)===V&&parseInt(Se)-1===Y&&parseInt(we)===Z})},[xe,e,r]),y=J.useCallback((V,Y,Z)=>{const se=Ne[V];if(!se)return 22;const pe=new Date(Z,Y+1,0).getDate();let ue=0;for(let we=1;we<=pe;we++){const be=new Date(Z,Y,we).toLocaleDateString("en-US",{weekday:"long"});ra(we,Y,Z)||(se.workDays.includes(be)?ue+=1:se.halfDays.includes(be)&&(ue+=.5))}return ue},[Ne,xe,ra]),N=J.useMemo(()=>{const V=[],Y=new Set;return Object.entries(Ee).forEach(([Z,se])=>{if(!se||!se.income||!se.expense)return;const[pe,ue,we]=Z.split("-").map(Number);se.income.forEach(Se=>{if(Se&&Se.id&&Se.name){const be=`${Z}-income-${Se.id}`;Y.has(be)||(Y.add(be),V.push({...Se,type:"income",year:pe,month:ue,day:we,date:new Date(pe,ue,we),isCarryOver:Se.isCarryOver||!1,uniqueKey:be}))}}),se.expense.forEach(Se=>{if(Se&&Se.id&&Se.name){const be=`${Z}-expense-${Se.id}`;Y.has(be)||(Y.add(be),V.push({...Se,type:"expense",year:pe,month:ue,day:we,date:new Date(pe,ue,we),uniqueKey:be}))}})}),V},[Ee]),T=(V,Y)=>V.reduce((Z,se,pe)=>Z+se*(Y[pe]||0),0),b=(V,Y)=>{const se=(Y||Ee)[V];if(!se)return{bgnBalance:0,eurBalance:0,bgnDenominations:{},eurDenominations:{}};let pe=0,ue=0,we=0,Se=0;const be={};He.forEach((Ae,Fe)=>be[Fe]=0);const Ce={};return sr.forEach((Ae,Fe)=>Ce[Fe]=0),se.income?.forEach(Ae=>{Ae&&(pe+=Ae.bgnTotal||0,ue+=Ae.eurTotal||0,Object.entries(Ae.bgnCounts||{}).forEach(([Fe,Ue])=>{be[Fe]=(be[Fe]||0)+(Ue||0)}),Object.entries(Ae.eurCounts||{}).forEach(([Fe,Ue])=>{Ce[Fe]=(Ce[Fe]||0)+(Ue||0)}))}),se.expense?.forEach(Ae=>{Ae&&(we+=Ae.bgnTotal||0,Se+=Ae.eurTotal||0,Object.entries(Ae.bgnCounts||{}).forEach(([Fe,Ue])=>{be[Fe]=(be[Fe]||0)-(Ue||0)}),Object.entries(Ae.eurCounts||{}).forEach(([Fe,Ue])=>{Ce[Fe]=(Ce[Fe]||0)-(Ue||0)}))}),{bgnBalance:pe-we,eurBalance:ue-Se,bgnDenominations:be,eurDenominations:Ce}},A=J.useCallback(V=>({income:Array(V===1?30:20).fill(null).map(()=>({id:Math.random(),name:"",group:"",bgnCounts:{},eurCounts:{},bgnTotal:0,eurTotal:0,isCarryOver:!1})),expense:Array(15).fill(null).map(()=>({id:Math.random(),name:"",group:"",bgnCounts:{},eurCounts:{},bgnTotal:0,eurTotal:0,isCarryOver:!1}))}),[]);J.useEffect(()=>{q.current||D(V=>{const Z=((pe,ue,we)=>{let Se=new Date(we,ue,pe);Se.setDate(Se.getDate()-1);for(let be=0;be<365;be++){const Ce=`${Se.getFullYear()}-${Se.getMonth()}-${Se.getDate()}`;if(V[Ce]){const Ae=b(Ce,V);if(Ae.bgnBalance!==0||Ae.eurBalance!==0)return{balance:Ae,date:`${Se.getDate()}.${Se.getMonth()+1}`}}Se.setDate(Se.getDate()-1)}return null})(a,e,r),se=`${r}-${e}-${a}`;return bt(V,pe=>{const ue=new Date(r,e,a),we=ue.getDay();(!pe[se]||we===1&&pe[se].income.length<30||we!==1&&pe[se].income.length<20)&&(pe[se]=A(we));const be=pe[se].income;if(Z){const Ae={...be[0],name:`Пари от ${Z.date}`,group:"Пренос",bgnCounts:Z.balance.bgnDenominations||{},eurCounts:Z.balance.eurDenominations||{},bgnTotal:Z.balance.bgnBalance,eurTotal:Z.balance.eurBalance,isCarryOver:!0};be[0]=Ae}else be[0]&&be[0].isCarryOver&&(be[0]={...A(we).income[0],id:be[0].id});const Ce=(Ae,Fe)=>{be[Ae]&&be[Ae].name===""&&(be[Ae].name=Fe)};if(we===1){const Ae=new Date(ue);Ae.setDate(ue.getDate()-3);const Fe=new Date(ue);Fe.setDate(ue.getDate()-2);const Ue=new Date(ue);Ue.setDate(ue.getDate()-1);const ir=hr=>`${hr.getDate().toString().padStart(2,"0")}.${(hr.getMonth()+1).toString().padStart(2,"0")}`;Ce(1,`Сини Камъни ${ir(Ae)}`),Ce(2,`Денонощен ${ir(Ae)}`),Ce(3,`Добрович ${ir(Ae)}`),Ce(4,`Сини Камъни ${ir(Fe)}`),Ce(5,`Денонощен ${ir(Fe)}`),Ce(6,`Добрович ${ir(Fe)}`),Ce(7,`Сини Камъни ${ir(Ue)}`),Ce(8,`Денонощен ${ir(Ue)}`),Ce(9,`Добрович ${ir(Ue)}`),Ce(10,`Хали ${ir(Ae)}`),Ce(11,`Хали ${ir(Fe)}`)}we>=2&&we<=5&&(Ce(1,"Сини Камъни"),Ce(2,"Денонощен"),Ce(3,"Добрович")),we>=3&&we<=5&&Ce(4,"Хали")})})},[a,e,r,B,A]);const O=J.useCallback((V,Y,Z,se)=>{Ze(pe=>{const ue={...pe[V]||{}},we=`${Y}-${Z}`;Object.values(se).some(be=>be>0)?ue[we]=se:delete ue[we];const Se={...pe,[V]:ue};return Object.keys(ue).length===0&&delete Se[V],Se})},[]),X=J.useCallback((V,Y,Z,se)=>{$e(pe=>{const ue=JSON.parse(JSON.stringify(pe));return ue[V]||(ue[V]={}),ue[V][Y]||(ue[V][Y]={}),se>0?ue[V][Y][Z]=se:(ue[V]?.[Y]?.[Z]&&delete ue[V][Y][Z],ue[V]?.[Y]&&Object.keys(ue[V][Y]).length===0&&delete ue[V][Y],ue[V]&&Object.keys(ue[V]).length===0&&delete ue[V]),ue})},[]),ee=V=>{if(q.current)return V();const Y=I.current;if(!Y){V();return}const Z=Y.scrollTop,se=document.activeElement;V(),requestAnimationFrame(()=>{I.current&&!q.current&&(I.current.scrollTop=Z),document.body.contains(se)&&se.focus()})},W=(V,Y)=>{ee(()=>{const Z=`${r}-${e}-${a}`,pe=new Date(r,e,a).getDay(),ue={id:Math.random(),name:"",group:"",bgnCounts:{},eurCounts:{},bgnTotal:0,eurTotal:0,isCarryOver:!1},we=bt(Ee,Se=>{Se[Z]||(Se[Z]=A(pe)),Se[Z][V].splice(Y+1,0,ue)});D(we),Rt(we),It({type:"add",location:{year:r,month:e,day:a,section:V,rowIndex:Y+1,rowId:ue.id},details:{addedData:ue}})})},K=(V,Y)=>{ee(()=>{const Z=`${r}-${e}-${a}`,se=Ee[Z]?.[V]?.[Y];se&&It({type:"delete",location:{year:r,month:e,day:a,section:V,rowIndex:Y,rowId:se.id},details:{deletedData:JSON.parse(JSON.stringify(se))}});const pe=bt(Ee,ue=>{ue[Z]&&ue[Z][V].length>1&&ue[Z][V].splice(Y,1)});D(pe),Rt(pe)})},z=J.useCallback((V,Y,Z)=>{ee(()=>{const se=`${r}-${e}-${a}`,pe=bt(Ee,ue=>{const Se=new Date(r,e,a).getDay();ue[se]||(ue[se]=A(Se));const be=ue[se]?.[V]?.[Y];if(!be)return;const Ce=ue[se][V][Y];Object.entries(Z).forEach(([Ae,Fe])=>{const Ue=be[Ae];JSON.stringify(Ue)!==JSON.stringify(Fe)&&(Ae==="bgnCounts"||Ae==="eurCounts"?(Ae==="bgnCounts"?He:sr).forEach((hr,pr)=>{const Yr=Ue?.[pr]||0,ta=Fe?.[pr]||0;Yr!==ta&&It({type:"edit",location:{year:r,month:e,day:a,section:V,rowIndex:Y,rowId:be.id},details:{field:`${Ae}.${pr}`,fieldName:`${hr} ${Ae==="bgnCounts"?"лв":"€"}`,oldValue:Yr,newValue:ta,updatedTransaction:{...Ce,...Z}}})}):It({type:"edit",location:{year:r,month:e,day:a,section:V,rowIndex:Y,rowId:be.id},details:{field:Ae,fieldName:Ae==="name"?"Име":"Група",oldValue:Ue,newValue:Fe,updatedTransaction:{...Ce,...Z}}}))}),Object.assign(Ce,Z),"bgnCounts"in Z&&(Ce.bgnTotal=T(He,He.map((Ae,Fe)=>Ce.bgnCounts[Fe]||0))),"eurCounts"in Z&&(Ce.eurTotal=T(sr,sr.map((Ae,Fe)=>Ce.eurCounts[Fe]||0)))});D(pe),Rt(pe)})},[Ee,r,e,a,Rt,It]),fe=J.useCallback((V,Y,Z,se)=>{if(Z==="bgnCount"||Z==="eurCount"){const pe=`${r}-${e}-${a}`,ue=(Ee[pe]?.[V]||[])[Y]||{},[we,Se,be]=se,Ce=we==="bgn"?"bgnCounts":"eurCounts",Ae={...ue[Ce],[Se]:parseInt(be)||0};z(V,Y,{[Ce]:Ae})}else z(V,Y,{[Z]:se})},[Ee,r,e,a,z]),ve=J.useCallback((V,Y,Z)=>{const pe=[...ke,...Ie.map(we=>({name:we.name,group:"Заплати"}))].find(we=>we.name.toLowerCase()===Z.toLowerCase()),ue={name:Z};pe&&pe.group&&(ue.group=pe.group),z(V,Y,ue)},[z,ke,Ie]),Te=J.useCallback(V=>{ee(()=>{w(V)})},[]),ge=()=>{const V=[];V.push(["Дата","Тип","Група","Име","BGN","EUR","Месец","Година"]),Object.entries(Ee).forEach(([ue,we])=>{const[Se,be,Ce]=ue.split("-"),Ae=`${Ce}.${parseInt(be)+1}.${Se}`,Fe=Rn[parseInt(be)];we.income?.forEach(Ue=>{Ue.name&&V.push([Ae,"Приход",Ue.group,Ue.name,Ue.bgnTotal,Ue.eurTotal,Fe,Se])}),we.expense?.forEach(Ue=>{Ue.name&&V.push([Ae,"Разход",Ue.group,Ue.name,Ue.bgnTotal,Ue.eurTotal,Fe,Se])})});const Y=V.map(ue=>ue.join(",")).join(`
`),Z=new Blob(["\uFEFF"+Y],{type:"text/csv;charset=utf-8;"}),se=document.createElement("a"),pe=URL.createObjectURL(Z);se.setAttribute("href",pe),se.setAttribute("download",`cash_management_${e+1}_${r}.csv`),se.style.visibility="hidden",document.body.appendChild(se),se.click(),document.body.removeChild(se)},_e=J.useCallback(V=>{let Y=ei(V.name);if(!Y){const ue=prompt(`Не можах да определя месец и година от името на файла.
Моля въведете в формат ММ.ГГ (напр. 04.25 за април 2025):`);if(Y=ei("x"+ue),!Y){alert("Невалиден формат.");return}}const{month:Z,year:se}=Y,pe=new FileReader;pe.onload=ue=>{const we=bn(ue.target.result,{type:"array"}),Se=bt(Ee,be=>{we.SheetNames.forEach(Ce=>{const Ae=Number(Ce);if(!Ae||Ae>31)return;const Fe=we.Sheets[Ce],Ue=xs.sheet_to_json(Fe,{header:1,raw:!1});let ir="income";const hr=[],pr=[];for(let Gr=2;Gr<Ue.length;Gr++){const Fr=Ue[Gr],wa=(Fr[1]||"").toString().trim(),Xs=(Fr[2]||"").toString().trim();if(/^приход/i.test(wa)||/^приход/i.test(Xs)){ir="income";continue}if(/^разход/i.test(wa)||/^разход/i.test(Xs)){ir="expense";continue}if(/^наличност/i.test(wa)||!wa)continue;const Ln=Array(He.length).fill(0),Bn=Array(sr.length).fill(0);Object.entries(Fg).forEach(([Lt,Bt])=>{const pt=Number(Fr[Bt]||0),_a=He.indexOf(Number(Lt));_a>=0&&pt&&(Ln[_a]=pt)}),Object.entries(Ng).forEach(([Lt,Bt])=>{const pt=Number(Fr[Bt]||0),_a=sr.indexOf(Number(Lt));_a>=0&&pt&&(Bn[_a]=pt)});const Xc={...Zn(),name:wa,bgnCounts:Ln,eurCounts:Bn,bgnTotal:He.reduce((Lt,Bt,pt)=>Lt+Bt*Ln[pt],0),eurTotal:sr.reduce((Lt,Bt,pt)=>Lt+Bt*Bn[pt],0)};(ir==="income"?hr:pr).push(Xc)}const Yr=`${se}-${Z}-${Ae}`,ta=A(new Date(se,Z,Ae).getDay());be[Yr]||(be[Yr]=ta);const Jr=be[Yr],qr=1;if(Jr.income.length<qr+hr.length){const Gr=qr+hr.length-Jr.income.length;for(let Fr=0;Fr<Gr;Fr++)Jr.income.push(Zn())}if(hr.forEach((Gr,Fr)=>{Jr.income[qr+Fr]={...Jr.income[qr+Fr],...Gr}}),Jr.expense.length<pr.length){const Gr=pr.length-Jr.expense.length;for(let Fr=0;Fr<Gr;Fr++)Jr.expense.push(Zn())}pr.forEach((Gr,Fr)=>{Jr.expense[Fr]={...Jr.expense[Fr],...Gr}})})});D(Se),alert(`Данните за ${Z+1}.${se} бяха импортирани успешно.`)},pe.readAsArrayBuffer(V)},[Ee,A]),Pe=V=>{const Y=V.target.files[0];if(!Y)return;const Z=Y.name.split(".").pop().toLowerCase();if(Z==="xls"||Z==="xlsx"){_e(Y);return}const se=new FileReader;se.onload=pe=>{const we=pe.target.result.split(`
`),Se=[];for(let be=1;be<we.length;be++){const Ce=we[be].split(",");Ce.length>1&&Se.push({name:Ce[0].trim(),group:""})}confirm(`Искате ли да добавите ${Se.length} имена към списъка с клиенти?`)&&Oe(be=>{const Ce=new Set(be.map(Fe=>Fe.name.toLowerCase())),Ae=Se.filter(Fe=>!Ce.has(Fe.name.toLowerCase()));return[...be,...Ae].sort((Fe,Ue)=>Fe.name.localeCompare(Ue.name))})},se.readAsText(Y)},nr=V=>{const Y=V.target.files[0];if(!Y)return;const Z=new FileReader;Z.onload=se=>{try{const pe=se.target.result,ue=bn(pe,{type:"binary"}),we=ue.SheetNames[0],Se=ue.Sheets[we],be=xs.sheet_to_json(Se,{header:1,range:1}),Ce=[],Ae=new Set;if(be.forEach((Fe,Ue)=>{if(Fe&&Fe.length>0&&Fe[0]){const ir=Fe[0].toString().trim(),hr=Fe[1]?Fe[1].toString().trim():"Администрация",pr=parseFloat(Fe[2])||0,Yr=parseFloat(Fe[3])||0;ir&&(Ce.push({id:Date.now()+Ue,name:ir,department:hr,totalSalary:pr,bankSalary:Yr,socialSecurity:0,annualLeave:0}),Ne[hr]||Ae.add(hr))}}),Ce.length===0){alert("Не са намерени служители за импортиране във файла.");return}if(window.confirm(`Намерени са ${Ce.length} служители. Искате ли да ги добавите? (Съществуващите служители с еднакви имена няма да бъдат променяни.)`)){Ae.size>0&&Be(Ue=>{const ir={...Ue};return Ae.forEach(hr=>{ir[hr]={workDays:["Monday","Tuesday","Wednesday","Thursday","Friday"],halfDays:[]}}),ir});let Fe=0;je(Ue=>{const ir=new Set(Ue.map(pr=>pr.name.toLowerCase())),hr=Ce.filter(pr=>!ir.has(pr.name.toLowerCase()));return Fe=hr.length,hr.length>0?(Oe(pr=>{const Yr=hr.map(qr=>({name:qr.name,group:"Заплати"})),ta=new Set(pr.map(qr=>qr.name.toLowerCase())),Jr=Yr.filter(qr=>!ta.has(qr.name.toLowerCase()));return[...pr,...Jr].sort((qr,Gr)=>qr.name.localeCompare(Gr.name))}),[...Ue,...hr].sort((pr,Yr)=>pr.name.localeCompare(Yr.name))):Ue}),Fe<Ce.length?alert(`${Fe} служители бяха добавени. ${Ce.length-Fe} бяха пропуснати, защото вече съществуват.`):alert(`${Fe} служители бяха добавени успешно.`)}}catch(pe){console.error("Грешка при импортиране на служители:",pe),alert(`Възникна грешка при четене на файла: ${pe.message}`)}finally{V.target.value=null}},Z.readAsBinaryString(Y)},tr=async()=>{const V=mt();if(Ar)zr();else{const Y=JSON.stringify(V.data,null,2),Z=new Blob([Y],{type:"application/json"}),se=URL.createObjectURL(Z),pe=document.createElement("a"),ue=new Date().toISOString().split("T")[0];pe.href=se,pe.download=`cash-management-backup-${ue}.json`,document.body.appendChild(pe),pe.click(),document.body.removeChild(pe),URL.revokeObjectURL(se)}},_r=V=>{const Y=V.target.files[0];if(!Y)return;if(!window.confirm("Сигурни ли сте, че искате да заредите нов файл? Всички текущи данни ще бъдат презаписани.")){V.target.value=null;return}const Z=new FileReader;Z.onload=async se=>{try{const pe=se.target.result,ue=JSON.parse(pe);await Zt(ue.data?ue:{data:ue})}catch(pe){console.error("Грешка при зареждане на данните:",pe),P(`Грешка при зареждане на файла: ${pe.message}`,"error")}finally{V.target.value=null}},Z.readAsText(Y)},Tt=async()=>{const V=P("Обновяване на всички месеци…","info");try{const Y=Object.keys(Ee).sort();let Z=0;const se=bt(Ee,pe=>{Y.forEach(ue=>{const we=pe[ue];if(!we)return;we.income=we.income.filter(be=>!be.isCarry),we.expense=we.expense.filter(be=>!be.isCarry),Z>0?we.income.unshift({id:"carry-in",name:"Налично от вчера",amount:Z,isCarry:!0}):Z<0&&we.expense.unshift({id:"carry-in",name:"Пренесен разход",amount:-Z,isCarry:!0});const Se=be=>be.reduce((Ce,Ae)=>Ce+(+Ae.amount||0),0);Z=Se(we.income)-Se(we.expense)})});D(se),Rt(se),V.update("Готово – всички месеци са пресметнати!","success")}catch(Y){console.error(Y),V.update(`Грешка: ${Y.message}`,"error")}},jt=()=>{Tt()},Pt=(V,Y)=>{V.preventDefault(),m(!0);const Z=V.target.parentElement.closest("th");C.current={startX:V.clientX,columnId:Y,startWidth:Z?Z.offsetWidth:Y==="name"?d:h[Y]||60}};J.useEffect(()=>{const V=Z=>{if(!g||!C.current)return;const{startX:se,startWidth:pe,columnId:ue}=C.current,we=Z.clientX-se,Se=pe+we;ue==="name"?p(Math.max(100,Math.min(600,Se))):_(be=>({...be,[ue]:Math.max(40,Math.min(200,Se))}))},Y=()=>{m(!1),C.current=null};return g&&(document.addEventListener("mousemove",V),document.addEventListener("mouseup",Y)),()=>{document.removeEventListener("mousemove",V),document.removeEventListener("mouseup",Y)}},[g]),J.useEffect(()=>{const V=Y=>{(Y.ctrlKey||Y.metaKey)&&(Y.key==="z"&&!Y.shiftKey?(Y.preventDefault(),ga()):(Y.key==="y"||Y.key==="z"&&Y.shiftKey)&&(Y.preventDefault(),pa())),Y.key==="F5"&&(Y.preventDefault(),Wc())};return window.addEventListener("keydown",V),()=>window.removeEventListener("keydown",V)},[ga,pa]);const[gt,ya]=J.useState({isOpen:!1,employee:null,month:0,year:0}),zr=async()=>{if(Ar){oe(!0);try{const V=mt(),Y=await Q0(V),Z=await window.electronAPI.saveAs(Y);Z.success?P(`Файлът е запазен успешно в: ${Z.path}`,"success"):Z.error&&Z.error!=="cancelled"&&P(`Грешка при запис: ${Z.error}`,"error")}catch(V){console.error("Save error:",V),P(`Грешка при запис: ${V.message}`,"error")}finally{oe(!1)}}else tr()},Wc=async()=>{if(Ar){oe(!0);try{const V=mt(),Y=await Q0(V);P("F5: Отваряне на диалог за запазване и автоматично затваряне...","info");const Z=await window.electronAPI.f5SaveAction(Y);Z.success?P(`F5: Диалогът е отворен и затворен! Запазено като ${Z.filename}`,"success"):P(`F5: Грешка при запис: ${Z.error}`,"error")}catch(V){console.error("F5 Save error:",V),P(`F5: Грешка при запис: ${V.message}`,"error")}finally{oe(!1)}}else tr()};return l.jsxs("div",{className:`min-h-screen ${o?"bg-gray-900 text-white":"bg-gray-100"}`,children:[l.jsx("header",{className:`${o?"bg-gray-800":"bg-white"} shadow-sm border-b ${o?"border-gray-700":"border-gray-200"}`,children:l.jsx("div",{className:"px-4 py-3",children:l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("h1",{className:"text-2xl font-bold",children:"Система за управление на пари в брой"}),l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsxs("div",{className:"flex gap-2",children:[Ar&&l.jsxs(l.Fragment,{children:[l.jsxs("button",{onClick:Cn,className:"bg-green-500 text-white px-3 py-1 rounded hover:bg-green-700 flex items-center",title:"Бързо запазване с автоматично име",children:[l.jsx(Qn,{className:"w-4 h-4 mr-1"}),"Бърз запис"]}),l.jsxs("button",{onClick:Qt,className:"bg-green-500 text-white px-3 py-1 rounded hover:bg-green-700 flex items-center",title:"Зареди последното автоматично запазване",children:[l.jsx(es,{className:"w-4 h-4 mr-1"}),"Зареди Последно"]}),l.jsxs("button",{onClick:zr,className:"bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center",title:"Запази с избор на място и име",children:[l.jsx(Qn,{className:"w-4 h-4 mr-1"}),"Запази като..."]})]}),l.jsxs("label",{className:"bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center cursor-pointer",title:"Зареди всички данни от файл",children:[l.jsx(es,{className:"w-4 h-4 mr-1"}),"Зареди",l.jsx("input",{type:"file",className:"hidden",accept:".json",onChange:_r})]}),i==="daily"&&l.jsxs(l.Fragment,{children:[l.jsxs("button",{onClick:()=>{const V=!he;F(V),H(V?160:320)},className:"bg-yellow-500 text-white px-3 py-1 rounded hover:bg-yellow-600 flex items-center",title:he?"Покажи календар":"Скрий календар",children:[he?l.jsx(qc,{className:"w-4 h-4 mr-1"}):l.jsx(Zc,{className:"w-4 h-4 mr-1"}),he?"Покажи":"Скрий"]}),l.jsxs("button",{onClick:jt,className:"bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center",title:"Обнови данните в текущата таблица",children:[l.jsx(rs,{className:"w-4 h-4 mr-1"}),"Обнови"]})]})]}),l.jsxs("div",{className:"flex gap-2",children:[l.jsx("button",{onClick:ga,disabled:R<=0,className:`px-2 py-1 rounded ${R<=0?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-500 text-white hover:bg-blue-600"}`,title:"Undo (Ctrl+Z)",children:l.jsx(Qc,{className:"w-4 h-4"})}),l.jsx("button",{onClick:pa,disabled:R>=k.length-1,className:`px-2 py-1 rounded ${R>=k.length-1?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-500 text-white hover:bg-blue-600"}`,title:"Redo (Ctrl+Y)",children:l.jsx(el,{className:"w-4 h-4"})})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("button",{onClick:()=>le(-10),className:"bg-gray-500 text-white px-2 py-1 rounded",children:"-"}),l.jsxs("span",{children:[Q[i]||100,"%"]}),l.jsx("button",{onClick:()=>le(10),className:"bg-gray-500 text-white px-2 py-1 rounded",children:"+"})]}),l.jsxs("label",{className:"bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 cursor-pointer flex items-center",children:[l.jsx(ni,{className:"w-4 h-4 mr-1"}),"Импорт",l.jsx("input",{type:"file",accept:".csv,.xlsx,.xls",onChange:Pe,className:"hidden"})]}),l.jsxs("button",{onClick:ge,className:"bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 flex items-center",children:[l.jsx(si,{className:"w-4 h-4 mr-1"}),"Експорт"]}),l.jsx("button",{onClick:()=>u(!o),className:`p-2 rounded ${o?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:o?l.jsx(rl,{className:"w-5 h-5"}):l.jsx(tl,{className:"w-5 h-5"})}),l.jsx("button",{onClick:()=>x(!f),className:`px-3 py-1 rounded ${o?"bg-gray-700 hover:bg-gray-600":"bg-gray-200 hover:bg-gray-300"}`,children:f?"БГ":"EN"})]})]})})}),l.jsx("div",{className:`${o?"bg-gray-800":"bg-white"} border-b ${o?"border-gray-700":"border-gray-200"}`,children:l.jsx("div",{className:"px-4",children:l.jsxs("div",{className:"flex gap-4 overflow-x-auto",children:[l.jsxs("button",{onClick:()=>c("daily"),className:`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${i==="daily"?"border-blue-500 text-blue-600":"border-transparent hover:text-blue-600"}`,children:[l.jsx(al,{className:"w-5 h-5 inline mr-2"}),"Дневно въвеждане"]}),l.jsxs("button",{onClick:()=>c("turnover"),className:`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${i==="turnover"?"border-blue-500 text-blue-600":"border-transparent hover:text-blue-600"}`,children:[l.jsx(nl,{className:"w-5 h-5 inline mr-2"}),"Оборот"]}),l.jsxs("button",{onClick:()=>c("analysis"),className:`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${i==="analysis"?"border-blue-500 text-blue-600":"border-transparent hover:text-blue-600"}`,children:[l.jsx(sl,{className:"w-5 h-5 inline mr-2"}),"Анализ"]}),l.jsxs("button",{onClick:()=>c("salary"),className:`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${i==="salary"?"border-blue-500 text-blue-600":"border-transparent hover:text-blue-600"}`,children:[l.jsx(ms,{className:"w-5 h-5 inline mr-2"}),"Заплати"]}),l.jsxs("button",{onClick:()=>c("history"),className:`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${i==="history"?"border-blue-500 text-blue-600":"border-transparent hover:text-blue-600"}`,children:[l.jsx(rs,{className:"w-5 h-5 inline mr-2"}),"История"]}),l.jsxs("button",{onClick:()=>c("employees"),className:`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${i==="employees"?"border-blue-500 text-blue-600":"border-transparent hover:text-blue-600"}`,children:[l.jsx(ii,{className:"w-5 h-5 inline mr-2"}),"Служители"]}),l.jsxs("button",{onClick:()=>c("lists"),className:`py-3 px-4 border-b-2 transition-colors whitespace-nowrap ${i==="lists"?"border-blue-500 text-blue-600":"border-transparent hover:text-blue-600"}`,children:[l.jsx(ci,{className:"w-5 h-5 inline mr-2"}),"Списъци"]}),i==="daily"&&l.jsxs("div",{className:"ml-auto flex items-center gap-3",children:[l.jsx("button",{onClick:()=>{const V=ai({day:a,month:e,year:r},-1);n(V.year),t(V.month),s(V.day)},className:"px-3 py-1 rounded bg-gray-500 text-white hover:bg-gray-600",title:"Предишен ден",children:"Ден <"}),l.jsx("button",{onClick:()=>{const V=ai({day:a,month:e,year:r},1);n(V.year),t(V.month),s(V.day)},className:"px-3 py-1 rounded bg-gray-500 text-white hover:bg-gray-600",title:"Следващ ден",children:"Ден >"}),l.jsx("div",{className:"flex gap-1 rounded p-0.5",children:["both","bgn","eur"].map(V=>l.jsx("button",{onClick:()=>Te(V),className:`px-3 py-1 rounded transition-colors ${S===V?"bg-blue-500 text-white":o?"bg-gray-600 hover:bg-gray-500":"bg-gray-200 text-black hover:bg-gray-300"}`,children:{both:"И двете",bgn:"Само BGN",eur:"Само EUR"}[V]},V))})]})]})})}),l.jsx("main",{className:"px-4 py-6",children:l.jsxs("div",{style:{zoom:`${Q[i]||100}%`},className:"h-[calc(100vh-150px)]",children:[i==="daily"&&l.jsx(Dg,{isDarkMode:o,currentYear:r,currentMonth:e,selectedDay:a,dailyData:Ee,initializeDayData:A,scrollToRowId:ae,setScrollToRowId:ce,highlightedRowId:de,setHighlightedRowId:ie,hasScrolledRef:q,monthNames:ea,isWeekend:Pn,isHoliday:ra,salaryCashPayments:Ye,calculateTotal:T,viewMode:S,frozenHeaders:j,setFrozenHeaders:E,scrollContainerRef:I,columnWidths:h,handleMouseDown:Pt,nameColumnWidth:d,transactionGroups:te,clientList:ke,employeeList:Ie,handleNameChange:ve,deleteRowAt:K,addRowAt:W,updateTransaction:fe,panelHeight:U,setPanelHeight:H,isFocusMode:he,setIsFocusMode:F,setCurrentMonth:t,setCurrentYear:n,setSelectedDay:s,daysInMonth:Dn,isCurrentMonth:jn}),i==="analysis"&&l.jsx(Rg,{isDarkMode:o,analysisFilters:L,setAnalysisFilters:G,allTransactions:N,currentYear:r,currentMonth:e,dailyData:Ee,transactionGroups:te,setActiveTab:c,setCurrentYear:n,setCurrentMonth:t,setSelectedDay:s,setScrollToRowId:ce,hasScrolledRef:q,salaryCashPayments:Ye,monthNames:ea,calculateTotal:T}),i==="history"&&l.jsx(Hg,{isDarkMode:o,changeHistory:Qe,setChangeHistory:Kr,isElectron:Ar,setActiveTab:c,setCurrentYear:n,setCurrentMonth:t,setSelectedDay:s,setScrollToRowId:ce,hasScrolledRef:q}),i==="employees"&&l.jsx(jg,{isDarkMode:o,departments:Ne,setDepartments:Be,employeeList:Ie,setEmployeeList:je,employeeTransactions:er,setEmployeeTransactions:qe,setClientList:Oe,leaveData:fr,currentYear:r,importEmployeesFromFile:nr}),i==="salary"&&l.jsx(Bg,{isDarkMode:o,currentMonth:e,currentYear:r,monthNames:ea,calculateWorkingDays:y,employeeList:Ie,allTransactions:N,salaryCashPayments:Ye,handleSalaryCashChange:O,leaveData:fr,handleLeaveDataChange:X,calculateTotal:T,popupData:gt,setPopupData:ya,departments:Ne,salaryInputs:rr,setSalaryInputs:Pr}),i==="turnover"&&l.jsx(Mg,{isDarkMode:o,currentYear:r,currentMonth:e,monthNames:ea,dailyData:Ee,turnoverOverrides:De,setTurnoverOverrides:Ge,isHoliday:ra}),i==="lists"&&l.jsx($g,{isDarkMode:o,clientList:ke,setClientList:Oe,customHolidays:xe,setCustomHolidays:Ve,transactionGroups:te,setTransactionGroups:ne,importFromFile:Pe})]})})]})};function Wg(){return l.jsx(Jc,{children:l.jsx(Gg,{})})}xl.createRoot(document.getElementById("root")).render(l.jsx(ds.StrictMode,{children:l.jsx(Wg,{})}));
